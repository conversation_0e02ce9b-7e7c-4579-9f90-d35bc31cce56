# 🛠️ GUIDE DE DÉVELOPPEMENT - FORSA FINANCE

## 🎯 Architecture & Structure

### **Stack Technique**
```
Frontend: Next.js 15 + TypeScript + Tailwind CSS
Base de données: localStorage (simulation SQLite)
Authentification: Système local
État: React Context + Hooks
UI: Shadcn/ui + Lucide Icons
```

### **Structure des Dossiers**
```
forsa finance/finance/
├── src/
│   ├── app/(app)/                    # Pages principales
│   │   ├── dashboard/               # Tableau de bord
│   │   ├── ressources-humaines/     # Module RH
│   │   ├── comptabilite/           # Module comptabilité
│   │   └── ...autres modules
│   ├── components/                  # Composants réutilisables
│   │   ├── ui/                     # Composants UI de base
│   │   ├── rh/                     # Composants RH
│   │   ├── paie/                   # Composants paie
│   │   └── providers/              # Providers React
│   ├── lib/                        # Utilitaires et services
│   │   ├── database.ts             # Base de données locale
│   │   ├── database-service.ts     # Service CRUD
│   │   └── payroll-calculator.ts   # Calculs paie
│   └── data/                       # Données statiques
├── public/                         # Assets statiques
├── DOCUMENTATION_COMPLETE.md       # Documentation complète
├── CHANGELOG.md                    # Historique des versions
└── package.json                   # Dépendances
```

## 🔧 Configuration de Développement

### **Prérequis**
```bash
Node.js >= 18.0.0
npm >= 9.0.0
Git
Éditeur de code (VS Code recommandé)
```

### **Installation**
```bash
cd "forsa finance/finance"
npm install
npm run dev
# Application sur http://localhost:9002
```

### **Scripts Disponibles**
```bash
npm run dev          # Développement
npm run build        # Build production
npm run start        # Serveur production
npm run lint         # Vérification code
npm run typecheck    # Vérification TypeScript
```

## 📊 Base de Données Locale

### **Classe LocalDatabase**
```typescript
class LocalDatabase {
  private prefix = 'forsa_finance_';
  
  // CRUD Operations
  insert(tableName: string, record: DatabaseRecord): DatabaseRecord
  findById(tableName: string, id: string): DatabaseRecord | null
  findAll(tableName: string, filters?: Record<string, any>): DatabaseRecord[]
  update(tableName: string, id: string, updates: Partial<DatabaseRecord>): DatabaseRecord | null
  delete(tableName: string, id: string): boolean
  
  // Utilitaires
  getTable(tableName: string): DatabaseRecord[]
  setTable(tableName: string, records: DatabaseRecord[]): void
  generateId(): string
  clearAllData(): void
}
```

### **Tables Principales**
```typescript
// Tables disponibles
'companies'     // Entreprises
'users'         // Utilisateurs
'employees'     // Employés
'products'      // Produits/Services
'clients'       // Clients
'suppliers'     // Fournisseurs
'invoices'      // Factures
'payrolls'      // Fiches de paie
```

### **Exemple d'Utilisation**
```typescript
import { localDatabase } from '@/lib/database';

// Créer un employé
const newEmployee = localDatabase.insert('employees', {
  first_name: 'Ahmed',
  last_name: 'Benali',
  email: '<EMAIL>',
  position: 'Développeur',
  salary: 80000
});

// Récupérer tous les employés
const employees = localDatabase.findAll('employees');

// Mettre à jour un employé
localDatabase.update('employees', 'emp-001', {
  salary: 85000
});
```

## 🧮 Calculs de Paie Algériens

### **Fonction Principale**
```typescript
export function calculatePayroll(input: PayrollInput): PayrollOutput {
  // 1. Calcul salaire brut
  const grossSalary = calculateGrossSalary(input);
  
  // 2. Calcul cotisations sociales
  const socialContributions = calculateSocialContributions(grossSalary);
  
  // 3. Calcul IRG
  const irg = calculateIRG(grossSalary - socialContributions.employee);
  
  // 4. Calcul salaire net
  const netSalary = grossSalary - socialContributions.employee - irg;
  
  return { grossSalary, socialContributions, irg, netSalary };
}
```

### **Barème IRG 2024**
```typescript
const calculateIRG = (taxableIncome: number): number => {
  if (taxableIncome <= 15000) return 0;                    // 0%
  if (taxableIncome <= 30000) return (taxableIncome - 15000) * 0.20;  // 20%
  if (taxableIncome <= 120000) return 3000 + (taxableIncome - 30000) * 0.30; // 30%
  return 30000 + (taxableIncome - 120000) * 0.35;         // 35%
};
```

### **Cotisations CNAS**
```typescript
const CNAS_RATES = {
  employee: 0.09,    // 9%
  employer: 0.25     // 25%
};
```

## 🎨 Composants UI

### **Structure d'un Composant**
```typescript
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface MonComposantProps {
  data?: any[];
  onAction?: (item: any) => void;
}

export function MonComposant({ data = [], onAction }: MonComposantProps) {
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    // Logique de chargement
    setLoading(false);
  }, []);
  
  if (loading) {
    return <div>Chargement...</div>;
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Mon Composant</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Contenu */}
      </CardContent>
    </Card>
  );
}
```

### **Bonnes Pratiques**
- Toujours typer les props avec TypeScript
- Gérer les états de chargement et d'erreur
- Utiliser les composants Shadcn/ui pour la cohérence
- Vérifier `typeof window !== 'undefined'` pour localStorage

## 🔄 Gestion d'État

### **AuthProvider**
```typescript
// Contexte d'authentification local
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
}
```

### **Utilisation**
```typescript
import { useAuth } from '@/components/providers/auth-provider';

function MonComposant() {
  const { user, company, loading } = useAuth();
  
  if (loading) return <div>Chargement...</div>;
  
  return <div>Bonjour {user?.first_name}</div>;
}
```

## 🧪 Tests et Débogage

### **Console Navigateur**
```javascript
// Vider toutes les données
localStorage.clear();

// Voir les données d'une table
JSON.parse(localStorage.getItem('forsa_finance_employees') || '[]');

// Ajouter un employé de test
const db = window.localDatabase;
db.insert('employees', {
  first_name: 'Test',
  last_name: 'User',
  email: '<EMAIL>',
  position: 'Testeur',
  salary: 50000
});
```

### **Débogage Courant**
```typescript
// Vérification localStorage
if (typeof window !== 'undefined') {
  console.log('Données employés:', localStorage.getItem('forsa_finance_employees'));
}

// Gestion d'erreurs
try {
  const result = localDatabase.findAll('employees');
  console.log('Employés trouvés:', result);
} catch (error) {
  console.error('Erreur:', error);
}
```

## 🚀 Déploiement

### **Build Production**
```bash
npm run build
npm run start
```

### **Variables d'Environnement**
```env
# .env.local
NEXT_PUBLIC_APP_NAME="Forsa Finance"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NODE_ENV=production
```

### **Optimisations**
- Images optimisées avec Next.js Image
- Code splitting automatique
- Compression gzip
- Cache navigateur optimisé

## 🔧 Ajout de Nouvelles Fonctionnalités

### **1. Nouveau Module**
```typescript
// 1. Créer la page
// src/app/(app)/nouveau-module/page.tsx

// 2. Créer les composants
// src/components/nouveau-module/

// 3. Ajouter au menu
// src/components/layout/sidebar.tsx

// 4. Créer les types
// src/types/nouveau-module.ts
```

### **2. Nouvelle Table**
```typescript
// Dans database.ts
private initializeNouvelleTable(): void {
  const data = this.getTable('nouvelle_table');
  if (data.length === 0) {
    this.insert('nouvelle_table', {
      // Données par défaut
    });
  }
}
```

### **3. Nouveau Calcul**
```typescript
// Dans lib/calculations/
export function nouveauCalcul(input: InputType): OutputType {
  // Logique de calcul
  return result;
}
```

## 📋 Checklist Développement

### **Avant Commit**
- [ ] Code TypeScript sans erreurs
- [ ] Tests fonctionnels passés
- [ ] Documentation mise à jour
- [ ] Performance vérifiée
- [ ] Responsive design testé

### **Avant Release**
- [ ] Build production réussi
- [ ] Tests sur différents navigateurs
- [ ] Documentation utilisateur
- [ ] Changelog mis à jour
- [ ] Sauvegarde des données

## 🆘 Résolution de Problèmes

### **Erreurs Fréquentes**
```typescript
// 1. localStorage undefined
if (typeof window !== 'undefined') {
  // Code utilisant localStorage
}

// 2. Données corrompues
try {
  JSON.parse(localStorage.getItem('key') || '[]');
} catch {
  localStorage.removeItem('key');
}

// 3. État non synchronisé
useEffect(() => {
  // Recharger les données
}, [dependency]);
```

### **Performance**
- Utiliser `useMemo` pour calculs coûteux
- `useCallback` pour fonctions dans deps
- Lazy loading pour gros composants
- Pagination pour grandes listes

---

## 🎯 Prochaines Étapes

1. **Tests automatisés** avec Jest/Testing Library
2. **Storybook** pour documentation composants
3. **ESLint/Prettier** pour qualité code
4. **Husky** pour hooks Git
5. **CI/CD** avec GitHub Actions

**Le code est maintenant prêt pour les développements futurs !** 🚀
