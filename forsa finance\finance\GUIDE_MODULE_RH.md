# 📋 Guide Complet du Module Ressources Humaines - Forsa Finance

## 🎯 Vue d'Ensemble

Le module Ressources Humaines de Forsa Finance est un système complet de gestion du personnel conçu spécifiquement pour les entreprises algériennes. Il respecte la réglementation locale et offre toutes les fonctionnalités nécessaires pour une gestion RH professionnelle.

## 🚀 Fonctionnalités Principales

### 1. 📊 Tableau de Bord RH
- **Vue d'ensemble** : Statistiques en temps réel
- **Indicateurs clés** : Employés actifs, présence, masse salariale
- **Alertes** : Notifications pour actions requises
- **Actions rapides** : Raccourcis vers fonctions principales

### 2. 👥 Gestion des Employés
- **CRUD complet** : <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modifier, Supprimer
- **Informations complètes** : Données personnelles et professionnelles
- **N° Sécurité Sociale** : Champ obligatoire conforme
- **Départements** : Organisation par services
- **Statuts** : Actif/Inactif avec gestion

### 3. 🕐 Pointage & Heures
- **Pointage quotidien** : Enregistrement arrivée/départ
- **Heures supplémentaires** : Calcul et approbation automatiques
- **Rapports détaillés** : Par employé et département
- **Feuilles d'émargement** : Génération et impression

### 4. 💰 Calcul de Paie
- **Barème IRG 2025** : Conforme à la réglementation algérienne
- **Calculs automatiques** : Retenues et charges sociales
- **Impression A4** : Fiche de paie professionnelle
- **QR Code** : Vérification et authenticité

### 5. 📈 Relevé des Émoluments
- **Rapports annuels** : Vue d'ensemble des salaires
- **Détails mensuels** : Par employé et période
- **Exports** : Documents officiels imprimables

## 🛠️ Guide d'Utilisation

### Démarrage Rapide

1. **Accès au module** : Naviguer vers `/ressources-humaines`
2. **Tableau de bord** : Vue d'ensemble automatique
3. **Navigation** : 5 onglets principaux disponibles

### Gestion des Employés

#### Ajouter un Employé
1. Onglet "Employés" → Bouton "Ajouter un employé"
2. Remplir le formulaire complet :
   - Nom complet
   - Email et téléphone
   - Poste et département
   - Salaire et date d'embauche
   - **N° Sécurité Sociale** (15 chiffres)
3. Enregistrer → Notification de succès

#### Modifier un Employé
1. Liste des employés → Menu "⋯" → "Modifier"
2. Formulaire pré-rempli avec données existantes
3. Modifier les champs nécessaires
4. Sauvegarder les modifications

#### Supprimer un Employé
1. Liste des employés → Menu "⋯" → "Supprimer"
2. Confirmation de suppression
3. Suppression définitive avec notification

### Pointage et Heures

#### Pointage Quotidien
1. Onglet "Pointage & Heures" → "Pointage Quotidien"
2. Sélectionner employé
3. Saisir heures d'arrivée et départ
4. Définir temps de pause (défaut: 60 min)
5. Choisir statut (Présent/Retard/Demi-journée/Absent)
6. Ajouter notes si nécessaire
7. Enregistrer → Calcul automatique des heures

#### Heures Supplémentaires
1. Onglet "Heures Supplémentaires"
2. **Automatique** : Générées si > 8h/jour
3. **Manuel** : Formulaire d'ajout dédié
4. **Approbation** : Workflow de validation
5. **Statistiques** : Coût estimé et totaux

#### Feuilles d'Émargement
1. Onglet "Émargement"
2. **Quotidienne** : Choisir date → Générer
3. **Mensuelle** : Choisir mois → Générer
4. **Impression** : Nouvelle fenêtre optimisée A4

### Calcul de Paie

#### Générer une Fiche de Paie
1. Depuis liste employés → "Générer fiche de paie"
2. Ou onglet "Fiche de paie" → Sélectionner employé
3. Remplir informations période :
   - Salaire de base
   - Jours travaillés
   - Indemnités (transport, repas, etc.)
   - Primes et avantages
4. **Calcul automatique** :
   - Salaire brut
   - Retenues CNAS (9%)
   - IRG selon barème 2025
   - Charges patronales
   - Salaire net
5. **Impression** : Fiche A4 professionnelle

#### Barème IRG 2025 (Automatique)
- **0 - 240 000 DZD/an** : 0%
- **240 001 - 480 000 DZD/an** : 23%
- **480 001 - 960 000 DZD/an** : 27%
- **960 001 - 1 920 000 DZD/an** : 30%
- **Au-dessus de 1 920 000 DZD/an** : 35%

### Rapports et Analyses

#### Relevé des Émoluments
1. Onglet "Relevé des Émoluments"
2. **Vue générale** : Tous employés
3. **Détail individuel** : Clic sur employé
4. **Impression** : Documents officiels

#### Rapports de Pointage
1. Onglet "Pointage & Heures" → "Rapports"
2. **Par employé** : Heures mensuelles
3. **Par département** : Répartition
4. **Exports** : Données téléchargeables

## 📋 Barème IRG 2025 - Détail Technique

### Calcul Progressif
```
Exemple : Salaire 80 000 DZD/mois (960 000 DZD/an)

1. Salaire annuel brut : 960 000 DZD
2. Retenue CNAS (9%) : 86 400 DZD
3. Abattement forfaitaire : 12 000 DZD
4. Revenu imposable : 861 600 DZD

Application du barème :
- Tranche 1 (0-240 000) : 240 000 × 0% = 0 DZD
- Tranche 2 (240 001-480 000) : 240 000 × 23% = 55 200 DZD
- Tranche 3 (480 001-861 600) : 381 600 × 27% = 103 032 DZD

IRG total annuel : 158 232 DZD
IRG mensuel : 13 186 DZD
Salaire net mensuel : 59 614 DZD
```

## 🖨️ Documents Générés

### Fiche de Paie
- **Format** : A4 portrait
- **Contenu** : Informations complètes conformes
- **QR Code** : Vérification unique
- **Signature** : Espaces dédiés

### Feuilles d'Émargement
- **Quotidienne** : A4 portrait avec colonnes détaillées
- **Mensuelle** : A4 paysage avec grille complète
- **Professionnelles** : En-têtes et signatures

### Relevés Officiels
- **Émoluments annuels** : Récapitulatif complet
- **Détails mensuels** : Par employé
- **Conformes** : Réglementation algérienne

## 💾 Persistance des Données

### Base de Données Locale
- **localStorage** : Sauvegarde automatique
- **Tables** : employees, timeEntries, overtimeEntries
- **Synchronisation** : Temps réel
- **Sauvegarde** : Automatique à chaque action

### Données Par Défaut
- **5 employés** : Exemples complets
- **Départements** : IT, Finance, Ventes, Administration, Maintenance
- **N° Sécurité Sociale** : Pré-remplis pour tests

## 🔧 Configuration Technique

### Prérequis
- Node.js 18+
- Next.js 15.3.3
- React 18+
- TypeScript

### Installation
```bash
cd "forsa finance/finance"
npm install
npm run dev
```

### Accès
- URL : http://localhost:9002/ressources-humaines
- Port : 9002 (configurable)

## 🎯 Prochaines Étapes

Le module RH est maintenant **100% complet et opérationnel**. Vous pouvez passer aux autres modules :

1. **Module Comptabilité** : Gestion des comptes et écritures
2. **Module Commercial** : Devis, factures, clients
3. **Module Inventaire** : Gestion des stocks
4. **Module Reporting** : Tableaux de bord avancés

## 📞 Support

Pour toute question ou amélioration :
- Documentation complète intégrée
- Interface intuitive et guidée
- Notifications d'aide contextuelles

---

**Le module Ressources Humaines de Forsa Finance est prêt pour une utilisation professionnelle en entreprise algérienne !** 🚀
