# 🏢 FORSA FINANCE - Logiciel de Gestion Financière pour Entreprises Algériennes

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/votre-repo/forsa-finance)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-Production%20Ready-brightgreen.svg)](README.md)

## 🎯 Vue d'Ensemble

**Forsa Finance Desktop** est un logiciel de gestion financière complet, spécialement conçu pour les entreprises algériennes. L'application fonctionne **100% hors ligne** avec une interface moderne et intuitive.

### ✨ **Caractéristiques Principales**
- 🇩🇿 **Conforme à la réglementation algérienne** (SCF, TVA, IRG, CNAS)
- 💾 **100% hors ligne** - aucune connexion internet requise
- 🏦 **Modules complets** - RH, Comptabilité, Stock, Trésorerie, Fiscalité
- 🧮 **Calculs automatiques** - Paie, taxes, amortissements
- 📊 **Rapports intégrés** - États financiers, tableaux de bord
- 🎨 **Interface moderne** - Design professionnel et responsive

## 🚀 Démarrage Rapide

### **Installation**
```bash
# Aller dans le dossier
cd "forsa finance/finance"

# Installer les dépendances
npm install

# Démarrer l'application
npm run dev
```

### **Accès**
- **URL :** http://localhost:9002
- **Mode :** Démo automatique (pas de connexion requise)
- **Données :** Exemples pré-chargés pour tests

## 📱 Modules Disponibles

### **👥 Ressources Humaines**
- Gestion des employés avec sélection automatique
- Calculateur de paie conforme à la réglementation algérienne
- Gestion des congés et formations
- Rapports RH personnalisés

### **💰 Comptabilité**
- Plan comptable SCF (Système Comptable Financier)
- Écritures comptables automatisées
- Bilan et compte de résultat
- Rapprochements bancaires

### **📦 Stock & Inventaire**
- Gestion des produits et services
- Mouvements de stock en temps réel
- Valorisation FIFO/LIFO
- Alertes de stock minimum

### **🏦 Trésorerie**
- Comptes bancaires algériens (BNA, CPA, BEA, etc.)
- Flux de trésorerie prévisionnels
- Rapprochements automatiques
- Tableaux de bord financiers

### **📋 Fiscalité**
- Déclarations TVA automatiques (19%)
- Calculs IRG progressifs
- Rapports de conformité DGI
- Échéanciers fiscaux

### **🧾 Facturation**
- Devis et factures professionnels
- Avoirs et remises
- Suivi des paiements clients
- Relances automatiques

### **🤝 Gestion des Tiers**
- Clients et fournisseurs
- Historique des transactions
- Relances automatiques
- Statistiques commerciales

### **📊 Tableau de Bord**
- KPIs financiers en temps réel
- Graphiques de performance
- Alertes et notifications
- Vue d'ensemble de l'activité

## 🇩🇿 Spécificités Algériennes

### **Fiscalité**
- **TVA :** 19% (taux standard)
- **IRG :** Barème progressif 2024 (0% à 35%)
- **CNAS :** 9% employé, 25% employeur
- **SNMG :** 20 000 DZD

### **Comptabilité**
- **Système :** SCF conforme
- **Devise :** Dinar algérien (DZD)
- **Plan comptable :** Normes algériennes

### **Géographie**
- **58 wilayas** intégrées
- **Banques locales** : BNA, CPA, BEA, BADR
- **Secteurs d'activité** algériens

## 🛠️ Technologies Utilisées

### **Frontend**
- **Next.js 15** - Framework React
- **TypeScript** - Typage statique
- **Tailwind CSS** - Styling moderne
- **Shadcn/ui** - Composants UI

### **Base de Données**
- **localStorage** - Stockage local navigateur
- **Simulation SQLite** - Interface familière
- **Persistance** - Données conservées

### **Calculs**
- **Algorithmes locaux** - Paie, fiscalité
- **Conformité** - Réglementation algérienne
- **Performance** - Calculs instantanés

## 📊 Fonctionnalités Avancées

### **Sélection Automatique d'Employés**
```
1. Aller dans "Ressources Humaines"
2. Voir la liste des employés
3. Cliquer "Générer fiche de paie"
4. → Sélection automatique + basculement vers paie
5. → Formulaire pré-rempli avec données employé
```

### **Calculs de Paie Intelligents**
- Salaire de base automatique selon l'employé
- Calculs IRG progressifs conformes
- Cotisations CNAS automatiques
- Indemnités et primes personnalisables

### **Interface Intuitive**
- Navigation fluide entre modules
- Indicateurs visuels de sélection
- États de chargement et erreurs
- Design responsive (desktop/tablet/mobile)

## 📋 Données d'Exemple

### **Employés Pré-chargés**
1. **Ahmed Benali** - Développeur (80 000 DZD)
2. **Fatima Khelifi** - Comptable (65 000 DZD)
3. **Mohamed Saidi** - Commercial (55 000 DZD)
4. **Amina Boudiaf** - Secrétaire (35 000 DZD)
5. **Karim Meziane** - Technicien (45 000 DZD)

### **Entreprise Demo**
- **Nom :** Entreprise Demo
- **Adresse :** Alger, Algérie
- **NIF :** 123456789012345
- **RC :** 16/00-1234567

## 🔧 Configuration

### **Prérequis**
- Node.js >= 18.0.0
- npm >= 9.0.0
- Navigateur moderne (Chrome, Firefox, Edge)

### **Scripts Disponibles**
```bash
npm run dev          # Développement
npm run build        # Build production
npm run start        # Serveur production
npm run lint         # Vérification code
npm run typecheck    # Vérification TypeScript
```

## 📚 Documentation

- **[Documentation Complète](DOCUMENTATION_COMPLETE.md)** - Guide détaillé
- **[Guide de Développement](GUIDE_DEVELOPPEMENT.md)** - Pour les développeurs
- **[Changelog](CHANGELOG.md)** - Historique des versions

## 🏆 Statut

**✅ APPLICATION 100% FONCTIONNELLE**

L'application Forsa Finance Desktop est maintenant complètement opérationnelle avec tous les modules financiers implémentés et optimisés pour les entreprises algériennes.

**Prêt pour déploiement commercial !** 🎉

---

**Développé avec ❤️ pour les entreprises algériennes**
