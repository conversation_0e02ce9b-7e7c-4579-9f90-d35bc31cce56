-- Seed data for Forsa Finance
-- Plan comptable SCF (Système Comptable Financier) - Algérie

-- Function to create default chart of accounts for a company
CREATE OR REPLACE FUNCTION create_default_chart_of_accounts(company_uuid UUID)
RETURNS VOID AS $$
BEGIN
  -- CLASSE 1: COMPTES DE CAPITAUX
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('10', 'Capital', 'equity', company_uuid),
  ('101', 'Capital émis', 'equity', company_uuid),
  ('1011', 'Capital souscrit - non appelé', 'equity', company_uuid),
  ('1012', 'Capital souscrit - appelé, non versé', 'equity', company_uuid),
  ('1013', 'Capital souscrit appelé, versé', 'equity', company_uuid),
  ('106', 'Réserves', 'equity', company_uuid),
  ('1061', 'Réserve légale', 'equity', company_uuid),
  ('1063', 'Réserves statutaires ou contractuelles', 'equity', company_uuid),
  ('1068', 'Autres réserves', 'equity', company_uuid),
  ('11', 'Report à nouveau', 'equity', company_uuid),
  ('110', 'Report à nouveau (solde créditeur)', 'equity', company_uuid),
  ('119', 'Report à nouveau (solde débiteur)', 'equity', company_uuid),
  ('12', 'Résultat de l''exercice', 'equity', company_uuid),
  ('120', 'Résultat de l''exercice (bénéfice)', 'equity', company_uuid),
  ('129', 'Résultat de l''exercice (perte)', 'equity', company_uuid),
  ('13', 'Produits des activités ordinaires constatés d''avance', 'liability', company_uuid),
  ('15', 'Provisions pour charges - passifs non courants', 'liability', company_uuid),
  ('16', 'Emprunts et dettes assimilées', 'liability', company_uuid),
  ('161', 'Emprunts obligataires', 'liability', company_uuid),
  ('162', 'Emprunts auprès des établissements de crédit', 'liability', company_uuid),
  ('164', 'Emprunts auprès des associés', 'liability', company_uuid),
  ('165', 'Dépôts et cautionnements reçus', 'liability', company_uuid),
  ('168', 'Autres emprunts et dettes assimilées', 'liability', company_uuid),
  ('17', 'Dettes rattachées à des participations', 'liability', company_uuid),
  ('18', 'Comptes de liaison des établissements et succursales', 'liability', company_uuid),
  ('19', 'Perte de valeur sur immobilisations non courantes', 'asset', company_uuid);

  -- CLASSE 2: COMPTES D'IMMOBILISATIONS
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('20', 'Immobilisations incorporelles', 'asset', company_uuid),
  ('201', 'Frais de développement', 'asset', company_uuid),
  ('203', 'Logiciels', 'asset', company_uuid),
  ('205', 'Concessions et droits similaires, brevets, licences, marques', 'asset', company_uuid),
  ('207', 'Fonds commercial', 'asset', company_uuid),
  ('208', 'Autres immobilisations incorporelles', 'asset', company_uuid),
  ('21', 'Immobilisations corporelles', 'asset', company_uuid),
  ('211', 'Terrains', 'asset', company_uuid),
  ('212', 'Agencements et aménagements de terrains', 'asset', company_uuid),
  ('213', 'Constructions', 'asset', company_uuid),
  ('215', 'Installations techniques, matériel et outillage industriels', 'asset', company_uuid),
  ('218', 'Autres immobilisations corporelles', 'asset', company_uuid),
  ('2181', 'Installations générales, agencements, aménagements divers', 'asset', company_uuid),
  ('2182', 'Matériel de transport', 'asset', company_uuid),
  ('2183', 'Matériel de bureau et matériel informatique', 'asset', company_uuid),
  ('2184', 'Mobilier', 'asset', company_uuid),
  ('2188', 'Autres immobilisations corporelles', 'asset', company_uuid),
  ('22', 'Immobilisations mises en concession', 'asset', company_uuid),
  ('23', 'Immobilisations en cours', 'asset', company_uuid),
  ('238', 'Avances et acomptes versés sur commandes d''immobilisations', 'asset', company_uuid),
  ('26', 'Participations et créances rattachées à des participations', 'asset', company_uuid),
  ('261', 'Titres de participation', 'asset', company_uuid),
  ('27', 'Autres immobilisations financières', 'asset', company_uuid),
  ('271', 'Titres immobilisés autres que les titres immobilisés de l''activité de portefeuille', 'asset', company_uuid),
  ('272', 'Titres immobilisés de l''activité de portefeuille', 'asset', company_uuid),
  ('273', 'Prêts', 'asset', company_uuid),
  ('274', 'Prêts au personnel', 'asset', company_uuid),
  ('275', 'Dépôts et cautionnements versés', 'asset', company_uuid),
  ('276', 'Autres créances immobilisées', 'asset', company_uuid),
  ('28', 'Amortissements des immobilisations', 'asset', company_uuid),
  ('280', 'Amortissements des immobilisations incorporelles', 'asset', company_uuid),
  ('281', 'Amortissements des immobilisations corporelles', 'asset', company_uuid),
  ('29', 'Pertes de valeur sur immobilisations', 'asset', company_uuid);

  -- CLASSE 3: COMPTES DE STOCKS ET EN-COURS
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('30', 'Stocks de marchandises', 'asset', company_uuid),
  ('31', 'Matières premières et fournitures', 'asset', company_uuid),
  ('32', 'Autres approvisionnements', 'asset', company_uuid),
  ('33', 'En-cours de production de biens', 'asset', company_uuid),
  ('34', 'En-cours de production de services', 'asset', company_uuid),
  ('35', 'Stocks de produits', 'asset', company_uuid),
  ('37', 'Stocks de déchets et rebuts', 'asset', company_uuid),
  ('38', 'Achats stockés', 'asset', company_uuid),
  ('39', 'Pertes de valeur sur stocks et en-cours', 'asset', company_uuid);

  -- CLASSE 4: COMPTES DE TIERS
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('40', 'Fournisseurs et comptes rattachés', 'liability', company_uuid),
  ('401', 'Fournisseurs', 'liability', company_uuid),
  ('403', 'Fournisseurs - Effets à payer', 'liability', company_uuid),
  ('404', 'Fournisseurs d''immobilisations', 'liability', company_uuid),
  ('405', 'Fournisseurs d''immobilisations - Effets à payer', 'liability', company_uuid),
  ('408', 'Fournisseurs - Factures non parvenues', 'liability', company_uuid),
  ('409', 'Fournisseurs débiteurs - Avances et acomptes versés', 'asset', company_uuid),
  ('41', 'Clients et comptes rattachés', 'asset', company_uuid),
  ('411', 'Clients', 'asset', company_uuid),
  ('413', 'Clients - Effets à recevoir', 'asset', company_uuid),
  ('416', 'Clients douteux ou litigieux', 'asset', company_uuid),
  ('418', 'Clients - Produits non encore facturés', 'asset', company_uuid),
  ('419', 'Clients créditeurs - Avances et acomptes reçus', 'liability', company_uuid),
  ('42', 'Personnel et comptes rattachés', 'liability', company_uuid),
  ('421', 'Personnel - Rémunérations dues', 'liability', company_uuid),
  ('422', 'Comités d''entreprises, d''établissement, ...', 'liability', company_uuid),
  ('424', 'Participation des salariés aux résultats', 'liability', company_uuid),
  ('425', 'Personnel - Avances et acomptes', 'asset', company_uuid),
  ('426', 'Personnel - Dépôts', 'liability', company_uuid),
  ('427', 'Personnel - Oppositions', 'liability', company_uuid),
  ('428', 'Personnel - Charges à payer et produits à recevoir', 'liability', company_uuid),
  ('43', 'Organismes sociaux et comptes rattachés', 'liability', company_uuid),
  ('431', 'Sécurité sociale', 'liability', company_uuid),
  ('437', 'Autres organismes sociaux', 'liability', company_uuid),
  ('44', 'État, collectivités publiques, organismes internationaux', 'liability', company_uuid),
  ('441', 'État - Subventions à recevoir', 'asset', company_uuid),
  ('442', 'État - Impôts et taxes recouvrables sur des tiers', 'asset', company_uuid),
  ('443', 'Opérations particulières avec l''État', 'liability', company_uuid),
  ('444', 'État - Impôts sur les bénéfices', 'liability', company_uuid),
  ('445', 'État - Taxes sur le chiffre d''affaires', 'liability', company_uuid),
  ('4452', 'État - TVA due intracommunautaire', 'liability', company_uuid),
  ('4456', 'État - TVA déductible', 'asset', company_uuid),
  ('4457', 'État - TVA collectée', 'liability', company_uuid),
  ('447', 'Autres impôts, taxes et versements assimilés', 'liability', company_uuid),
  ('448', 'État - Charges à payer et produits à recevoir', 'liability', company_uuid),
  ('45', 'Groupe et associés', 'liability', company_uuid),
  ('455', 'Associés - Comptes courants', 'liability', company_uuid),
  ('456', 'Associés - Opérations sur le capital', 'liability', company_uuid),
  ('46', 'Débiteurs divers et créditeurs divers', 'asset', company_uuid),
  ('462', 'Créances sur cessions d''immobilisations', 'asset', company_uuid),
  ('464', 'Dettes sur acquisitions de valeurs mobilières de placement', 'liability', company_uuid),
  ('467', 'Autres comptes débiteurs ou créditeurs', 'asset', company_uuid),
  ('47', 'Comptes transitoires ou d''attente', 'asset', company_uuid),
  ('471', 'Comptes d''attente', 'asset', company_uuid),
  ('472', 'Comptes de répartition périodique des charges', 'asset', company_uuid),
  ('473', 'Comptes de répartition périodique des produits', 'liability', company_uuid),
  ('48', 'Comptes de régularisation', 'asset', company_uuid),
  ('481', 'Charges à répartir sur plusieurs exercices', 'asset', company_uuid),
  ('486', 'Charges constatées d''avance', 'asset', company_uuid),
  ('487', 'Produits constatés d''avance', 'liability', company_uuid),
  ('488', 'Comptes de répartition de charges à payer', 'liability', company_uuid),
  ('489', 'Comptes de répartition de produits à recevoir', 'asset', company_uuid),
  ('49', 'Pertes de valeur sur comptes de tiers', 'asset', company_uuid);

  -- CLASSE 5: COMPTES FINANCIERS
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('50', 'Valeurs mobilières de placement', 'asset', company_uuid),
  ('503', 'Actions', 'asset', company_uuid),
  ('506', 'Obligations', 'asset', company_uuid),
  ('508', 'Autres valeurs mobilières de placement et créances assimilées', 'asset', company_uuid),
  ('51', 'Banques, établissements financiers et assimilés', 'asset', company_uuid),
  ('511', 'Valeurs à l''encaissement', 'asset', company_uuid),
  ('512', 'Banques', 'asset', company_uuid),
  ('514', 'Chèques postaux', 'asset', company_uuid),
  ('515', 'Caisses du Trésor et des établissements publics', 'asset', company_uuid),
  ('516', 'Autres organismes financiers', 'asset', company_uuid),
  ('517', 'Instruments de trésorerie', 'asset', company_uuid),
  ('518', 'Intérêts courus', 'asset', company_uuid),
  ('519', 'Concours bancaires courants', 'liability', company_uuid),
  ('52', 'Instruments de trésorerie', 'asset', company_uuid),
  ('53', 'Caisse', 'asset', company_uuid),
  ('531', 'Caisse siège social', 'asset', company_uuid),
  ('532', 'Caisse succursale (ou usine) A', 'asset', company_uuid),
  ('54', 'Régies d''avance et accréditifs', 'asset', company_uuid),
  ('58', 'Virements internes', 'asset', company_uuid),
  ('59', 'Pertes de valeur sur actifs financiers courants', 'asset', company_uuid);

  -- CLASSE 6: COMPTES DE CHARGES
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('60', 'Achats consommés', 'expense', company_uuid),
  ('601', 'Achats de marchandises vendues', 'expense', company_uuid),
  ('602', 'Achats de matières premières et fournitures', 'expense', company_uuid),
  ('604', 'Achats d''études et prestations de services', 'expense', company_uuid),
  ('605', 'Achats de matériel, équipements et travaux', 'expense', company_uuid),
  ('607', 'Achats de marchandises, matières premières et fournitures', 'expense', company_uuid),
  ('608', 'Frais accessoires d''achats', 'expense', company_uuid),
  ('609', 'Rabais, remises et ristournes obtenus sur achats', 'expense', company_uuid),
  ('61', 'Services extérieurs', 'expense', company_uuid),
  ('611', 'Sous-traitance générale', 'expense', company_uuid),
  ('613', 'Locations', 'expense', company_uuid),
  ('614', 'Charges locatives et de copropriété', 'expense', company_uuid),
  ('615', 'Entretien et réparations', 'expense', company_uuid),
  ('616', 'Primes d''assurances', 'expense', company_uuid),
  ('617', 'Études et recherches', 'expense', company_uuid),
  ('618', 'Divers', 'expense', company_uuid),
  ('619', 'Rabais, remises et ristournes obtenus sur services extérieurs', 'expense', company_uuid),
  ('62', 'Autres services extérieurs', 'expense', company_uuid),
  ('621', 'Personnel extérieur à l''entreprise', 'expense', company_uuid),
  ('622', 'Rémunérations d''intermédiaires et honoraires', 'expense', company_uuid),
  ('623', 'Publicité, publications, relations publiques', 'expense', company_uuid),
  ('624', 'Transports de biens et transports collectifs du personnel', 'expense', company_uuid),
  ('625', 'Déplacements, missions et réceptions', 'expense', company_uuid),
  ('626', 'Frais postaux et frais de télécommunications', 'expense', company_uuid),
  ('627', 'Services bancaires et assimilés', 'expense', company_uuid),
  ('628', 'Divers', 'expense', company_uuid),
  ('629', 'Rabais, remises et ristournes obtenus sur autres services extérieurs', 'expense', company_uuid),
  ('63', 'Charges de personnel', 'expense', company_uuid),
  ('631', 'Salaires et appointements', 'expense', company_uuid),
  ('633', 'Indemnités et avantages divers', 'expense', company_uuid),
  ('635', 'Charges de sécurité sociale et de prévoyance', 'expense', company_uuid),
  ('637', 'Autres charges sociales', 'expense', company_uuid),
  ('64', 'Impôts, taxes et versements assimilés', 'expense', company_uuid),
  ('641', 'Impôts, taxes et versements assimilés sur rémunérations', 'expense', company_uuid),
  ('645', 'Autres impôts, taxes et versements assimilés', 'expense', company_uuid),
  ('65', 'Autres charges opérationnelles', 'expense', company_uuid),
  ('651', 'Redevances pour concessions, brevets, licences, marques, procédés, logiciels, droits et valeurs similaires', 'expense', company_uuid),
  ('653', 'Jetons de présence', 'expense', company_uuid),
  ('654', 'Pertes sur créances irrécouvrables', 'expense', company_uuid),
  ('655', 'Quote-part de résultat sur opérations faites en commun', 'expense', company_uuid),
  ('658', 'Charges diverses de gestion courante', 'expense', company_uuid),
  ('66', 'Charges financières', 'expense', company_uuid),
  ('661', 'Charges d''intérêts', 'expense', company_uuid),
  ('665', 'Escomptes accordés', 'expense', company_uuid),
  ('666', 'Pertes de change', 'expense', company_uuid),
  ('667', 'Pertes nettes sur cessions de valeurs mobilières de placement', 'expense', company_uuid),
  ('668', 'Autres charges financières', 'expense', company_uuid),
  ('67', 'Éléments extraordinaires', 'expense', company_uuid),
  ('675', 'Valeurs comptables des éléments d''actif cédés', 'expense', company_uuid),
  ('678', 'Autres charges exceptionnelles', 'expense', company_uuid),
  ('68', 'Dotations aux amortissements et aux provisions', 'expense', company_uuid),
  ('681', 'Dotations aux amortissements et aux provisions - Charges d''exploitation', 'expense', company_uuid),
  ('686', 'Dotations aux amortissements et aux provisions - Charges financières', 'expense', company_uuid),
  ('687', 'Dotations aux amortissements et aux provisions - Éléments extraordinaires', 'expense', company_uuid),
  ('69', 'Impôts sur les bénéfices et assimilés', 'expense', company_uuid),
  ('695', 'Impôts sur les bénéfices', 'expense', company_uuid),
  ('697', 'Imposition forfaitaire annuelle des sociétés', 'expense', company_uuid),
  ('698', 'Intégration fiscale', 'expense', company_uuid),
  ('699', 'Produits - Reports en arrière des déficits', 'expense', company_uuid);

  -- CLASSE 7: COMPTES DE PRODUITS
  INSERT INTO accounts (code, name, type, company_id) VALUES
  ('70', 'Ventes de produits fabriqués, prestations de services, marchandises', 'revenue', company_uuid),
  ('701', 'Ventes de produits finis', 'revenue', company_uuid),
  ('702', 'Ventes de produits intermédiaires', 'revenue', company_uuid),
  ('703', 'Ventes de produits résiduels', 'revenue', company_uuid),
  ('704', 'Travaux', 'revenue', company_uuid),
  ('705', 'Études', 'revenue', company_uuid),
  ('706', 'Prestations de services', 'revenue', company_uuid),
  ('707', 'Ventes de marchandises', 'revenue', company_uuid),
  ('708', 'Produits des activités annexes', 'revenue', company_uuid),
  ('709', 'Rabais, remises et ristournes accordés par l''entreprise', 'revenue', company_uuid),
  ('71', 'Production stockée', 'revenue', company_uuid),
  ('713', 'Variation des stocks (en-cours de production, produits)', 'revenue', company_uuid),
  ('72', 'Production immobilisée', 'revenue', company_uuid),
  ('721', 'Immobilisations incorporelles', 'revenue', company_uuid),
  ('722', 'Immobilisations corporelles', 'revenue', company_uuid),
  ('74', 'Subventions d''exploitation', 'revenue', company_uuid),
  ('75', 'Autres produits de gestion courante', 'revenue', company_uuid),
  ('751', 'Redevances pour concessions, brevets, licences, marques, procédés, droits et valeurs similaires', 'revenue', company_uuid),
  ('752', 'Revenus des immeubles non affectés aux activités professionnelles', 'revenue', company_uuid),
  ('753', 'Jetons de présence et rémunérations d''administrateurs', 'revenue', company_uuid),
  ('754', 'Ristournes perçues des coopératives', 'revenue', company_uuid),
  ('755', 'Quotes-parts de résultat sur opérations faites en commun', 'revenue', company_uuid),
  ('758', 'Produits divers de gestion courante', 'revenue', company_uuid),
  ('76', 'Produits financiers', 'revenue', company_uuid),
  ('761', 'Produits de participations', 'revenue', company_uuid),
  ('762', 'Produits des autres immobilisations financières', 'revenue', company_uuid),
  ('763', 'Revenus des autres créances', 'revenue', company_uuid),
  ('764', 'Revenus des valeurs mobilières de placement', 'revenue', company_uuid),
  ('765', 'Escomptes obtenus', 'revenue', company_uuid),
  ('766', 'Gains de change', 'revenue', company_uuid),
  ('767', 'Produits nets sur cessions de valeurs mobilières de placement', 'revenue', company_uuid),
  ('768', 'Autres produits financiers', 'revenue', company_uuid),
  ('77', 'Éléments extraordinaires', 'revenue', company_uuid),
  ('775', 'Produits des cessions d''éléments d''actif', 'revenue', company_uuid),
  ('777', 'Quote-part des subventions d''investissement virée au résultat de l''exercice', 'revenue', company_uuid),
  ('778', 'Autres produits exceptionnels', 'revenue', company_uuid),
  ('78', 'Reprises sur amortissements et provisions', 'revenue', company_uuid),
  ('781', 'Reprises sur amortissements et provisions (à inscrire dans les produits d''exploitation)', 'revenue', company_uuid),
  ('786', 'Reprises sur provisions pour risques et charges financiers', 'revenue', company_uuid),
  ('787', 'Reprises sur provisions exceptionnelles', 'revenue', company_uuid);

END;
$$ LANGUAGE plpgsql;
