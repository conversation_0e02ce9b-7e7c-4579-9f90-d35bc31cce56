# **App Name**: Forsa Finance

## Core Features:

- Automated Payslips: AI-powered payslip generation: Automatically generate complete payslips (CNAS, IRG, net pay, etc.) from minimal input using an AI tool.
- Anomaly Detector: AI-driven anomaly detection: Continuously analyze financial data to identify errors, unusual patterns, and potential risks, suggesting necessary corrections using an AI tool.
- Multilingual Interface: Multilingual Support: Provide a user interface that supports French, Arabic, and English, with dynamic language switching via a language selection menu.
- Financial Dashboards: Interactive Dashboards: Display key financial metrics and reports in an easy-to-understand visual format, exportable to PDF or Excel.
- Data Sync: Offline/Online Synchronization: Automatically synchronize data between offline (local) and online (cloud) modes, managing any conflicts that may arise.
- User Permissions: User Access Control: Implement role-based access control to restrict access to sensitive data and features based on the user's position within the company organigram.
- Tax Automation: Automated Tax calculations and form population for VAT, IRG, G50, DAS

## Style Guidelines:

- Primary color: Deep Indigo (#6666B3), providing a sense of reliability, stability, and depth suitable for financial applications.
- Background color: Light gray (#F0F0F5), offering a clean and professional backdrop that reduces eye strain.
- Accent color: Teal (#408075), used for interactive elements and highlights, suggesting growth and trustworthiness.
- Body font: 'Inter', a sans-serif typeface to ensure excellent readability across different screen sizes.
- Headline font: 'Space Grotesk', a modern sans-serif typeface that complements Inter, to add a bit of technical flair.
- Consistent Icon Set: Utilize a modern, flat icon set, where the icon shapes convey trustworthiness.
- Subtle Transitions: Implement smooth transitions and feedback animations upon user interactions to enhance user experience.