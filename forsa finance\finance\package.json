{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 9002", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@react-pdf/renderer": "^4.3.0", "@types/uuid": "^10.0.0", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^17.2.0", "electron-is-dev": "^3.0.1", "embla-carousel-react": "^8.6.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.475.0", "next": "15.3.3", "patch-package": "^8.0.0", "qrcode": "^1.5.3", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-pdf": "^10.0.1", "recharts": "^2.15.1", "sqlite3": "^5.1.7", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "to-words": "^4.7.0", "uuid": "^11.1.0", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/file-saver": "^2.0.7", "@types/jspdf": "^1.3.3", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron": "^37.2.1", "electron-builder": "^26.0.12", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5", "wait-on": "^8.0.3"}, "build": {"appId": "com.forsafinance.app", "productName": "Forsa Finance", "directories": {"output": "dist"}, "files": ["out/**/*", "electron/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Forsa Finance"}, "mac": {"target": "dmg", "icon": "public/icon.icns"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}}}