// Squelette fonctionnel pour la génération de fiche de paie
export type GeneratePayslipInput = {
  employeeName: string;
  employeeRole: string;
  netSalary: number;
  employeeNSS?: string;
  hireDate?: string;
  children?: number;
};

export type PayslipItem = {
  description: string;
  amount: number;
};

export type GeneratePayslipOutput = {
  companyInfo: {
    name: string;
    address: string;
    nif: string;
  };
  employeeInfo: {
    name: string;
    role: string;
    nss: string;
    hireDate: string;
  };
  period: string;
  earnings: PayslipItem[];
  deductions: PayslipItem[];
  summary: {
    grossSalary: number;
    totalDeductions: number;
    netSalary: number;
  };
  qrCodeDataUri: string;
};

// Fonction factice qui retourne une fiche de paie de test
export async function generatePayslip(input: GeneratePayslipInput): Promise<GeneratePayslipOutput> {
  return {
    companyInfo: {
      name: "Entreprise Test",
      address: "1 rue de l'Exemple, Alger",
      nif: "1234567890",
    },
    employeeInfo: {
      name: input.employeeName,
      role: input.employeeRole,
      nss: input.employeeNSS || "N/A",
      hireDate: input.hireDate || "01/01/2020",
    },
    period: "Juillet 2025",
    earnings: [
      { description: "Salaire de base", amount: input.netSalary + 10000 },
      { description: "Prime", amount: 5000 },
    ],
    deductions: [
      { description: "CNAS", amount: 3000 },
      { description: "IRG", amount: 2000 },
    ],
    summary: {
      grossSalary: input.netSalary + 10000 + 5000,
      totalDeductions: 3000 + 2000,
      netSalary: input.netSalary,
    },
    qrCodeDataUri: "https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=FicheTest",
  };
}
