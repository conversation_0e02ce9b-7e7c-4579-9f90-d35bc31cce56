import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Journal } from "@/components/comptabilite/journal";
import { GrandLivre } from "@/components/comptabilite/grand-livre";
import { BalanceComptable } from "@/components/comptabilite/balance-comptable";
import { FileDigit, Book, Scale } from "lucide-react";

export default function ComptabilitePage() {
  return (
    <div className="flex flex-col gap-8">
      <div>
        <h1 className="text-3xl font-bold font-headline tracking-tight">Comptabilité Générale</h1>
        <p className="text-muted-foreground">
          Module de comptabilité conforme au système comptable financier algérien (SCF).
        </p>
      </div>
       <Tabs defaultValue="journal" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="journal">
            <FileDigit className="mr-2 h-4 w-4" />
            Journal
          </TabsTrigger>
          <TabsTrigger value="grand-livre">
            <Book className="mr-2 h-4 w-4" />
            Grand Livre
          </TabsTrigger>
          <TabsTrigger value="balance">
            <Scale className="mr-2 h-4 w-4" />
            Balance
          </TabsTrigger>
        </TabsList>
        <TabsContent value="journal">
          <Journal />
        </TabsContent>
        <TabsContent value="grand-livre">
          <GrandLivre />
        </TabsContent>
        <TabsContent value="balance">
          <BalanceComptable />
        </TabsContent>
      </Tabs>
    </div>
  );
}
