import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { G50Declaration } from "@/components/fiscalite/g50-declaration";
import { TvaDeclaration } from "@/components/fiscalite/tva-declaration";
import { IrgDeclaration } from "@/components/fiscalite/irg-declaration";
import { DasDeclaration } from "@/components/fiscalite/das-declaration";
import { FileText, Percent, Banknote, FileArchive } from "lucide-react";

export default function FiscalitePage() {
  return (
    <div className="flex flex-col gap-8">
      <div>
        <h1 className="text-3xl font-bold font-headline tracking-tight">Gestion Fiscale</h1>
        <p className="text-muted-foreground">
          Générez et préparez vos déclarations fiscales (TVA, IRG, G50, DAS).
        </p>
      </div>
      <Tabs defaultValue="g50" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="g50">
                <FileText className="mr-2 h-4 w-4" />
                G50
            </TabsTrigger>
            <TabsTrigger value="tva">
                <Percent className="mr-2 h-4 w-4" />
                TVA
            </TabsTrigger>
            <TabsTrigger value="irg">
                <Banknote className="mr-2 h-4 w-4" />
                IRG Salaires
            </TabsTrigger>
            <TabsTrigger value="das">
                <FileArchive className="mr-2 h-4 w-4" />
                DAS
            </TabsTrigger>
        </TabsList>
        <TabsContent value="g50">
            <G50Declaration />
        </TabsContent>
        <TabsContent value="tva">
             <TvaDeclaration />
        </TabsContent>
        <TabsContent value="irg">
            <IrgDeclaration />
        </TabsContent>
        <TabsContent value="das">
            <DasDeclaration />
        </TabsContent>
      </Tabs>
    </div>
  );
}
