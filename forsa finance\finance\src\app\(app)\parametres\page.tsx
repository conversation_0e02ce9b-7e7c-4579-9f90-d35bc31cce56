import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { CompanyProfile } from "@/components/parametres/company-profile";
import { UsersTable } from "@/components/parametres/users-table";
import { Integrations } from "@/components/parametres/integrations";
import { Building, Users, Puzzle } from "lucide-react";

export default function ParametresPage() {
  return (
    <div className="flex flex-col gap-8">
      <div>
        <h1 className="text-3xl font-bold font-headline tracking-tight">Paramètres</h1>
        <p className="text-muted-foreground">
          <PERSON><PERSON><PERSON> les paramètres de votre entreprise, les utilisateurs et les intégrations.
        </p>
      </div>
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="grid w-full grid-cols-1 md:grid-cols-3">
          <TabsTrigger value="profile">
            <Building className="mr-2 h-4 w-4" />
            Profil de l'entreprise
            </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="mr-2 h-4 w-4" />
            Utilisateurs & Accès
            </TabsTrigger>
          <TabsTrigger value="integrations">
            <Puzzle className="mr-2 h-4 w-4" />
            Intégrations
            </TabsTrigger>
        </TabsList>
        <TabsContent value="profile">
          <CompanyProfile />
        </TabsContent>
        <TabsContent value="users">
          <UsersTable />
        </TabsContent>
        <TabsContent value="integrations">
          <Integrations />
        </TabsContent>
      </Tabs>
    </div>
  );
}
