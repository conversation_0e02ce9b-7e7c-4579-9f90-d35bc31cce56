
'use client';

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PayrollCalculator } from '@/components/paie/payroll-calculator';
import { EmployeeList } from "@/components/rh/employee-list";
import { TimeTracking } from "@/components/rh/time-tracking";
import { EmolumentsReport } from "@/components/rh/emoluments-report";
import { HRDashboard } from "@/components/rh/hr-dashboard";
import { Users, Calculator, BarChart3, CheckCircle, Clock, LayoutDashboard } from "lucide-react";
import type { Employee } from "@/data/employees";

export default function RessourcesHumainesPage() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);

  const handleGeneratePayslip = (employee: Employee) => {
    setSelectedEmployee(employee);
    setActiveTab("payslip");
  };

  const tabs = [
    {
      id: 'dashboard',
      label: 'Tableau de Bord',
      icon: LayoutDashboard,
      description: 'Vue d\'ensemble et statistiques RH',
      status: 'ready'
    },
    {
      id: 'employees',
      label: 'Employés',
      icon: Users,
      description: 'Gestion complète des employés',
      status: 'ready'
    },
    {
      id: 'timetracking',
      label: 'Pointage & Heures',
      icon: Clock,
      description: 'Pointage quotidien et heures supplémentaires',
      status: 'ready'
    },
    {
      id: 'payslip',
      label: 'Fiche de paie',
      icon: Calculator,
      description: 'Calcul et génération des fiches de paie',
      status: 'ready'
    },
    {
      id: 'reports',
      label: 'Relevé des Émoluments',
      icon: BarChart3,
      description: 'Relevé annuel des émoluments',
      status: 'ready'
    }
  ];

  return (
    <div className="flex flex-col gap-8">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold font-headline tracking-tight">Ressources Humaines</h1>
          <p className="text-muted-foreground mt-2">
            Gestion complète des employés, calcul des salaires et rapports RH
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="text-green-600 border-green-600">
            <CheckCircle className="w-3 h-3 mr-1" />
            Module Opérationnel
          </Badge>
        </div>
      </div>

      {/* Statistiques rapides */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        {tabs.map((tab) => {
          const IconComponent = tab.icon;
          return (
            <Card
              key={tab.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                activeTab === tab.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => setActiveTab(tab.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    activeTab === tab.id ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <IconComponent className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium text-gray-900">{tab.label}</h3>
                      <Badge
                        variant={tab.status === 'ready' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {tab.status === 'ready' ? 'Prêt' : 'Dev'}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{tab.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Contenu principal */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <TabsTrigger key={tab.id} value={tab.id} className="flex items-center space-x-2">
                <IconComponent className="w-4 h-4" />
                <span>{tab.label}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <HRDashboard />
        </TabsContent>

        <TabsContent value="employees" className="space-y-6">
          <EmployeeList onGeneratePayslip={handleGeneratePayslip} />
        </TabsContent>

        <TabsContent value="timetracking" className="space-y-6">
          <TimeTracking />
        </TabsContent>

        <TabsContent value="payslip" className="space-y-6">
          <PayrollCalculator selectedEmployee={selectedEmployee} />
        </TabsContent>

        <TabsContent value="reports" className="space-y-6">
          <EmolumentsReport />
        </TabsContent>


      </Tabs>
    </div>
  );
}
