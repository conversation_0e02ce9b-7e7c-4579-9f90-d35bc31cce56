
import { StatsCard } from '@/components/dashboard/stats-card';
import { ProductList } from '@/components/stock/product-list';
import { StockMovements } from '@/components/stock/stock-movements';
import { StockReports } from '@/components/stock/stock-reports';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Package, Boxes, ArrowLeftRight, FileText, IndianRupee, Sigma } from "lucide-react";

export default function StockPage() {
  return (
    <div className="flex flex-col gap-8">
      <div>
        <h1 className="text-3xl font-bold font-headline tracking-tight">Gestion de Stock</h1>
        <p className="text-muted-foreground">
          Suivez vos articles, gérez les niveaux de stock, les mouvements et analysez vos performances.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard title="Valeur du Stock" value="1,848,500 DZD" icon={<IndianRupee className="h-5 w-5 text-muted-foreground" />} />
        <StatsCard title="Références" value="4 Produits" icon={<Sigma className="h-5 w-5 text-muted-foreground" />} />
        <StatsCard title="Articles en faible stock" value="1" change="8 unités restantes" icon={<Boxes className="h-5 w-5 text-muted-foreground" />} />
        <StatsCard title="Articles en rupture" value="0" icon={<Package className="h-5 w-5 text-muted-foreground" />} />
      </div>

      <Tabs defaultValue="products" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="products">
            <Package className="mr-2 h-4 w-4" />
            Liste des Produits
          </TabsTrigger>
          <TabsTrigger value="movements">
            <ArrowLeftRight className="mr-2 h-4 w-4" />
            Mouvements de Stock
          </TabsTrigger>
          <TabsTrigger value="reports">
            <FileText className="mr-2 h-4 w-4" />
            Rapports
          </TabsTrigger>
        </TabsList>
        <TabsContent value="products">
          <ProductList />
        </TabsContent>
        <TabsContent value="movements">
          <StockMovements />
        </TabsContent>
        <TabsContent value="reports">
          <StockReports />
        </TabsContent>
      </Tabs>
    </div>
  );
}
