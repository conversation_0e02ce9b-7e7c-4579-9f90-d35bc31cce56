'use client';

import { useState, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { anomalyDetectionService, type FinancialData, type Anomaly } from '@/lib/analytics/anomaly-detection';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Loader2, AlertTriangle, ShieldCheck, ScanSearch } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const formSchema = z.object({
  financialData: z.string().min(50, { message: 'Veuillez fournir des données plus détaillées (au moins 50 caractères).' }),
});

type FormValues = z.infer<typeof formSchema>;

const sampleData = `Revenus: Jan-5M DZD, Fev-5.2M DZD, Mar-4.8M DZD, Avr-7.5M DZD (lancement produit), Mai-6M DZD
Dépenses: Jan-3M, Fev-3.1M, Mar-3.2M, Avr-4.5M, Mai-5.5M (campagne marketing)
Salaires: 1.5M/mois constant
Transaction inhabituelle: Paiement de 2M DZD à un fournisseur inconnu 'XYZ Global' en Mai.`;

export function AnomalyDetector() {
  const [isPending, startTransition] = useTransition();
  const [analysisResult, setAnalysisResult] = useState<Anomaly[] | null>(null);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      financialData: '',
    },
  });

  const onSubmit = (values: FormValues) => {
    setAnalysisResult(null);
    startTransition(async () => {
      try {
        const result = await detectAnomalies({ financialData: values.financialData });
        setAnalysisResult(result);
      } catch (error) {
        console.error('Anomaly detection failed:', error);
        toast({
          variant: 'destructive',
          title: 'Erreur d\'Analyse',
          description: 'Impossible d\'analyser les données. Veuillez réessayer.',
        });
      }
    });
  };

  const getSeverityVariant = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const getSeverityIcon = (severity: 'low' | 'medium' | 'high') => {
    switch (severity) {
      case 'high':
        return <AlertTriangle className="h-4 w-4" />;
      case 'medium':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <ShieldCheck className="h-4 w-4 text-blue-500" />;
    }
  };

  return (
    <div className="grid gap-8 lg:grid-cols-2">
      <Card>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardHeader>
              <CardTitle>Analyseur de Données Financières</CardTitle>
              <CardDescription>
                Collez vos données financières brutes ici. L&apos;IA les analysera pour y déceler des anomalies.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="financialData"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Données financières</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Ex: Revenus, Dépenses, Transactions..."
                        className="min-h-[200px] font-code"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button type="submit" disabled={isPending}>
                {isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <ScanSearch className="mr-2 h-4 w-4" />
                )}
                Analyser les données
              </Button>
              <Button
                type="button"
                variant="ghost"
                onClick={() => form.setValue('financialData', sampleData)}
              >
                Utiliser un exemple
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Résultats de l&apos;Analyse</CardTitle>
          <CardDescription>Les anomalies détectées par l&apos;IA s&apos;afficheront ici.</CardDescription>
        </CardHeader>
        <CardContent>
          {isPending && (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          )}
          {analysisResult && (
            <div className="space-y-4">
              <Alert>
                <ShieldCheck className="h-4 w-4" />
                <AlertTitle>Résumé de l&apos;Analyse</AlertTitle>
                <AlertDescription>{analysisResult.summary}</AlertDescription>
              </Alert>
              <h3 className="font-semibold pt-4">Anomalies Détaillées</h3>
              {analysisResult.anomalies.length > 0 ? (
                analysisResult.anomalies.map((anomaly, index) => (
                  <Alert key={index} variant={getSeverityVariant(anomaly.severity)}>
                    {getSeverityIcon(anomaly.severity)}
                    <AlertTitle className="capitalize">
                      Anomalie (Sévérité : {anomaly.severity})
                    </AlertTitle>
                    <AlertDescription>
                      <p className="font-semibold">{anomaly.description}</p>
                      <p className="text-xs mt-1">Cause potentielle : {anomaly.potentialCause}</p>
                    </AlertDescription>
                  </Alert>
                ))
              ) : (
                <div className="text-center text-muted-foreground p-8 border-2 border-dashed rounded-lg">
                  <ShieldCheck className="mx-auto h-12 w-12 text-green-500" />
                  <p className="mt-4">Aucune anomalie significative détectée. Tout semble en ordre !</p>
                </div>
              )}
            </div>
          )}
          {!isPending && !analysisResult && (
            <div className="flex flex-col items-center justify-center text-center text-muted-foreground h-64 border-2 border-dashed rounded-lg">
              <p>Le rapport d&apos;analyse apparaîtra ici.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
