'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, TableFooter } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";

const balanceData = [
    { account: '401', name: 'Fournisseurs', debit: 0, credit: 50000 },
    { account: '411', name: 'Clients', debit: 0, credit: 120000 },
    { account: '512', name: 'Banque BNA', debit: 120000, credit: 15000 },
    { account: '601', name: 'Achats de marchandises', debit: 50000, credit: 0 },
    { account: '626', name: 'Frais postaux et télécoms', debit: 15000, credit: 0 },
];

const calculateBalance = (data: typeof balanceData) => {
    return data.map(item => {
        const soldeDebit = item.debit > item.credit ? item.debit - item.credit : 0;
        const soldeCredit = item.credit > item.debit ? item.credit - item.debit : 0;
        return { ...item, soldeDebit, soldeCredit };
    });
};

const formatCurrency = (amount: number) => {
    if (amount === 0) return '0.00';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

export function BalanceComptable() {
    const balancedAccounts = calculateBalance(balanceData);

    const totalMvtDebit = balancedAccounts.reduce((sum, item) => sum + item.debit, 0);
    const totalMvtCredit = balancedAccounts.reduce((sum, item) => sum + item.credit, 0);
    const totalSoldeDebit = balancedAccounts.reduce((sum, item) => sum + item.soldeDebit, 0);
    const totalSoldeCredit = balancedAccounts.reduce((sum, item) => sum + item.soldeCredit, 0);

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Balance Générale</CardTitle>
                    <CardDescription>État récapitulatif des comptes de l'entreprise.</CardDescription>
                </div>
                 <Button variant="outline">
                    <Printer className="mr-2 h-4 w-4" />
                    Imprimer la Balance
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead className="w-[100px]">Compte</TableHead>
                            <TableHead>Intitulé du compte</TableHead>
                            <TableHead className="text-right">Mvt. Débit</TableHead>
                            <TableHead className="text-right">Mvt. Crédit</TableHead>
                            <TableHead className="text-right">Solde Débiteur</TableHead>
                            <TableHead className="text-right">Solde Créditeur</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {balancedAccounts.map((item) => (
                            <TableRow key={item.account}>
                                <TableCell className="font-medium">{item.account}</TableCell>
                                <TableCell>{item.name}</TableCell>
                                <TableCell className="text-right font-mono">{formatCurrency(item.debit)}</TableCell>
                                <TableCell className="text-right font-mono">{formatCurrency(item.credit)}</TableCell>
                                <TableCell className="text-right font-mono text-accent">{formatCurrency(item.soldeDebit)}</TableCell>
                                <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.soldeCredit)}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                    <TableFooter>
                        <TableRow>
                            <TableCell colSpan={2} className="font-bold text-lg">Totaux</TableCell>
                            <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totalMvtDebit)}</TableCell>
                            <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totalMvtCredit)}</TableCell>
                            <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totalSoldeDebit)}</TableCell>
                            <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totalSoldeCredit)}</TableCell>
                        </TableRow>
                    </TableFooter>
                </Table>
            </CardContent>
        </Card>
    );
}
