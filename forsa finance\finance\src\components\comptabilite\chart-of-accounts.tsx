'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Search, Edit, Trash2 } from "lucide-react";
import { useToast } from '@/hooks/use-toast';

// Plan comptable algérien (SCF) - Comptes principaux
const defaultAccounts = [
  // Classe 1 - Comptes de capitaux
  { code: '10', name: 'Capital', type: 'equity', parent: null, isActive: true },
  { code: '101', name: 'Capital émis', type: 'equity', parent: '10', isActive: true },
  { code: '106', name: 'Réserves', type: 'equity', parent: '10', isActive: true },
  { code: '12', name: 'Résultat de l\'exercice', type: 'equity', parent: null, isActive: true },
  
  // Classe 2 - Comptes d'immobilisations
  { code: '20', name: 'Immobilisations incorporelles', type: 'asset', parent: null, isActive: true },
  { code: '21', name: 'Immobilisations corporelles', type: 'asset', parent: null, isActive: true },
  { code: '213', name: 'Constructions', type: 'asset', parent: '21', isActive: true },
  { code: '218', name: 'Autres immobilisations corporelles', type: 'asset', parent: '21', isActive: true },
  { code: '2182', name: 'Matériel de transport', type: 'asset', parent: '218', isActive: true },
  { code: '2183', name: 'Matériel de bureau et informatique', type: 'asset', parent: '218', isActive: true },
  
  // Classe 3 - Comptes de stocks
  { code: '30', name: 'Stocks de marchandises', type: 'asset', parent: null, isActive: true },
  { code: '31', name: 'Matières premières', type: 'asset', parent: null, isActive: true },
  { code: '32', name: 'Autres approvisionnements', type: 'asset', parent: null, isActive: true },
  
  // Classe 4 - Comptes de tiers
  { code: '40', name: 'Fournisseurs et comptes rattachés', type: 'liability', parent: null, isActive: true },
  { code: '401', name: 'Fournisseurs', type: 'liability', parent: '40', isActive: true },
  { code: '408', name: 'Fournisseurs - Factures non parvenues', type: 'liability', parent: '40', isActive: true },
  { code: '41', name: 'Clients et comptes rattachés', type: 'asset', parent: null, isActive: true },
  { code: '411', name: 'Clients', type: 'asset', parent: '41', isActive: true },
  { code: '416', name: 'Clients douteux', type: 'asset', parent: '41', isActive: true },
  { code: '42', name: 'Personnel et comptes rattachés', type: 'liability', parent: null, isActive: true },
  { code: '421', name: 'Personnel - Rémunérations dues', type: 'liability', parent: '42', isActive: true },
  { code: '43', name: 'Organismes sociaux', type: 'liability', parent: null, isActive: true },
  { code: '431', name: 'Sécurité sociale', type: 'liability', parent: '43', isActive: true },
  { code: '44', name: 'État et collectivités publiques', type: 'liability', parent: null, isActive: true },
  { code: '445', name: 'État - TVA', type: 'liability', parent: '44', isActive: true },
  { code: '447', name: 'État - Autres impôts et taxes', type: 'liability', parent: '44', isActive: true },
  
  // Classe 5 - Comptes financiers
  { code: '50', name: 'Valeurs mobilières de placement', type: 'asset', parent: null, isActive: true },
  { code: '51', name: 'Banques, établissements financiers', type: 'asset', parent: null, isActive: true },
  { code: '512', name: 'Banques', type: 'asset', parent: '51', isActive: true },
  { code: '53', name: 'Caisse', type: 'asset', parent: null, isActive: true },
  
  // Classe 6 - Comptes de charges
  { code: '60', name: 'Achats consommés', type: 'expense', parent: null, isActive: true },
  { code: '601', name: 'Achats de marchandises', type: 'expense', parent: '60', isActive: true },
  { code: '602', name: 'Achats de matières premières', type: 'expense', parent: '60', isActive: true },
  { code: '61', name: 'Services extérieurs', type: 'expense', parent: null, isActive: true },
  { code: '613', name: 'Locations', type: 'expense', parent: '61', isActive: true },
  { code: '62', name: 'Autres services extérieurs', type: 'expense', parent: null, isActive: true },
  { code: '626', name: 'Frais postaux et télécommunications', type: 'expense', parent: '62', isActive: true },
  { code: '63', name: 'Charges de personnel', type: 'expense', parent: null, isActive: true },
  { code: '631', name: 'Rémunérations du personnel', type: 'expense', parent: '63', isActive: true },
  { code: '635', name: 'Cotisations aux organismes sociaux', type: 'expense', parent: '63', isActive: true },
  
  // Classe 7 - Comptes de produits
  { code: '70', name: 'Ventes de marchandises', type: 'revenue', parent: null, isActive: true },
  { code: '701', name: 'Ventes de marchandises', type: 'revenue', parent: '70', isActive: true },
  { code: '706', name: 'Prestations de services', type: 'revenue', parent: '70', isActive: true },
];

const accountSchema = z.object({
  code: z.string().min(1, 'Le code compte est requis').max(10, 'Le code ne peut pas dépasser 10 caractères'),
  name: z.string().min(1, 'Le nom du compte est requis').max(100, 'Le nom ne peut pas dépasser 100 caractères'),
  type: z.enum(['asset', 'liability', 'equity', 'revenue', 'expense'], {
    required_error: 'Le type de compte est requis',
  }),
  parent: z.string().optional(),
});

type AccountFormValues = z.infer<typeof accountSchema>;

const accountTypeLabels = {
  asset: 'Actif',
  liability: 'Passif',
  equity: 'Capitaux propres',
  revenue: 'Produits',
  expense: 'Charges',
};

const accountTypeColors = {
  asset: 'bg-blue-100 text-blue-800',
  liability: 'bg-red-100 text-red-800',
  equity: 'bg-green-100 text-green-800',
  revenue: 'bg-purple-100 text-purple-800',
  expense: 'bg-orange-100 text-orange-800',
};

export function ChartOfAccounts() {
  const [accounts, setAccounts] = useState(defaultAccounts);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<any>(null);
  const { toast } = useToast();

  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      code: '',
      name: '',
      type: 'asset',
      parent: '',
    },
  });

  const filteredAccounts = accounts.filter(account =>
    account.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const parentAccounts = accounts.filter(account => !account.parent);

  const onSubmit = (values: AccountFormValues) => {
    try {
      if (editingAccount) {
        // Modifier un compte existant
        setAccounts(prev => prev.map(account =>
          account.code === editingAccount.code
            ? { ...account, ...values, isActive: true }
            : account
        ));
        toast({
          title: "Compte modifié",
          description: `Le compte ${values.code} - ${values.name} a été modifié avec succès.`,
        });
      } else {
        // Ajouter un nouveau compte
        const newAccount = {
          ...values,
          parent: values.parent || null,
          isActive: true,
        };
        setAccounts(prev => [...prev, newAccount]);
        toast({
          title: "Compte ajouté",
          description: `Le compte ${values.code} - ${values.name} a été ajouté avec succès.`,
        });
      }

      setIsDialogOpen(false);
      setEditingAccount(null);
      form.reset();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement du compte.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (account: any) => {
    setEditingAccount(account);
    form.reset({
      code: account.code,
      name: account.name,
      type: account.type,
      parent: account.parent || '',
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (accountCode: string) => {
    // Vérifier si le compte a des sous-comptes
    const hasChildren = accounts.some(account => account.parent === accountCode);
    if (hasChildren) {
      toast({
        title: "Suppression impossible",
        description: "Ce compte a des sous-comptes. Supprimez d'abord les sous-comptes.",
        variant: "destructive",
      });
      return;
    }

    setAccounts(prev => prev.filter(account => account.code !== accountCode));
    toast({
      title: "Compte supprimé",
      description: "Le compte a été supprimé avec succès.",
    });
  };

  const openNewAccountDialog = () => {
    setEditingAccount(null);
    form.reset();
    setIsDialogOpen(true);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Plan Comptable</CardTitle>
          <CardDescription>
            Gestion du plan comptable conforme au système comptable financier (SCF) algérien.
          </CardDescription>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={openNewAccountDialog}>
              <Plus className="mr-2 h-4 w-4" />
              Nouveau compte
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingAccount ? 'Modifier le compte' : 'Nouveau compte'}
              </DialogTitle>
              <DialogDescription>
                {editingAccount 
                  ? 'Modifiez les informations du compte comptable.'
                  : 'Ajoutez un nouveau compte au plan comptable.'
                }
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Code compte</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: 411" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom du compte</FormLabel>
                      <FormControl>
                        <Input placeholder="Ex: Clients" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Type de compte</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner le type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(accountTypeLabels).map(([value, label]) => (
                            <SelectItem key={value} value={value}>
                              {label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="parent"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Compte parent (optionnel)</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner le compte parent" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="">Aucun parent</SelectItem>
                          {parentAccounts.map((account) => (
                            <SelectItem key={account.code} value={account.code}>
                              {account.code} - {account.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">
                    {editingAccount ? 'Modifier' : 'Ajouter'}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-2 mb-4">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher un compte..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Code</TableHead>
              <TableHead>Nom du compte</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Compte parent</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAccounts.map((account) => (
              <TableRow key={account.code}>
                <TableCell className="font-mono font-medium">{account.code}</TableCell>
                <TableCell>{account.name}</TableCell>
                <TableCell>
                  <Badge className={accountTypeColors[account.type as keyof typeof accountTypeColors]}>
                    {accountTypeLabels[account.type as keyof typeof accountTypeLabels]}
                  </Badge>
                </TableCell>
                <TableCell>
                  {account.parent ? (
                    <span className="font-mono text-sm text-muted-foreground">
                      {account.parent}
                    </span>
                  ) : (
                    '-'
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant={account.isActive ? 'default' : 'secondary'}>
                    {account.isActive ? 'Actif' : 'Inactif'}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(account)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(account.code)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
