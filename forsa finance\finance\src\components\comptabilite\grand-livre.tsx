'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";

const ledgerData = {
    '401': {
        name: 'Fournisseurs',
        entries: [
            { date: '2023-10-28', journal: 'JV001', label: 'Achat de marchandises', debit: 0, credit: 50000 },
        ],
    },
    '411': {
        name: 'Clients',
        entries: [
            { date: '2023-10-27', journal: 'JV002', label: 'Virement client Innova SARL', debit: 0, credit: 120000 },
        ],
    },
    '512': {
        name: 'Banque BNA',
        entries: [
            { date: '2023-10-27', journal: 'JV002', label: 'Virement client Innova SARL', debit: 120000, credit: 0 },
            { date: '2023-10-26', journal: 'JV003', label: 'Frais postaux et télécoms', debit: 0, credit: 15000 },
        ],
    },
    '601': {
        name: 'Achats de marchandises',
        entries: [
            { date: '2023-10-28', journal: 'JV001', label: 'Achat de marchandises', debit: 50000, credit: 0 },
        ],
    },
    '626': {
        name: 'Frais postaux et télécommunications',
        entries: [
            { date: '2023-10-26', journal: 'JV003', label: 'Frais postaux et télécoms', debit: 15000, credit: 0 },
        ],
    },
};

const formatCurrency = (amount: number) => {
    if (amount === 0) return '';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

const calculateBalance = (entries: any[]) => {
    let balance = 0;
    const entriesWithBalance = entries.map(entry => {
        balance += entry.debit - entry.credit;
        return { ...entry, balance };
    });
    return entriesWithBalance;
};

const calculateTotals = (entries: any[]) => {
    const totalDebit = entries.reduce((sum, entry) => sum + entry.debit, 0);
    const totalCredit = entries.reduce((sum, entry) => sum + entry.credit, 0);
    const finalBalance = totalDebit - totalCredit;
    return { totalDebit, totalCredit, finalBalance };
};

export function GrandLivre() {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Grand Livre</CardTitle>
                <CardDescription>Détail des mouvements par compte pour la période sélectionnée.</CardDescription>
            </CardHeader>
            <CardContent>
                <Accordion type="single" collapsible className="w-full">
                    {Object.entries(ledgerData).map(([account, data]) => {
                        const entriesWithBalance = calculateBalance(data.entries);
                        const { totalDebit, totalCredit, finalBalance } = calculateTotals(data.entries);

                        return (
                            <AccordionItem value={account} key={account}>
                                <AccordionTrigger className="hover:no-underline">
                                    <div className="flex justify-between w-full pr-4">
                                        <span className="font-semibold">{account} - {data.name}</span>
                                        <span className={`font-mono ${finalBalance >= 0 ? 'text-accent' : 'text-destructive'}`}>
                                            Solde: {formatCurrency(Math.abs(finalBalance))} {finalBalance >= 0 ? 'D' : 'C'}
                                        </span>
                                    </div>
                                </AccordionTrigger>
                                <AccordionContent>
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Date</TableHead>
                                                <TableHead>Journal</TableHead>
                                                <TableHead>Libellé</TableHead>
                                                <TableHead className="text-right">Débit</TableHead>
                                                <TableHead className="text-right">Crédit</TableHead>
                                                <TableHead className="text-right">Solde</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {entriesWithBalance.map((entry, index) => (
                                                <TableRow key={index}>
                                                    <TableCell>{entry.date}</TableCell>
                                                    <TableCell>{entry.journal}</TableCell>
                                                    <TableCell>{entry.label}</TableCell>
                                                    <TableCell className="text-right font-mono">{formatCurrency(entry.debit)}</TableCell>
                                                    <TableCell className="text-right font-mono">{formatCurrency(entry.credit)}</TableCell>
                                                    <TableCell className="text-right font-mono">{formatCurrency(entry.balance)}</TableCell>
                                                </TableRow>
                                            ))}
                                             <TableRow className="font-bold bg-muted/50">
                                                <TableCell colSpan={3}>Totaux du compte</TableCell>
                                                <TableCell className="text-right font-mono">{formatCurrency(totalDebit)}</TableCell>
                                                <TableCell className="text-right font-mono">{formatCurrency(totalCredit)}</TableCell>
                                                <TableCell className="text-right font-mono">{formatCurrency(finalBalance)}</TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </AccordionContent>
                            </AccordionItem>
                        );
                    })}
                </Accordion>
            </CardContent>
        </Card>
    );
}
