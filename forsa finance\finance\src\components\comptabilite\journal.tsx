'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Filter } from "lucide-react";
import { NewEntryDialog } from './new-entry-dialog';

const journalEntries = [
  { id: 'JV001', date: '2023-10-28', account: '601', label: 'Achat de marchandises', debit: 50000, credit: 0 },
  { id: 'JV001', date: '2023-10-28', account: '401', label: 'Fournisseur TechCorp', debit: 0, credit: 50000 },
  { id: 'JV002', date: '2023-10-27', account: '512', label: 'Banque BNA', debit: 120000, credit: 0 },
  { id: 'JV002', date: '2023-10-27', account: '411', label: 'Client Innova SARL', debit: 0, credit: 120000 },
  { id: 'JV003', date: '2023-10-26', account: '626', label: 'Frais postaux et télécoms', debit: 15000, credit: 0 },
  { id: 'JV003', date: '2023-10-26', account: '512', label: 'Banque BNA', debit: 0, credit: 15000 },
];

const formatCurrency = (amount: number) => {
    if (amount === 0) return '';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

export function Journal() {
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    return (
        <>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Journal Comptable</CardTitle>
                    <CardDescription>Liste des écritures comptables.</CardDescription>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline">
                        <Filter className="mr-2 h-4 w-4" />
                        Filtrer
                    </Button>
                    <Button onClick={() => setIsDialogOpen(true)}>
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Ajouter une écriture
                    </Button>
                </div>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Date</TableHead>
                            <TableHead>Pièce</TableHead>
                            <TableHead>N° Compte</TableHead>
                            <TableHead>Libellé</TableHead>
                            <TableHead className="text-right">Débit (DZD)</TableHead>
                            <TableHead className="text-right">Crédit (DZD)</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {journalEntries.map((entry, index) => (
                            <TableRow key={index}>
                                <TableCell>{entry.date}</TableCell>
                                <TableCell>{entry.id}</TableCell>
                                <TableCell>{entry.account}</TableCell>
                                <TableCell>{entry.label}</TableCell>
                                <TableCell className="text-right font-mono">{formatCurrency(entry.debit)}</TableCell>
                                <TableCell className="text-right font-mono">{formatCurrency(entry.credit)}</TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
        <NewEntryDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
        </>
    );
}
