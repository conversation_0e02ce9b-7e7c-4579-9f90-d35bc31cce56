'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PlusCircle, Trash2 } from 'lucide-react';
import { useState } from 'react';

type EntryLine = {
    account: string;
    label: string;
    debit: string;
    credit: string;
};

export function NewEntryDialog({ open, onOpenChange }: { open: boolean, onOpenChange: (open: boolean) => void }) {
    const [lines, setLines] = useState<EntryLine[]>([
        { account: '', label: '', debit: '', credit: '' },
        { account: '', label: '', debit: '', credit: '' },
    ]);

    const addLine = () => {
        setLines([...lines, { account: '', label: '', debit: '', credit: '' }]);
    };

    const removeLine = (index: number) => {
        setLines(lines.filter((_, i) => i !== index));
    };

    const handleLineChange = (index: number, field: keyof EntryLine, value: string) => {
        const newLines = [...lines];
        newLines[index][field] = value;
        setLines(newLines);
    };

    const totalDebit = lines.reduce((acc, line) => acc + (parseFloat(line.debit) || 0), 0);
    const totalCredit = lines.reduce((acc, line) => acc + (parseFloat(line.credit) || 0), 0);
    const isBalanced = totalDebit === totalCredit && totalDebit > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>Nouvelle Écriture Comptable</DialogTitle>
          <DialogDescription>
            Saisissez les détails de l'écriture. Assurez-vous que le total des débits est égal au total des crédits.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="entry-date">Date</Label>
                    <Input id="entry-date" type="date" defaultValue={new Date().toISOString().split('T')[0]} />
                </div>
                 <div className="space-y-2">
                    <Label htmlFor="entry-id">N° Pièce</Label>
                    <Input id="entry-id" placeholder="Ex: JV004" />
                </div>
            </div>
            <div className="space-y-2">
                {lines.map((line, index) => (
                    <div key={index} className="grid grid-cols-[1fr_2fr_1fr_1fr_auto] gap-2 items-center">
                        <Input placeholder="Compte" value={line.account} onChange={e => handleLineChange(index, 'account', e.target.value)} />
                        <Input placeholder="Libellé" value={line.label} onChange={e => handleLineChange(index, 'label', e.target.value)} />
                        <Input placeholder="Débit" type="number" value={line.debit} onChange={e => handleLineChange(index, 'debit', e.target.value)} />
                        <Input placeholder="Crédit" type="number" value={line.credit} onChange={e => handleLineChange(index, 'credit', e.target.value)} />
                        <Button variant="ghost" size="icon" onClick={() => removeLine(index)} disabled={lines.length <= 2}>
                            <Trash2 className="h-4 w-4" />
                        </Button>
                    </div>
                ))}
                <Button variant="outline" size="sm" onClick={addLine}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Ajouter une ligne
                </Button>
            </div>
            <div className="flex justify-end gap-8 font-mono font-bold text-lg pt-4 pr-16">
                <div className="flex flex-col items-end">
                    <span>Total Débit</span>
                    <span>{totalDebit.toFixed(2)} DZD</span>
                </div>
                 <div className="flex flex-col items-end">
                    <span>Total Crédit</span>
                    <span>{totalCredit.toFixed(2)} DZD</span>
                </div>
            </div>
        </div>
        <DialogFooter>
            <DialogClose asChild>
                <Button type="button" variant="secondary">Annuler</Button>
            </DialogClose>
            <Button type="submit" disabled={!isBalanced}>Enregistrer l'écriture</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
