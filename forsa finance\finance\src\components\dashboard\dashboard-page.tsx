import { StatsCard } from '@/components/dashboard/stats-card';
import { RevenueChart } from '@/components/dashboard/revenue-chart';
import { Button } from '@/components/ui/button';
import { FileDown, DollarSign, CreditCard, TrendingUp, Wallet } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

const recentTransactions = [
  {
    id: 1,
    description: 'Paiement fournisseur TechCorp',
    amount: -45000,
    date: '2023-10-26',
    status: 'Complété',
  },
  {
    id: 2,
    description: 'Virement client Innova SARL',
    amount: 120000,
    date: '2023-10-25',
    status: 'Complété',
  },
  {
    id: 3,
    description: 'Achat de fournitures',
    amount: -7500,
    date: '2023-10-24',
    status: 'Comp<PERSON><PERSON>',
  },
  {
    id: 4,
    description: 'Remboursement prêt bancaire',
    amount: -80000,
    date: '2023-10-22',
    status: 'Complété',
  },
];

export default function DashboardPage() {
  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold font-headline tracking-tight">Tableau de bord</h1>
          <p className="text-muted-foreground">
            Aperçu rapide des performances financières de votre entreprise.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <FileDown className="mr-2" />
            Exporter en PDF
          </Button>
          <Button variant="outline">
            <FileDown className="mr-2" />
            Exporter en Excel
          </Button>
        </div>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard title="Revenu Total" value="1,250,000 DZD" change="+12.5%" icon={<DollarSign className="h-4 w-4 text-muted-foreground" />} />
        <StatsCard title="Dépenses" value="780,000 DZD" change="+8.2%" icon={<CreditCard className="h-4 w-4 text-muted-foreground" />} />
        <StatsCard title="Bénéfice Net" value="470,000 DZD" change="+18.1%" icon={<TrendingUp className="h-4 w-4 text-muted-foreground" />} />
        <StatsCard title="Solde de Trésorerie" value="2,130,000 DZD" icon={<Wallet className="h-4 w-4 text-muted-foreground" />} />
      </div>
      <div className="grid gap-4 lg:grid-cols-2">
        <RevenueChart />
        <Card>
          <CardHeader>
            <CardTitle>Transactions Récentes</CardTitle>
            <CardDescription>Les 4 dernières transactions enregistrées.</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Description</TableHead>
                  <TableHead className="text-right">Montant (DZD)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentTransactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell className="font-medium">{transaction.description}</TableCell>
                    <TableCell
                      className={`text-right font-semibold ${
                        transaction.amount > 0 ? 'text-green-600' : 'text-red-500'
                      }`}
                    >
                      {new Intl.NumberFormat('fr-DZ').format(transaction.amount)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
