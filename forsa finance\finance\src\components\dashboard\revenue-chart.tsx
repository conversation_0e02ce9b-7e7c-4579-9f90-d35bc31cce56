'use client';

import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XA<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltipContent,
  type ChartConfig,
  ChartLegendContent,
} from '@/components/ui/chart';

const chartData = [
  { month: 'Jan', revenue: 1860000, expenses: 800000 },
  { month: 'Fev', revenue: 3050000, expenses: 2000000 },
  { month: 'Mar', revenue: 2370000, expenses: 1200000 },
  { month: 'Avr', revenue: 730000, expenses: 1900000 },
  { month: 'Mai', revenue: 2090000, expenses: 1300000 },
  { month: 'Juin', revenue: 2140000, expenses: 1400000 },
];

const chartConfig = {
  revenue: {
    label: 'Revenu',
    color: 'hsl(var(--chart-1))',
  },
  expenses: {
    label: 'Dépenses',
    color: 'hsl(var(--chart-2))',
  },
} satisfies ChartConfig;

export function RevenueChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Revenus vs Dépenses</CardTitle>
        <CardDescription>Comparaison des 6 derniers mois (en DZD)</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-80 w-full">
          <BarChart data={chartData} accessibilityLayer>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <YAxis
              tickFormatter={(value) => `${Number(value) / 1000000}M`}
              tickLine={false}
              axisLine={false}
              width={80}
            />
            <Tooltip
              cursor={false}
              content={<ChartTooltipContent
                formatter={(value) => new Intl.NumberFormat('fr-DZ', { style: 'currency', currency: 'DZD' }).format(Number(value))}
                indicator="dot"
              />}
            />
            <Legend content={<ChartLegendContent />} />
            <Bar dataKey="revenue" fill="var(--color-revenue)" radius={4} />
            <Bar dataKey="expenses" fill="var(--color-expenses)" radius={4} />
          </BarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
