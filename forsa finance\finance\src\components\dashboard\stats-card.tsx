import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import type { ReactNode } from 'react';

type StatsCardProps = {
  title: string;
  value: string;
  change?: string;
  icon?: ReactNode;
};

export function StatsCard({ title, value, change, icon }: StatsCardProps) {
  const isPositive = change && change.startsWith('+');
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p
            className={`text-xs text-muted-foreground ${
              isPositive ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {change} par rapport au mois dernier
          </p>
        )}
      </CardContent>
    </Card>
  );
}
