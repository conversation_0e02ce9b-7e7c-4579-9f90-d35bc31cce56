
'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '../ui/textarea';
import type { Invoice } from '@/data/invoices';
import { PlusCircle, Trash2, Info } from 'lucide-react';
import { Separator } from '../ui/separator';
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form';
import { products as availableProducts } from '@/data/products';
import { Combobox } from '@/components/ui/combobox';

type CreateInvoiceDialogProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onAddInvoice: (invoice: Omit<Invoice, 'id' | 'status' | 'number'>) => void;
};

const lineItemSchema = z.object({
  productId: z.coerce.number().min(1, 'Veuillez sélectionner un produit.'),
  description: z.string(),
  quantity: z.coerce.number().min(1, 'La quantité doit être au moins 1.'),
  unitPriceHT: z.coerce.number(),
  tvaRate: z.coerce.number(),
});

const invoiceFormSchema = z.object({
  clientName: z.string().min(1, 'Le nom du client est requis.'),
  clientNif: z.string().min(1, 'Le NIF du client est requis.'),
  clientAddress: z.string().min(1, "L'adresse du client est requise."),
  issueDate: z.string().min(1, "La date d'émission est requise."),
  dueDate: z.string().min(1, "La date d'échéance est requise."),
  items: z.array(lineItemSchema).min(1, 'La facture doit contenir au moins un article.'),
  timbre: z.coerce.number().min(0),
});

type InvoiceFormValues = z.infer<typeof invoiceFormSchema>;

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const productOptions = availableProducts.map(p => ({ value: p.id, label: `${p.name} (Stock: ${p.quantity})` }));

export function CreateInvoiceDialog({ open, onOpenChange, onAddInvoice }: CreateInvoiceDialogProps) {
    const form = useForm<InvoiceFormValues>({
        resolver: zodResolver(invoiceFormSchema),
        defaultValues: {
            clientName: '',
            clientNif: '',
            clientAddress: '',
            issueDate: new Date().toISOString().split('T')[0],
            dueDate: '',
            items: [{ productId: 0, description: '', quantity: 1, unitPriceHT: 0, tvaRate: 0.19 }],
            timbre: 100,
        },
    });

    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: 'items',
    });

    const watchedItems = form.watch('items');
    const watchedTimbre = form.watch('timbre');

    const [totals, setTotals] = useState({ totalHT: 0, totalTVA: 0, totalTTC: 0 });

    useEffect(() => {
        const ht = watchedItems.reduce((acc, line) => acc + (line.quantity * line.unitPriceHT), 0);
        const tva = watchedItems.reduce((acc, line) => acc + (line.quantity * line.unitPriceHT * line.tvaRate), 0);
        const ttc = ht + tva + (watchedTimbre || 0);
        setTotals({ totalHT: ht, totalTVA: tva, totalTTC: ttc });
    }, [watchedItems, watchedTimbre]);
    
    const handleProductSelect = (index: number, productId: number) => {
        const product = availableProducts.find(p => p.id === productId);
        if (product) {
            form.setValue(`items.${index}.description`, product.name);
            form.setValue(`items.${index}.unitPriceHT`, product.price);
            form.setValue(`items.${index}.tvaRate`, product.tvaRate);
        }
    };

    const handleSubmit = (values: InvoiceFormValues) => {
        onAddInvoice({
            ...values,
            totalHT: totals.totalHT,
            totalTVA: totals.totalTVA,
            totalTTC: totals.totalTTC,
            items: values.items.map((item, index) => ({ id: index + 1, ...item })),
        });
        form.reset();
        onOpenChange(false);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-5xl">
                <DialogHeader>
                    <DialogTitle>Créer une nouvelle facture</DialogTitle>
                    <DialogDescription>
                        Remplissez les informations ci-dessous. Les totaux sont calculés automatiquement.
                    </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(handleSubmit)} id="invoice-form" className="space-y-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 py-4">
                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Informations Client</h3>
                                <Separator />
                                <FormField control={form.control} name="clientName" render={({ field }) => (
                                    <FormItem><Label>Nom du client</Label><FormControl><Input placeholder="SARL Innova" {...field} /></FormControl><FormMessage /></FormItem>
                                )}/>
                                <FormField control={form.control} name="clientNif" render={({ field }) => (
                                    <FormItem><Label>NIF du client</Label><FormControl><Input placeholder="001234567891234" {...field} /></FormControl><FormMessage /></FormItem>
                                )}/>
                                <FormField control={form.control} name="clientAddress" render={({ field }) => (
                                    <FormItem><Label>Adresse du client</Label><FormControl><Textarea placeholder="123 Rue de la Liberté, Alger" {...field} /></FormControl><FormMessage /></FormItem>
                                )}/>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                     <FormField control={form.control} name="issueDate" render={({ field }) => (
                                        <FormItem><Label>Date d'émission</Label><FormControl><Input type="date" {...field} /></FormControl><FormMessage /></FormItem>
                                    )}/>
                                     <FormField control={form.control} name="dueDate" render={({ field }) => (
                                        <FormItem><Label>Date d'échéance</Label><FormControl><Input type="date" {...field} /></FormControl><FormMessage /></FormItem>
                                    )}/>
                                </div>
                            </div>

                            <div className="space-y-4">
                                <h3 className="text-lg font-medium">Lignes de la facture</h3>
                                <Separator />
                                <div className="max-h-72 overflow-y-auto pr-2 space-y-4">
                                    {fields.map((field, index) => {
                                        const unitPriceHT = watchedItems[index]?.unitPriceHT || 0;
                                        const tvaRate = watchedItems[index]?.tvaRate || 0;
                                        const unitPriceTTC = unitPriceHT * (1 + tvaRate);
                                        const quantity = watchedItems[index]?.quantity || 0;

                                        return (
                                        <div key={field.id} className="p-4 border rounded-lg space-y-4 relative">
                                            <Button type="button" variant="ghost" size="icon" className="absolute top-2 right-2" onClick={() => remove(index)}>
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                             <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <FormField control={form.control} name={`items.${index}.productId`} render={({ field }) => (
                                                    <FormItem className="md:col-span-3">
                                                        <Label>Produit</Label>
                                                        <FormControl>
                                                            <Combobox
                                                            options={productOptions}
                                                            value={field.value}
                                                            onChange={(value) => {
                                                                field.onChange(value);
                                                                handleProductSelect(index, value);
                                                            }}
                                                            placeholder="Chercher un produit..."
                                                            />
                                                        </FormControl>
                                                        <FormMessage />
                                                    </FormItem>
                                                )}/>
                                                <FormField control={form.control} name={`items.${index}.quantity`} render={({ field }) => (
                                                    <FormItem><Label>Quantité</Label><FormControl><Input type="number" className="text-right" {...field} onChange={e => field.onChange(e.target.valueAsNumber)} /></FormControl><FormMessage /></FormItem>
                                                )}/>
                                                <FormItem>
                                                    <Label>Prix U. TTC</Label>
                                                    <Input value={formatCurrency(unitPriceTTC)} readOnly className="text-right bg-muted/50" />
                                                </FormItem>
                                                <FormItem>
                                                    <Label>Total Ligne TTC</Label>
                                                    <Input value={formatCurrency(unitPriceTTC * quantity)} readOnly className="text-right bg-muted/50" />
                                                </FormItem>
                                            </div>
                                        </div>
                                    )})}
                                </div>
                                <Button type="button" variant="outline" size="sm" onClick={() => append({ productId: 0, description: '', quantity: 1, unitPriceHT: 0, tvaRate: 0.19 })}>
                                    <PlusCircle className="mr-2 h-4 w-4" />
                                    Ajouter une ligne
                                </Button>
                                <Separator />
                                <div className="space-y-2">
                                    <div className="flex justify-between items-center font-medium"><Label>Total HT</Label><span className="font-mono">{formatCurrency(totals.totalHT)} DZD</span></div>
                                    <div className="flex justify-between items-center font-medium"><Label>Total TVA</Label><span className="font-mono">{formatCurrency(totals.totalTVA)} DZD</span></div>
                                    <FormField control={form.control} name="timbre" render={({ field }) => (
                                        <FormItem className="flex justify-between items-center">
                                            <Label htmlFor="timbre" className="flex items-center gap-1">Timbre Fiscal <Info className="h-3 w-3 text-muted-foreground" /></Label>
                                            <FormControl><Input type="number" className="w-28 text-right font-mono" {...field} onChange={e => field.onChange(e.target.valueAsNumber)} /></FormControl>
                                        </FormItem>
                                    )}/>
                                    <Separator />
                                    <div className="flex justify-between items-center font-bold text-lg"><Label>Total TTC</Label><span className="font-mono">{formatCurrency(totals.totalTTC)} DZD</span></div>
                                </div>
                            </div>
                        </div>
                    </form>
                </Form>
                <DialogFooter>
                    <DialogClose asChild><Button type="button" variant="secondary">Annuler</Button></DialogClose>
                    <Button type="submit" form="invoice-form">Créer la facture</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}

    