
'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, PlusCircle, Printer, Download, Eye } from "lucide-react";
import { invoices as initialInvoices, type Invoice } from '@/data/invoices';
import { CreateInvoiceDialog } from './create-invoice-dialog';
import { InvoiceView } from './invoice-view';

const getStatusVariant = (status: 'Payée' | 'En attente' | 'En retard'): 'secondary' | 'default' | 'destructive' => {
  switch (status) {
    case 'Payée':
      return 'secondary';
    case 'En attente':
      return 'default';
    case 'En retard':
      return 'destructive';
  }
};

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};


export function InvoiceList() {
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [viewingInvoice, setViewingInvoice] = useState<Invoice | null>(null);
    const [invoices, setInvoices] = useState<Invoice[]>(initialInvoices);

    const handleAddInvoice = (newInvoiceData: Omit<Invoice, 'id' | 'status' | 'number'>) => {
        const newInvoice: Invoice = {
            ...newInvoiceData,
            id: invoices.length + 1,
            number: `FACT-${new Date().getFullYear()}-${String(invoices.length + 1).padStart(4, '0')}`,
            status: 'En attente',
        };
        setInvoices(prevInvoices => [newInvoice, ...prevInvoices]);
    };

    return (
        <>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Liste des Factures</CardTitle>
                    <CardDescription>Affichez et gérez les factures de vos clients.</CardDescription>
                </div>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Créer une facture
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Numéro</TableHead>
                            <TableHead>Client</TableHead>
                            <TableHead>Date d'émission</TableHead>
                            <TableHead className="text-right">Montant TTC</TableHead>
                            <TableHead>Statut</TableHead>
                            <TableHead><span className="sr-only">Actions</span></TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {invoices.map((invoice) => (
                            <TableRow key={invoice.id}>
                                <TableCell className="font-medium">{invoice.number}</TableCell>
                                <TableCell>{invoice.clientName}</TableCell>
                                <TableCell>{invoice.issueDate}</TableCell>
                                <TableCell className="text-right font-mono">{formatCurrency(invoice.totalTTC)} DZD</TableCell>
                                <TableCell>
                                    <Badge variant={getStatusVariant(invoice.status)}>
                                        {invoice.status}
                                    </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <span className="sr-only">Ouvrir le menu</span>
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                             <DropdownMenuItem onClick={() => setViewingInvoice(invoice)}>
                                                <Eye className="mr-2 h-4 w-4" />
                                                Voir la facture
                                            </DropdownMenuItem>
                                            <DropdownMenuItem>
                                                <Download className="mr-2 h-4 w-4" />
                                                Télécharger PDF
                                            </DropdownMenuItem>
                                            <DropdownMenuItem>
                                                <Printer className="mr-2 h-4 w-4" />
                                                Imprimer
                                            </DropdownMenuItem>
                                            <DropdownMenuItem>Modifier</DropdownMenuItem>
                                            <DropdownMenuItem className="text-destructive">Annuler</DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
        <CreateInvoiceDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} onAddInvoice={handleAddInvoice} />
        {viewingInvoice && (
            <InvoiceView
                invoice={viewingInvoice}
                open={!!viewingInvoice}
                onOpenChange={(open) => { if (!open) setViewingInvoice(null); }}
            />
        )}
        </>
    );
}
