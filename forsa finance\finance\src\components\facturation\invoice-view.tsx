
'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import type { Invoice } from '@/data/invoices';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, TableFooter } from '@/components/ui/table';
import { Separator } from '../ui/separator';
import { Button } from '../ui/button';
import { Printer } from 'lucide-react';

type InvoiceViewProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    invoice: Invoice;
};

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const ForsaLogo = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-10 w-10 text-primary"
  >
    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
    <path d="M2 17l10 5 10-5"></path>
    <path d="M2 12l10 5 10-5"></path>
  </svg>
);

export function InvoiceView({ open, onOpenChange, invoice }: InvoiceViewProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl p-0">
        <div className="p-8 space-y-8" id="invoice-print-area">
          {/* Header */}
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-4">
                <ForsaLogo />
                <div>
                    <h1 className="text-2xl font-bold font-headline">SARL ForsaTech</h1>
                    <p className="text-muted-foreground text-sm">Cité 1000 Logements, Bab Ezzouar, Alger</p>
                    <p className="text-muted-foreground text-sm">NIF: 001234567890123</p>
                </div>
            </div>
            <div className="text-right">
              <h2 className="text-3xl font-bold text-primary">FACTURE</h2>
              <p className="font-mono">{invoice.number}</p>
            </div>
          </div>

          {/* Client & Dates Info */}
          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-2">
                <h3 className="font-semibold">Facturé à :</h3>
                <p className="font-bold">{invoice.clientName}</p>
                <p className="text-sm text-muted-foreground">{invoice.clientAddress}</p>
                <p className="text-sm text-muted-foreground">NIF: {invoice.clientNif}</p>
            </div>
            <div className="space-y-2 text-right">
                <div className="grid grid-cols-2">
                    <span className="font-semibold">Date d'émission:</span>
                    <span>{invoice.issueDate}</span>
                </div>
                 <div className="grid grid-cols-2">
                    <span className="font-semibold">Date d'échéance:</span>
                    <span>{invoice.dueDate}</span>
                </div>
            </div>
          </div>

          {/* Invoice Lines */}
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/2">Description</TableHead>
                <TableHead className="text-right">Qté</TableHead>
                <TableHead className="text-right">Prix U. HT</TableHead>
                <TableHead className="text-right">TVA</TableHead>
                <TableHead className="text-right">Total HT</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoice.items.map((item) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.description}</TableCell>
                  <TableCell className="text-right font-mono">{item.quantity}</TableCell>
                  <TableCell className="text-right font-mono">{formatCurrency(item.unitPriceHT)}</TableCell>
                  <TableCell className="text-right font-mono">{item.tvaRate * 100}%</TableCell>
                  <TableCell className="text-right font-mono">{formatCurrency(item.quantity * item.unitPriceHT)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* Totals */}
          <div className="flex justify-end">
            <div className="w-full max-w-sm space-y-2">
                <div className="flex justify-between">
                    <span>Total HT</span>
                    <span className="font-mono">{formatCurrency(invoice.totalHT)} DZD</span>
                </div>
                 <div className="flex justify-between">
                    <span>Total TVA</span>
                    <span className="font-mono">{formatCurrency(invoice.totalTVA)} DZD</span>
                </div>
                 <div className="flex justify-between">
                    <span>Timbre Fiscal</span>
                    <span className="font-mono">{formatCurrency(invoice.timbre)} DZD</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                    <span>Total TTC</span>
                    <span className="font-mono">{formatCurrency(invoice.totalTTC)} DZD</span>
                </div>
            </div>
          </div>

          <Separator />
          {/* Footer */}
          <div className="text-center text-xs text-muted-foreground">
            <p>Arrêtée la présente facture à la somme de : (montant en lettres ici)</p>
            <p>Merci de votre confiance.</p>
          </div>
        </div>
        <DialogFooter className="p-4 bg-muted/50 border-t">
          <Button variant="outline" onClick={() => window.print()}>
            <Printer className="mr-2 h-4 w-4" />
            Imprimer
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
