'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Printer, FileDown } from "lucide-react";

const dasData = [
    { employee: "<PERSON>", annualGross: 1440000, annualNet: 1278000, annualIRG: 162000 },
    { employee: "<PERSON>ima Zohra", annualGross: 1140000, annualNet: 1035000, annualIRG: 105000 },
    { employee: "<PERSON><PERSON>", annualGross: 960000, annualNet: 882000, annualIRG: 78000 },
];

const formatCurrency = (amount: number) => {
    if (amount === 0) return '0.00';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

export function DasDeclaration() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Déclaration Annuelle des Salaires (DAS)</CardTitle>
        <CardDescription>Générez l'état 9421 pour la déclaration annuelle des traitements et salaires.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
            <Label htmlFor="das-year">Année de déclaration</Label>
            <Input id="das-year" type="number" defaultValue={new Date().getFullYear() - 1} className="w-[150px]" />
        </div>
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Employé</TableHead>
                    <TableHead className="text-right">Brut Annuel</TableHead>
                    <TableHead className="text-right">IRG Annuel</TableHead>
                    <TableHead className="text-right">Net Annuel</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {dasData.map((item) => (
                    <TableRow key={item.employee}>
                        <TableCell className="font-medium">{item.employee}</TableCell>
                        <TableCell className="text-right font-mono">{formatCurrency(item.annualGross)}</TableCell>
                        <TableCell className="text-right font-mono">{formatCurrency(item.annualIRG)}</TableCell>
                        <TableCell className="text-right font-mono text-accent">{formatCurrency(item.annualNet)}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
      </CardContent>
       <CardFooter className="flex justify-end gap-2">
        <Button variant="outline">
            <FileDown className="mr-2 h-4 w-4" />
            Exporter (Fichier Texte)
        </Button>
        <Button>
            <Printer className="mr-2 h-4 w-4" />
            Imprimer l'état 9421
        </Button>
      </CardFooter>
    </Card>
  )
}
