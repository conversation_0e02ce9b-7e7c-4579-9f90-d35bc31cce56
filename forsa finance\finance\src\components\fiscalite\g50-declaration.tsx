'use client';

import { Card, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Printer, Calculator } from "lucide-react";

export function G50Declaration() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Déclaration G50</CardTitle>
        <CardDescription>Remplissez les informations pour la déclaration mensuelle des impôts et taxes.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
                <Label htmlFor="period-month">Mois</Label>
                <Select defaultValue="10">
                    <SelectTrigger id="period-month">
                        <SelectValue placeholder="Sélectionnez un mois" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="1">Janvier</SelectItem>
                        <SelectItem value="2">Février</SelectItem>
                        <SelectItem value="3">Mars</SelectItem>
                        <SelectItem value="4">Avril</SelectItem>
                        <SelectItem value="5">Mai</SelectItem>
                        <SelectItem value="6">Juin</SelectItem>
                        <SelectItem value="7">Juillet</SelectItem>
                        <SelectItem value="8">Août</SelectItem>
                        <SelectItem value="9">Septembre</SelectItem>
                        <SelectItem value="10">Octobre</SelectItem>
                        <SelectItem value="11">Novembre</SelectItem>
                        <SelectItem value="12">Décembre</SelectItem>
                    </SelectContent>
                </Select>
            </div>
            <div className="space-y-2">
                <Label htmlFor="period-year">Année</Label>
                <Input id="period-year" type="number" defaultValue={new Date().getFullYear()} />
            </div>
        </div>

        <div className="space-y-4 rounded-lg border p-4">
            <h3 className="font-semibold">TVA (Taxe sur la Valeur Ajoutée)</h3>
             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label>TVA collectée</Label>
                    <Input type="number" placeholder="0.00" />
                </div>
                <div className="space-y-2">
                    <Label>TVA déductible</Label>
                    <Input type="number" placeholder="0.00" />
                </div>
            </div>
        </div>

         <div className="space-y-4 rounded-lg border p-4">
            <h3 className="font-semibold">TAP (Taxe sur l'Activité Professionnelle)</h3>
             <div className="space-y-2">
                <Label>Chiffre d'affaires imposable</Label>
                <Input type="number" placeholder="0.00" />
            </div>
        </div>
        
         <div className="space-y-4 rounded-lg border p-4">
            <h3 className="font-semibold">IRG Salaires (Impôt sur le Revenu Global)</h3>
             <div className="space-y-2">
                <Label>Montant total de l'IRG retenu</Label>
                <Input type="number" placeholder="0.00" />
            </div>
        </div>

      </CardContent>
      <CardFooter className="flex justify-between">
        <Button>
            <Calculator className="mr-2 h-4 w-4"/>
            Calculer les Totaux
        </Button>
        <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Générer le PDF
        </Button>
      </CardFooter>
    </Card>
  )
}
