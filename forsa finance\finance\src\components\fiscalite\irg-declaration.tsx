'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, TableFooter } from "@/components/ui/table";
import { Printer } from "lucide-react";

const irgData = [
    { employee: "<PERSON>", grossSalary: 120000, irg: 13500 },
    { employee: "Fatima Zohra", grossSalary: 95000, irg: 8750 },
    { employee: "<PERSON><PERSON> Belkacem", grossSalary: 80000, irg: 6500 },
];

const totalIRG = irgData.reduce((sum, item) => sum + item.irg, 0);

const formatCurrency = (amount: number) => {
    if (amount === 0) return '0.00';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};


export function IrgDeclaration() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Déclaration de l'IRG Salaires</CardTitle>
        <CardDescription>Récapitulatif de l'Impôt sur le Revenu Global retenu sur les salaires du mois.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Employé</TableHead>
                    <TableHead className="text-right">Salaire Brut (DZD)</TableHead>
                    <TableHead className="text-right">IRG Retenu (DZD)</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {irgData.map((item) => (
                    <TableRow key={item.employee}>
                        <TableCell className="font-medium">{item.employee}</TableCell>
                        <TableCell className="text-right font-mono">{formatCurrency(item.grossSalary)}</TableCell>
                        <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.irg)}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
            <TableFooter>
                <TableRow>
                    <TableCell colSpan={2} className="font-bold text-lg">Total IRG à verser</TableCell>
                    <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totalIRG)}</TableCell>
                </TableRow>
            </TableFooter>
        </Table>
      </CardContent>
       <CardFooter className="flex justify-end">
        <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Imprimer le récapitulatif
        </Button>
      </CardFooter>
    </Card>
  )
}
