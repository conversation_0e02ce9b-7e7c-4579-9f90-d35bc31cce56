'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Printer, Calculator } from "lucide-react";

export function TvaDeclaration() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Déclaration de TVA</CardTitle>
        <CardDescription>Calculez et préparez votre déclaration de la Taxe sur la Valeur Ajoutée.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4 rounded-lg border p-4">
            <h3 className="font-semibold">TVA Collectée (sur ventes)</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="ca-19">Chi<PERSON>re d'affaires à 19%</Label>
                    <Input id="ca-19" type="number" placeholder="0.00" />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="ca-9">Chiffre d'affaires à 9%</Label>
                    <Input id="ca-9" type="number" placeholder="0.00" />
                </div>
            </div>
        </div>

         <div className="space-y-4 rounded-lg border p-4">
            <h3 className="font-semibold">TVA Déductible (sur achats)</h3>
             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="tva-biens-services">TVA sur biens et services</Label>
                    <Input id="tva-biens-services" type="number" placeholder="0.00" />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="tva-immo">TVA sur immobilisations</Label>
                    <Input id="tva-immo" type="number" placeholder="0.00" />
                </div>
            </div>
            <div className="space-y-2 mt-4">
                <Label htmlFor="credit-report">Crédit de TVA du mois précédent</Label>
                <Input id="credit-report" type="number" placeholder="0.00" />
            </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button>
            <Calculator className="mr-2 h-4 w-4"/>
            Calculer la TVA
        </Button>
        <Button variant="outline">
            <Printer className="mr-2 h-4 w-4" />
            Générer le PDF
        </Button>
      </CardFooter>
    </Card>
  )
}
