'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { useToast } from '@/hooks/use-toast';
import { localDatabase } from '@/lib/database';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Calendar, 
  Download,
  FileText,
  PieChart
} from 'lucide-react';

interface Employee {
  id: string;
  first_name: string;
  last_name: string;
  position: string;
  department: string;
  salary: number;
  hire_date: string;
  status: 'active' | 'inactive';
}

interface PayrollRecord {
  id: string;
  employee_id: string;
  employee_name: string;
  period: string;
  gross_salary: number;
  net_salary: number;
  deductions: number;
  created_at: string;
}

export function HRReports() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [payrollRecords, setPayrollRecords] = useState<PayrollRecord[]>([]);
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const employeesData = await localDatabase.getEmployees();
      setEmployees(employeesData);
      
      // Simuler des données de paie (à remplacer par de vraies données)
      const mockPayrollRecords: PayrollRecord[] = employeesData.map(emp => ({
        id: `PAY-${emp.id}-${new Date().getMonth() + 1}`,
        employee_id: emp.id,
        employee_name: `${emp.first_name} ${emp.last_name}`,
        period: `${new Date().getMonth() + 1}/${new Date().getFullYear()}`,
        gross_salary: emp.salary + (emp.salary * 0.1), // Avec indemnités
        net_salary: emp.salary * 0.75, // Après retenues
        deductions: emp.salary * 0.25,
        created_at: new Date().toISOString()
      }));
      setPayrollRecords(mockPayrollRecords);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      toast({
        title: "Erreur",
        description: "Impossible de charger les données",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }) + ' DZD';
  };

  const getFilteredEmployees = () => {
    if (selectedDepartment === 'all') {
      return employees;
    }
    return employees.filter(emp => emp.department === selectedDepartment);
  };

  const getDepartments = () => {
    return [...new Set(employees.map(emp => emp.department))];
  };

  const calculateStats = () => {
    const filteredEmployees = getFilteredEmployees();
    const activeEmployees = filteredEmployees.filter(emp => emp.status === 'active');
    
    const totalSalaries = activeEmployees.reduce((sum, emp) => sum + emp.salary, 0);
    const averageSalary = activeEmployees.length > 0 ? totalSalaries / activeEmployees.length : 0;
    
    const totalGross = payrollRecords.reduce((sum, record) => sum + record.gross_salary, 0);
    const totalNet = payrollRecords.reduce((sum, record) => sum + record.net_salary, 0);
    const totalDeductions = payrollRecords.reduce((sum, record) => sum + record.deductions, 0);

    return {
      totalEmployees: filteredEmployees.length,
      activeEmployees: activeEmployees.length,
      totalSalaries,
      averageSalary,
      totalGross,
      totalNet,
      totalDeductions
    };
  };

  const getDepartmentStats = () => {
    const departments = getDepartments();
    return departments.map(dept => {
      const deptEmployees = employees.filter(emp => emp.department === dept && emp.status === 'active');
      const totalSalary = deptEmployees.reduce((sum, emp) => sum + emp.salary, 0);
      return {
        department: dept,
        count: deptEmployees.length,
        totalSalary,
        averageSalary: deptEmployees.length > 0 ? totalSalary / deptEmployees.length : 0
      };
    });
  };

  const getAgeDistribution = () => {
    const currentYear = new Date().getFullYear();
    const ageGroups = {
      '20-30': 0,
      '31-40': 0,
      '41-50': 0,
      '51+': 0
    };

    employees.forEach(emp => {
      const yearsOfService = currentYear - new Date(emp.hire_date).getFullYear();
      const estimatedAge = 25 + yearsOfService; // Estimation basée sur l'ancienneté
      
      if (estimatedAge <= 30) ageGroups['20-30']++;
      else if (estimatedAge <= 40) ageGroups['31-40']++;
      else if (estimatedAge <= 50) ageGroups['41-50']++;
      else ageGroups['51+']++;
    });

    return ageGroups;
  };

  const exportReport = (type: string) => {
    const stats = calculateStats();
    const deptStats = getDepartmentStats();
    
    let reportData = '';
    
    switch (type) {
      case 'payroll':
        reportData = `Rapport de Paie - ${new Date().toLocaleDateString('fr-DZ')}\n\n`;
        reportData += `Statistiques Générales:\n`;
        reportData += `- Employés actifs: ${stats.activeEmployees}\n`;
        reportData += `- Masse salariale brute: ${formatCurrency(stats.totalGross)}\n`;
        reportData += `- Masse salariale nette: ${formatCurrency(stats.totalNet)}\n`;
        reportData += `- Total retenues: ${formatCurrency(stats.totalDeductions)}\n\n`;
        
        reportData += `Détail par employé:\n`;
        payrollRecords.forEach(record => {
          reportData += `${record.employee_name}: Brut ${formatCurrency(record.gross_salary)}, Net ${formatCurrency(record.net_salary)}\n`;
        });
        break;
        
      case 'department':
        reportData = `Rapport par Département - ${new Date().toLocaleDateString('fr-DZ')}\n\n`;
        deptStats.forEach(dept => {
          reportData += `${dept.department}:\n`;
          reportData += `- Effectif: ${dept.count}\n`;
          reportData += `- Masse salariale: ${formatCurrency(dept.totalSalary)}\n`;
          reportData += `- Salaire moyen: ${formatCurrency(dept.averageSalary)}\n\n`;
        });
        break;
        
      case 'employees':
        reportData = `Liste des Employés - ${new Date().toLocaleDateString('fr-DZ')}\n\n`;
        employees.forEach(emp => {
          reportData += `${emp.first_name} ${emp.last_name} - ${emp.position} (${emp.department})\n`;
          reportData += `Salaire: ${formatCurrency(emp.salary)} - Statut: ${emp.status}\n`;
          reportData += `Embauché le: ${new Date(emp.hire_date).toLocaleDateString('fr-DZ')}\n\n`;
        });
        break;
    }
    
    // Créer et télécharger le fichier
    const blob = new Blob([reportData], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rapport-${type}-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Export réussi",
      description: `Le rapport ${type} a été téléchargé`,
    });
  };

  const stats = calculateStats();
  const deptStats = getDepartmentStats();
  const ageDistribution = getAgeDistribution();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p>Chargement des rapports...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Rapports RH</h2>
          <p className="text-gray-600">Analyses et statistiques des ressources humaines</p>
        </div>
        <div className="flex space-x-2">
          <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Tous les départements" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les départements</SelectItem>
              {getDepartments().map(dept => (
                <SelectItem key={dept} value={dept}>{dept}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="current-month">Mois actuel</SelectItem>
              <SelectItem value="last-month">Mois dernier</SelectItem>
              <SelectItem value="current-year">Année actuelle</SelectItem>
              <SelectItem value="last-year">Année dernière</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Employés Actifs</p>
                <p className="text-2xl font-bold text-gray-900">{stats.activeEmployees}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Masse Salariale</p>
                <p className="text-lg font-bold text-gray-900">{formatCurrency(stats.totalSalaries)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Salaire Moyen</p>
                <p className="text-lg font-bold text-gray-900">{formatCurrency(stats.averageSalary)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <PieChart className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Départements</p>
                <p className="text-2xl font-bold text-gray-900">{getDepartments().length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Rapports détaillés */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Répartition par département */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="w-5 h-5 mr-2" />
              Répartition par Département
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {deptStats.map((dept, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{dept.department}</p>
                    <p className="text-sm text-gray-600">{dept.count} employé(s)</p>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">{formatCurrency(dept.totalSalary)}</p>
                    <p className="text-sm text-gray-600">Moy: {formatCurrency(dept.averageSalary)}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Masse salariale */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              Analyse de la Masse Salariale
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                <span className="font-medium">Salaire Brut Total</span>
                <span className="font-bold text-green-700">{formatCurrency(stats.totalGross)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                <span className="font-medium">Salaire Net Total</span>
                <span className="font-bold text-blue-700">{formatCurrency(stats.totalNet)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                <span className="font-medium">Total Retenues</span>
                <span className="font-bold text-red-700">{formatCurrency(stats.totalDeductions)}</span>
              </div>
              <Separator />
              <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Charges Patronales (26.5%)</span>
                <span className="font-bold text-gray-700">{formatCurrency(stats.totalGross * 0.265)}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                <span className="font-medium">Coût Total Employeur</span>
                <span className="font-bold text-purple-700">{formatCurrency(stats.totalGross * 1.265)}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Actions d'export */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Exporter les Rapports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button onClick={() => exportReport('payroll')} className="flex items-center justify-center">
              <Download className="w-4 h-4 mr-2" />
              Rapport de Paie
            </Button>
            <Button onClick={() => exportReport('department')} variant="outline" className="flex items-center justify-center">
              <Download className="w-4 h-4 mr-2" />
              Rapport par Département
            </Button>
            <Button onClick={() => exportReport('employees')} variant="outline" className="flex items-center justify-center">
              <Download className="w-4 h-4 mr-2" />
              Liste des Employés
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Tableau récapitulatif */}
      <Card>
        <CardHeader>
          <CardTitle>Récapitulatif des Paies</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Employé</TableHead>
                <TableHead>Période</TableHead>
                <TableHead>Salaire Brut</TableHead>
                <TableHead>Retenues</TableHead>
                <TableHead>Salaire Net</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payrollRecords.slice(0, 10).map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="font-medium">{record.employee_name}</TableCell>
                  <TableCell>{record.period}</TableCell>
                  <TableCell>{formatCurrency(record.gross_salary)}</TableCell>
                  <TableCell className="text-red-600">{formatCurrency(record.deductions)}</TableCell>
                  <TableCell className="font-medium text-green-600">{formatCurrency(record.net_salary)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
