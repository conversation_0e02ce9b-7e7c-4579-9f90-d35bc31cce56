
'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import {
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarContent,
  SidebarFooter,
  SidebarMenuSub,
  SidebarMenuSubButton,
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  ScanSearch,
  Landmark,
  ArrowLeftRight,
  Settings,
  HelpCircle,
  FileDigit,
  Users,
  ReceiptText,
  Boxes,
  Contact,
} from 'lucide-react';

const ForsaLogo = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    className="h-7 w-7 text-primary"
  >
    <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
    <path d="M2 17l10 5 10-5"></path>
    <path d="M2 12l10 5 10-5"></path>
  </svg>
);

const menuItems = [
  { href: '/dashboard', label: 'Tableau de bord', icon: LayoutDashboard },
  { href: '/ressources-humaines', label: 'Ressources Humaines', icon: Users },
  { href: '/facturation', label: 'Facturation', icon: ReceiptText },
  { href: '/stock', label: 'Gestion de Stock', icon: Boxes },
  { href: '/clients', label: 'Clients', icon: Contact, group: 'Tiers' },
  { href: '/fournisseurs', label: 'Fournisseurs', icon: Contact, group: 'Tiers' },
  { href: '/analyse', label: 'Analyse IA', icon: ScanSearch },
  { href: '/comptabilite', label: 'Comptabilité', icon: FileDigit },
  { href: '/fiscalite', label: 'Fiscalité', icon: Landmark },
  { href: '/tresorerie', label: 'Trésorerie', icon: ArrowLeftRight },
  { href: '/parametres', label: 'Paramètres', icon: Settings },
];

const groupedMenuItems = menuItems.reduce((acc, item) => {
  const group = item.group || item.label;
  if (!acc[group]) {
    acc[group] = [];
  }
  acc[group].push(item);
  return acc;
}, {} as Record<string, typeof menuItems>);


export function SidebarNav() {
  const pathname = usePathname();

  const mainLinks = menuItems.filter(item => !item.group);
  const tiersLinks = menuItems.filter(item => item.group === 'Tiers');

  return (
    <>
      <SidebarHeader>
        <div className="flex items-center gap-2.5">
          <ForsaLogo />
          <span className="text-xl font-semibold font-headline text-sidebar-foreground">Forsa</span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {mainLinks.filter(item => ['Tableau de bord', 'Ressources Humaines', 'Facturation', 'Gestion de Stock'].includes(item.label)).map((item) => (
             <SidebarMenuItem key={item.href}>
                <SidebarMenuButton asChild isActive={pathname.startsWith(item.href)} tooltip={item.label}>
                  <Link href={item.href}>
                    <>
                      <item.icon />
                      <span>{item.label}</span>
                    </>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
          ))}

          {/* Tiers Group */}
          <SidebarMenuItem>
            <SidebarMenuButton asChild isActive={pathname.startsWith('/clients') || pathname.startsWith('/fournisseurs')} tooltip="Tiers">
              <Link href="/clients">
                <>
                  <Contact />
                  <span>Tiers</span>
                </>
              </Link>
            </SidebarMenuButton>
            <SidebarMenuSub>
                <SidebarMenuItem>
                    <SidebarMenuSubButton asChild isActive={pathname.startsWith('/clients')}>
                        <Link href="/clients">Clients</Link>
                    </SidebarMenuSubButton>
                </SidebarMenuItem>
                  <SidebarMenuItem>
                    <SidebarMenuSubButton asChild isActive={pathname.startsWith('/fournisseurs')}>
                        <Link href="/fournisseurs">Fournisseurs</Link>
                    </SidebarMenuSubButton>
                </SidebarMenuItem>
            </SidebarMenuSub>
          </SidebarMenuItem>
          
           {mainLinks.filter(item => !['Tableau de bord', 'Ressources Humaines', 'Facturation', 'Gestion de Stock'].includes(item.label)).map((item) => (
             <SidebarMenuItem key={item.href}>
                <SidebarMenuButton asChild isActive={pathname.startsWith(item.href)} tooltip={item.label}>
                  <Link href={item.href}>
                    <>
                      <item.icon />
                      <span>{item.label}</span>
                    </>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
             <SidebarMenuButton asChild tooltip="Aide et Support">
                <Link href="#">
                  <>
                    <HelpCircle />
                    <span>Aide & Support</span>
                  </>
                </Link>
              </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </>
  );
}
