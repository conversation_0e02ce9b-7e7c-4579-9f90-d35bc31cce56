'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { calculatePayroll, validatePayrollInput, type PayrollInput, type PayrollOutput } from '@/lib/payroll-calculations';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calculator, Printer, Download } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { employees as staticEmployees, type Employee } from '@/data/employees';
import { localDatabase } from '@/lib/database';
import { Separator } from '@/components/ui/separator';
// import { SimplePayslip } from './simple-payslip';

const formSchema = z.object({
  employeeId: z.string().min(1, 'Veuillez sélectionner un employé'),
  basicSalary: z.coerce.number().min(20000, 'Le salaire de base doit être au moins égal au SNMG (20,000 DZD)'),
  workingDays: z.coerce.number().min(1).max(31, 'Le nombre de jours travaillés doit être entre 1 et 31'),
  standardWorkingDays: z.coerce.number().min(1).max(31, 'Le nombre de jours ouvrables doit être entre 1 et 31'),
  overtimeHours: z.coerce.number().min(0).optional(),
  transportAllowance: z.coerce.number().min(0).optional(),
  mealAllowance: z.coerce.number().min(0).optional(),
  housingAllowance: z.coerce.number().min(0).optional(),
  familyAllowance: z.coerce.number().min(0).optional(),
  otherAllowance: z.coerce.number().min(0).optional(),
  unpaidDays: z.coerce.number().min(0).optional(),
  sickDays: z.coerce.number().min(0).optional(),
  socialSecurityNumber: z.string().optional(),
  month: z.coerce.number().min(1).max(12),
  year: z.coerce.number().min(2020).max(new Date().getFullYear() + 1),
});

type FormValues = z.infer<typeof formSchema>;

const formatCurrency = (amount: number) => {
  return amount.toLocaleString('fr-DZ', { 
    style: 'decimal', 
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  }) + ' DZD';
};

const getMonthName = (month: number) => {
  const months = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];
  return months[month - 1];
};

interface PayrollCalculatorProps {
  selectedEmployee?: Employee | null;
}

export function PayrollCalculator({ selectedEmployee: propSelectedEmployee }: PayrollCalculatorProps) {
  const [payrollResult, setPayrollResult] = useState<PayrollOutput | null>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loadingEmployees, setLoadingEmployees] = useState(true);
  const { toast } = useToast();

  // Informations de l'entreprise par défaut
  const company = {
    name: 'Entreprise Demo SARL',
    address: '123 Rue de l\'Indépendance, Alger 16000, Algérie',
    phone: '+213 21 XX XX XX',
    email: '<EMAIL>',
    nif: '123456789012345',
    rc: '16/00-1234567'
  };

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employeeId: propSelectedEmployee?.id?.toString() || '',
      basicSalary: propSelectedEmployee?.salary || 25000,
      workingDays: 22,
      standardWorkingDays: 22,
      overtimeHours: 0,
      transportAllowance: 3000, // Indemnité transport standard
      mealAllowance: 2000,      // Indemnité panier standard
      housingAllowance: 0,
      familyAllowance: 1500,    // Allocation familiale standard
      otherAllowance: 0,
      unpaidDays: 0,
      sickDays: 0,
      socialSecurityNumber: '',
      month: new Date().getMonth() + 1,
      year: new Date().getFullYear(),
    },
  });

  const selectedEmployee = employees.find(emp => emp.id === form.watch('employeeId'));

  // Charger les employés depuis la base de données locale
  useEffect(() => {
    const loadEmployees = () => {
      try {
        setLoadingEmployees(true);
        const employeesData = localDatabase.findAll('employees');
        const formattedEmployees = employeesData.map(emp => ({
          id: emp.id?.toString() || '',
          name: `${emp.first_name} ${emp.last_name}`,
          first_name: emp.first_name,
          last_name: emp.last_name,
          email: emp.email,
          role: emp.position || '',
          position: emp.position,
          department: emp.department,
          salary: emp.salary,
          hireDate: emp.hire_date,
          status: emp.status,
          avatar: 'https://placehold.co/100x100',
          socialSecurityNumber: emp.nss || emp.socialSecurityNumber,
        }));
        setEmployees(formattedEmployees);
        console.log('Employés chargés dans PayrollCalculator:', formattedEmployees);
      } catch (error) {
        console.error('Erreur lors du chargement des employés:', error);
        // Fallback vers les données statiques
        setEmployees(staticEmployees);
      } finally {
        setLoadingEmployees(false);
      }
    };

    loadEmployees();
  }, []);

  // Mettre à jour le formulaire quand un employé est sélectionné depuis la liste
  useEffect(() => {
    if (propSelectedEmployee) {
      form.setValue('employeeId', propSelectedEmployee.id?.toString() || '');
      form.setValue('basicSalary', propSelectedEmployee.salary || 0);
    }
  }, [propSelectedEmployee, form]);

  const onSubmit = async (values: FormValues) => {
    setIsCalculating(true);
    
    try {
      const payrollInput: PayrollInput = {
        employeeId: values.employeeId,
        basicSalary: values.basicSalary,
        workingDays: values.workingDays,
        standardWorkingDays: values.standardWorkingDays,
        overtimeHours: values.overtimeHours || 0,
        allowances: {
          transport: values.transportAllowance || 0,
          meal: values.mealAllowance || 0,
          housing: values.housingAllowance || 0,
          family: values.familyAllowance || 0,
          other: values.otherAllowance || 0,
        },
        absences: {
          unpaidDays: values.unpaidDays || 0,
          sickDays: values.sickDays || 0,
        },
        period: {
          month: values.month,
          year: values.year,
        },
      };

      // Récupérer les informations de l'employé
      const employee = employees.find(emp => emp.id === values.employeeId);
      if (!employee) {
        toast({
          title: "Erreur",
          description: "Employé non trouvé",
          variant: "destructive",
        });
        return;
      }

      // Calcul avec barème IRG 2025 correct
      const grossSalary = values.basicSalary + (values.transportAllowance || 0) + (values.mealAllowance || 0) + (values.familyAllowance || 0) + (values.housingAllowance || 0) + (values.otherAllowance || 0);
      const cnasEmployee = grossSalary * 0.09;

      // Calcul IRG selon barème 2025 (annuel converti en mensuel)
      const annualGrossSalary = grossSalary * 12;
      const annualCnas = cnasEmployee * 12;
      const annualAbatement = Math.min(annualGrossSalary * 0.10, 12000); // 10% plafonné à 12000 DZD/an
      const annualTaxableIncome = Math.max(0, annualGrossSalary - annualCnas - annualAbatement);

      let annualIRG = 0;

      // Barème IRG 2025 progressif
      if (annualTaxableIncome > 240000) {
        // Tranche 1: 240 001 à 480 000 DZD - 23%
        const tranche1 = Math.min(annualTaxableIncome - 240000, 240000);
        annualIRG += tranche1 * 0.23;

        if (annualTaxableIncome > 480000) {
          // Tranche 2: 480 001 à 960 000 DZD - 27%
          const tranche2 = Math.min(annualTaxableIncome - 480000, 480000);
          annualIRG += tranche2 * 0.27;

          if (annualTaxableIncome > 960000) {
            // Tranche 3: 960 001 à 1 920 000 DZD - 30%
            const tranche3 = Math.min(annualTaxableIncome - 960000, 960000);
            annualIRG += tranche3 * 0.30;

            if (annualTaxableIncome > 1920000) {
              // Tranche 4: au-dessus de 1 920 000 DZD - 35%
              const tranche4 = annualTaxableIncome - 1920000;
              annualIRG += tranche4 * 0.35;
            }
          }
        }
      }

      const irgAmount = Math.round(annualIRG / 12); // Conversion en mensuel
      const netSalary = grossSalary - cnasEmployee - irgAmount;

      const result: PayrollOutput = {
        employee: {
          id: employee.id?.toString() || '',
          name: employee.name || `${employee.first_name || ''} ${employee.last_name || ''}`,
          position: employee.position || employee.role || '',
          socialSecurityNumber: values.socialSecurityNumber || employee.socialSecurityNumber || 'N/A',
        },
        period: {
          month: values.month,
          year: values.year,
          workingDays: values.standardWorkingDays,
          workedDays: values.workingDays
        },
        earnings: {
          basicSalary: values.basicSalary,
          overtime: values.overtimeHours ? values.basicSalary * 0.01 * (values.overtimeHours || 0) : 0,
          allowances: {
            transport: values.transportAllowance || 0,
            meal: values.mealAllowance || 0,
            housing: values.housingAllowance || 0,
            family: values.familyAllowance || 0,
            other: values.otherAllowance || 0,
            total: (values.transportAllowance || 0) + (values.mealAllowance || 0) + (values.familyAllowance || 0) + (values.housingAllowance || 0) + (values.otherAllowance || 0)
          },
          grossSalary: grossSalary
        },
        deductions: {
          cnas: {
            employee: cnasEmployee,
            employer: grossSalary * 0.25,
            rate: 0.09
          },
          irg: {
            amount: irgAmount,
            rate: annualTaxableIncome > 0 ? (annualIRG / annualTaxableIncome * 100) : 0,
            taxableIncome: Math.round(annualTaxableIncome / 12),
          },
          other: 0,
          totalDeductions: cnasEmployee + irgAmount
        },
        netSalary: netSalary,
        employerCharges: {
          cnas: grossSalary * 0.25,
          cacobatph: grossSalary * 0.015,
          total: grossSalary * 0.265
        },
        totalCost: grossSalary + (grossSalary * 0.265)
      };

      setPayrollResult(result);

      toast({
        title: "Calcul terminé",
        description: "Le bulletin de paie a été généré avec succès.",
      });
    } catch (error) {
      console.error('Erreur lors du calcul de la paie:', error);
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue lors du calcul.",
        variant: "destructive",
      });
    } finally {
      setIsCalculating(false);
    }
  };

  const handlePrint = () => {
    try {
      // Créer une nouvelle fenêtre pour l'impression
      const printWindow = window.open('', '_blank', 'width=800,height=600');
      if (!printWindow) {
        alert('Veuillez autoriser les pop-ups pour imprimer');
        return;
      }

      // Générer le HTML de la fiche de paie pour impression
      const printHTML = generatePrintablePayslip();

      printWindow.document.write(printHTML);
      printWindow.document.close();

      // Attendre un court délai puis imprimer
      setTimeout(() => {
        try {
          printWindow.print();
          setTimeout(() => {
            printWindow.close();
          }, 100);
        } catch (error) {
          console.error('Erreur lors de l\'impression:', error);
          printWindow.close();
        }
      }, 500);
    } catch (error) {
      console.error('Erreur lors de la génération de l\'impression:', error);
      alert('Erreur lors de la génération de l\'impression');
    }
  };

  const generatePrintablePayslip = () => {
    if (!payrollResult) return '';

    return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bulletin de Paie - ${payrollResult.employee.name}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        @page {
            size: A4;
            margin: 15mm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #333;
            background: white;
            padding: 0;
            margin: 0;
        }
        .payslip-container {
            width: 100%;
            max-width: 180mm;
            margin: 0 auto;
            background: white;
            page-break-inside: avoid;
        }
        .header {
            background: #1e40af;
            color: white;
            padding: 12px 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .company-info h1 { font-size: 16px; margin-bottom: 6px; }
        .company-info p { font-size: 9px; margin-bottom: 3px; opacity: 0.9; }
        .payslip-title { text-align: right; }
        .payslip-title h2 { font-size: 14px; margin-bottom: 6px; }
        .payslip-title p { font-size: 9px; }
        .section { margin-bottom: 12px; }
        .section h3 {
            color: #1e40af;
            font-size: 12px;
            margin-bottom: 8px;
            padding-bottom: 3px;
            border-bottom: 1px solid #1e40af;
        }
        .employee-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 12px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            padding: 2px 0;
            border-bottom: 1px dotted #ddd;
        }
        .info-row label { font-weight: 600; color: #666; font-size: 10px; }
        .info-row span { font-weight: 500; font-size: 10px; }
        .salary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 12px;
            font-size: 10px;
        }
        .salary-table th, .salary-table td {
            border: 1px solid #ddd;
            padding: 4px 6px;
            text-align: left;
        }
        .salary-table th {
            background: #f8f9fa;
            font-weight: bold;
            font-size: 9px;
        }
        .salary-table .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
            font-size: 10px;
        }
        .salary-table .section-header td {
            background: #e9ecef;
            font-weight: bold;
            color: #1e40af;
        }
        .salary-table .total-row {
            background: #f8f9fa;
            font-weight: bold;
        }
        .salary-table .gains-total {
            background: #dbeafe;
            color: #1e40af;
        }
        .salary-table .deductions-total {
            background: #fee2e2;
            color: #dc2626;
        }
        .salary-table .net-pay {
            background: #d1fae5;
            color: #059669;
            font-size: 14px;
            font-weight: bold;
        }
        .irg-detail {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 3px;
            padding: 8px;
            margin-bottom: 10px;
            font-size: 9px;
        }
        .irg-detail h4 {
            color: #1e40af;
            margin-bottom: 6px;
            font-size: 10px;
        }
        .irg-breakdown p {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 9px;
        }
        .charges-section {
            background: #f8f9fa;
            border-radius: 3px;
            padding: 8px;
            margin-bottom: 10px;
            font-size: 9px;
        }
        .charges-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .charge-item {
            display: flex;
            justify-content: space-between;
            padding: 8px;
            background: white;
            border-radius: 3px;
        }
        .charge-item.total {
            grid-column: 1 / -1;
            background: #dbeafe;
            font-weight: bold;
            color: #1e40af;
        }
        .signatures {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            margin: 15px 0 10px 0;
        }
        .signature-box {
            text-align: center;
        }
        .signature-area {
            height: 50px;
            border: 1px dashed #ccc;
            border-radius: 3px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 8px;
        }
        .qr-code {
            height: 50px;
            border: 1px solid #ccc;
            border-radius: 3px;
            margin-bottom: 4px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .qr-placeholder {
            width: 25px;
            height: 25px;
            background: #e5e7eb;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 8px;
            margin-bottom: 2px;
        }
        .signature-box p {
            font-size: 8px;
            color: #666;
        }
        .legal-mentions {
            text-align: center;
            font-size: 8px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 8px;
        }
        .legal-mentions p {
            margin-bottom: 2px;
        }
        @media print {
            @page {
                size: A4;
                margin: 10mm;
            }
            body {
                padding: 0;
                margin: 0;
                font-size: 10px;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .payslip-container {
                max-width: none;
                width: 100%;
                page-break-inside: avoid;
            }
            .header {
                background: #1e40af !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .salary-table th {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .irg-detail {
                background: #eff6ff !important;
                border: 1px solid #bfdbfe !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .charges-section {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .section {
                page-break-inside: avoid;
            }
            .signatures {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="payslip-container">
        <!-- En-tête -->
        <div class="header">
            <div class="company-info">
                <h1>${company?.name || 'Entreprise Demo'}</h1>
                <p>${company?.address || 'Alger, Algérie'}</p>
                <p>Tél: ${company?.phone || '+213 21 XX XX XX'} • Email: ${company?.email || '<EMAIL>'}</p>
                <p>NIF: ${company?.nif || '123456789012345'} • RC: ${company?.rc || '16/00-1234567'}</p>
            </div>
            <div class="payslip-title">
                <h2>BULLETIN DE PAIE</h2>
                <p>Période: ${getMonthName(payrollResult.period.month)} ${payrollResult.period.year}</p>
                <p style="font-size: 10px;">N° ${payrollResult.employee.id}-${payrollResult.period.month.toString().padStart(2, '0')}-${payrollResult.period.year}</p>
            </div>
        </div>

        <!-- Informations Employé -->
        <div class="section">
            <h3>INFORMATIONS EMPLOYÉ</h3>
            <div class="employee-grid">
                <div>
                    <div class="info-row">
                        <label>Nom et Prénom:</label>
                        <span>${payrollResult.employee.name}</span>
                    </div>
                    <div class="info-row">
                        <label>Matricule:</label>
                        <span>${payrollResult.employee.id}</span>
                    </div>
                    <div class="info-row">
                        <label>Fonction:</label>
                        <span>${payrollResult.employee.position}</span>
                    </div>
                    <div class="info-row">
                        <label>N° Sécurité Sociale:</label>
                        <span>${payrollResult.employee.socialSecurityNumber || 'N/A'}</span>
                    </div>
                </div>
                <div>
                    <div class="info-row">
                        <label>Période de paie:</label>
                        <span>${getMonthName(payrollResult.period.month)} ${payrollResult.period.year}</span>
                    </div>
                    <div class="info-row">
                        <label>Jours travaillés:</label>
                        <span>${payrollResult.period.workedDays}/${payrollResult.period.workingDays} jours</span>
                    </div>
                    <div class="info-row">
                        <label>Statut:</label>
                        <span>Actif</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau des Gains et Retenues -->
        <div class="section">
            <h3>DÉTAIL DES GAINS ET RETENUES</h3>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>DÉSIGNATION</th>
                        <th style="text-align: center;">BASE/TAUX</th>
                        <th style="text-align: right;">MONTANT (DZD)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Gains -->
                    <tr class="section-header">
                        <td colspan="3"><strong>GAINS</strong></td>
                    </tr>
                    <tr>
                        <td>Salaire de base</td>
                        <td style="text-align: center;">${payrollResult.period.workedDays}/${payrollResult.period.workingDays} jours</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.basicSalary)}</td>
                    </tr>
                    ${payrollResult.earnings.overtime > 0 ? `
                    <tr>
                        <td>Heures supplémentaires</td>
                        <td style="text-align: center;">-</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.overtime)}</td>
                    </tr>` : ''}
                    ${payrollResult.earnings.allowances.transport > 0 ? `
                    <tr>
                        <td>Indemnité de transport</td>
                        <td style="text-align: center;">Forfait</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.allowances.transport)}</td>
                    </tr>` : ''}
                    ${payrollResult.earnings.allowances.meal > 0 ? `
                    <tr>
                        <td>Indemnité de panier</td>
                        <td style="text-align: center;">Forfait</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.allowances.meal)}</td>
                    </tr>` : ''}
                    ${payrollResult.earnings.allowances.family > 0 ? `
                    <tr>
                        <td>Allocation familiale</td>
                        <td style="text-align: center;">Forfait</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.allowances.family)}</td>
                    </tr>` : ''}
                    ${payrollResult.earnings.allowances.housing > 0 ? `
                    <tr>
                        <td>Indemnité de logement</td>
                        <td style="text-align: center;">Forfait</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.allowances.housing)}</td>
                    </tr>` : ''}
                    ${payrollResult.earnings.allowances.other > 0 ? `
                    <tr>
                        <td>Autres indemnités</td>
                        <td style="text-align: center;">-</td>
                        <td class="amount">${formatCurrency(payrollResult.earnings.allowances.other)}</td>
                    </tr>` : ''}
                    <tr class="total-row gains-total">
                        <td><strong>TOTAL GAINS</strong></td>
                        <td></td>
                        <td class="amount"><strong>${formatCurrency(payrollResult.earnings.grossSalary)}</strong></td>
                    </tr>

                    <!-- Retenues -->
                    <tr class="section-header">
                        <td colspan="3"><strong>RETENUES</strong></td>
                    </tr>
                    <tr>
                        <td>Cotisation CNAS</td>
                        <td style="text-align: center;">9%</td>
                        <td class="amount">-${formatCurrency(payrollResult.deductions.cnas.employee)}</td>
                    </tr>
                    <tr>
                        <td>Impôt sur le Revenu Global (IRG)</td>
                        <td style="text-align: center;">Progressif</td>
                        <td class="amount">-${formatCurrency(payrollResult.deductions.irg.amount)}</td>
                    </tr>
                    ${payrollResult.deductions.other > 0 ? `
                    <tr>
                        <td>Autres retenues</td>
                        <td style="text-align: center;">-</td>
                        <td class="amount">-${formatCurrency(payrollResult.deductions.other)}</td>
                    </tr>` : ''}
                    <tr class="total-row deductions-total">
                        <td><strong>TOTAL RETENUES</strong></td>
                        <td></td>
                        <td class="amount"><strong>-${formatCurrency(payrollResult.deductions.totalDeductions)}</strong></td>
                    </tr>

                    <!-- Net à payer -->
                    <tr class="net-pay">
                        <td><strong>NET À PAYER</strong></td>
                        <td></td>
                        <td class="amount"><strong>${formatCurrency(payrollResult.netSalary)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Détail IRG -->
        <div class="irg-detail">
            <h4>Calcul IRG (Barème 2025)</h4>
            <div class="irg-breakdown">
                <p><span>Brut annuel:</span><span>${formatCurrency(payrollResult.earnings.grossSalary * 12)} DZD</span></p>
                <p><span>CNAS annuelle:</span><span>-${formatCurrency(payrollResult.deductions.cnas.employee * 12)} DZD</span></p>
                <p><span>Abattement (10%):</span><span>-${formatCurrency(Math.min(payrollResult.earnings.grossSalary * 12 * 0.10, 12000))} DZD</span></p>
                <p><span>Imposable annuel:</span><span>${formatCurrency(payrollResult.deductions.irg.taxableIncome * 12 || 0)} DZD</span></p>
                <p><span><strong>IRG mensuel:</strong></span><span><strong>${formatCurrency(payrollResult.deductions.irg.amount)} DZD</strong></span></p>
            </div>
        </div>

        <!-- Charges patronales -->
        <div class="charges-section">
            <h4 style="color: #1e40af; margin-bottom: 10px;">Charges patronales</h4>
            <div class="charges-grid">
                <div class="charge-item">
                    <span>CNAS Employeur (25%):</span>
                    <span>${formatCurrency(payrollResult.employerCharges.cnas)} DZD</span>
                </div>
                <div class="charge-item">
                    <span>CACOBATPH (1.5%):</span>
                    <span>${formatCurrency(payrollResult.employerCharges.cacobatph)} DZD</span>
                </div>
                <div class="charge-item total">
                    <span><strong>Coût total employeur:</strong></span>
                    <span><strong>${formatCurrency(payrollResult.totalCost)} DZD</strong></span>
                </div>
            </div>
        </div>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <div class="signature-area">Cachet de l'entreprise</div>
                <p>Cachet et signature</p>
            </div>
            <div class="signature-box">
                <div class="signature-area">Signature du Directeur</div>
                <p>Directeur Général</p>
            </div>
            <div class="signature-box">
                <div class="qr-code">
                    <div class="qr-placeholder">QR</div>
                    <div style="font-size: 8px; font-family: monospace;">${payrollResult.employee.id}-${payrollResult.period.month}-${payrollResult.period.year}</div>
                </div>
                <p>Code de vérification</p>
            </div>
        </div>

        <!-- Mentions légales -->
        <div class="legal-mentions">
            <p>Ce bulletin de paie est conforme à la législation algérienne en vigueur.</p>
            <p>Document généré le ${new Date().toLocaleDateString('fr-DZ')} par Forsa Finance</p>
            <p><strong>Net à payer en lettres:</strong> ${numberToWords(payrollResult.netSalary)}</p>
        </div>
    </div>
</body>
</html>`;
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  const numberToWords = (amount: number): string => {
    // Fonction simplifiée pour convertir un nombre en lettres
    if (amount === 0) return 'zéro dinars';

    if (amount < 1000) {
      return Math.floor(amount).toString() + ' dinars';
    } else if (amount < 1000000) {
      const thousands = Math.floor(amount / 1000);
      const remainder = Math.floor(amount % 1000);
      let result = thousands + ' mille';
      if (remainder > 0) {
        result += ' ' + remainder;
      }
      return result + ' dinars';
    } else {
      return Math.floor(amount).toLocaleString('fr-DZ') + ' dinars';
    }
  };

  const handleExport = () => {
    // TODO: Implémenter l'export PDF
    toast({
      title: "Export PDF",
      description: "Fonctionnalité d'export PDF à implémenter.",
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Calculateur de Paie</CardTitle>
          <CardDescription>
            Calculez automatiquement les bulletins de paie conformément à la réglementation algérienne.
          </CardDescription>
          {propSelectedEmployee && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-900">
                  Employé sélectionné : {propSelectedEmployee.first_name} {propSelectedEmployee.last_name}
                </span>
              </div>
              <div className="text-xs text-blue-700 mt-1">
                {propSelectedEmployee.position} • Salaire de base : {propSelectedEmployee.salary?.toLocaleString('fr-DZ')} DZD
              </div>
            </div>
          )}
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="employeeId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employé</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner un employé" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingEmployees ? (
                            <SelectItem value="" disabled>
                              Chargement des employés...
                            </SelectItem>
                          ) : employees.length === 0 ? (
                            <SelectItem value="" disabled>
                              Aucun employé trouvé
                            </SelectItem>
                          ) : (
                            employees.map((employee) => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name} - {employee.position}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="month"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mois</FormLabel>
                      <Select onValueChange={(value) => field.onChange(parseInt(value))} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Sélectionner le mois" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Array.from({ length: 12 }, (_, i) => (
                            <SelectItem key={i + 1} value={(i + 1).toString()}>
                              {getMonthName(i + 1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="year"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Année</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="basicSalary"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Salaire de base (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="workingDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jours travaillés</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="standardWorkingDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jours ouvrables</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="overtimeHours"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Heures supplémentaires</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="transportAllowance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Indemnité transport (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="mealAllowance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Indemnité panier (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="housingAllowance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Indemnité logement (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="familyAllowance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Allocation familiale (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="otherAllowance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Autres indemnités (DZD)</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unpaidDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jours d'absence non payés</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="sickDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Jours de maladie</FormLabel>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="socialSecurityNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>N° Sécurité Sociale</FormLabel>
                      <FormControl>
                        <Input type="text" placeholder="Ex: 123456789012345" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button type="submit" disabled={isCalculating} className="w-full">
                {isCalculating ? (
                  <>
                    <Calculator className="mr-2 h-4 w-4 animate-spin" />
                    Calcul en cours...
                  </>
                ) : (
                  <>
                    <Calculator className="mr-2 h-4 w-4" />
                    Calculer la paie
                  </>
                )}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Nouveau bulletin professionnel simplifié (temporairement désactivé) */}
      {payrollResult && false && (
        <SimplePayslip
          payrollData={payrollResult}
          onPrint={handlePrint}
          onDownload={handleExport}
        />
      )}

      {/* Ancien affichage simple (réactivé temporairement) */}
      {payrollResult && (
        <div className="payslip-print-area">
          <style jsx>{`
            @media print {
              body * {
                visibility: hidden;
              }
              .payslip-print-area, .payslip-print-area * {
                visibility: visible;
              }
              .payslip-print-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white;
                padding: 20px;
                font-size: 12px;
              }
              .print-hide {
                display: none !important;
              }
              .print-header {
                border-bottom: 2px solid #1e40af;
                margin-bottom: 20px;
                padding-bottom: 15px;
              }
              .print-title {
                font-size: 18px;
                font-weight: bold;
                color: #1e40af;
                text-align: center;
                margin-bottom: 10px;
              }
              .print-company {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 5px;
              }
              .print-details {
                font-size: 11px;
                color: #374151;
              }
              .print-section {
                margin-bottom: 15px;
              }
              .print-table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;
              }
              .print-table th,
              .print-table td {
                border: 1px solid #d1d5db;
                padding: 8px;
                text-align: left;
                font-size: 11px;
              }
              .print-table th {
                background-color: #f3f4f6;
                font-weight: bold;
              }
              .print-net {
                background-color: #dcfce7;
                font-weight: bold;
                font-size: 14px;
              }
              .print-footer {
                margin-top: 30px;
                border-top: 1px solid #d1d5db;
                padding-top: 15px;
              }
              .print-signatures {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 20px;
                margin-bottom: 20px;
              }
              .print-signature-box {
                border: 2px dashed #9ca3af;
                height: 80px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                color: #6b7280;
              }
              .print-legal {
                font-size: 10px;
                color: #6b7280;
                text-align: center;
                margin-top: 15px;
              }
            }
          `}</style>

        <Card className="print:shadow-none print:border-none">
          {/* En-tête entreprise */}
          <div className="bg-blue-50 border-b p-6 print-header">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-xl font-bold text-gray-900 print-company">
                  {company?.name || 'Entreprise Demo'}
                </h1>
                <div className="text-sm text-gray-600 mt-1 print-details">
                  <p>{company?.address || 'Alger, Algérie'}</p>
                  <p>Tél: {company?.phone || '+213 21 XX XX XX'} • Email: {company?.email || '<EMAIL>'}</p>
                  <p>NIF: {company?.nif || '123456789012345'} • RC: {company?.rc || '16/00-1234567'}</p>
                </div>
              </div>
              <div className="text-right">
                <div className="print-title">BULLETIN DE PAIE</div>
                <p className="text-sm text-blue-700 print-details">
                  Période: {getMonthName(payrollResult.period.month)} {payrollResult.period.year}
                </p>
                <p className="text-xs text-blue-600 mt-1 print-details">
                  N° {payrollResult.employee.id}-{payrollResult.period.month.toString().padStart(2, '0')}-{payrollResult.period.year}
                </p>
              </div>
            </div>
          </div>

          <CardHeader className="flex flex-row items-center justify-between print:hidden">
            <div>
              <CardTitle>Détail de la Paie</CardTitle>
              <CardDescription>
                Calcul conforme à la réglementation algérienne
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="mr-2 h-4 w-4" />
                Imprimer
              </Button>
              <Button variant="outline" onClick={handleExport}>
                <Download className="mr-2 h-4 w-4" />
                Exporter PDF
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Informations employé */}
            <div className="grid grid-cols-2 gap-6 mb-6 print-section">
              <div>
                <h3 className="font-semibold mb-3 text-gray-800">INFORMATIONS EMPLOYÉ</h3>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Nom et Prénom:</span> {payrollResult.employee.name}</p>
                  <p><span className="font-medium">Poste:</span> {payrollResult.employee.position}</p>
                  <p><span className="font-medium">Matricule:</span> {payrollResult.employee.id}</p>
                  <p><span className="font-medium">N° Sécurité Sociale:</span> {payrollResult.employee.socialSecurityNumber || 'N/A'}</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-3 text-gray-800">PÉRIODE DE PAIE</h3>
                <div className="space-y-1 text-sm">
                  <p><span className="font-medium">Mois:</span> {getMonthName(payrollResult.period.month)} {payrollResult.period.year}</p>
                  <p><span className="font-medium">Jours ouvrables:</span> {payrollResult.period.workingDays}</p>
                  <p><span className="font-medium">Jours travaillés:</span> {payrollResult.period.workedDays}</p>
                  <p><span className="font-medium">Statut:</span> <span className="text-green-600 font-medium">Actif</span></p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Détail des calculs */}
            <div className="print-section">
              <h3 className="font-semibold mb-3 text-gray-800">DÉTAIL DES GAINS ET RETENUES</h3>
              <table className="print-table w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">DÉSIGNATION</th>
                    <th className="border border-gray-300 px-4 py-2 text-center">BASE/TAUX</th>
                    <th className="border border-gray-300 px-4 py-2 text-right">MONTANT (DZD)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium">Salaire de base</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">{payrollResult.period.workedDays}/{payrollResult.period.workingDays} jours</td>
                    <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.basicSalary)}</td>
                  </tr>
                  {payrollResult.earnings.overtime > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Heures supplémentaires</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">-</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.overtime)}</td>
                    </tr>
                  )}
                  {payrollResult.earnings.allowances.transport > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Indemnité de transport</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">Forfait</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.allowances.transport)}</td>
                    </tr>
                  )}
                  {payrollResult.earnings.allowances.meal > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Indemnité de panier</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">Forfait</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.allowances.meal)}</td>
                    </tr>
                  )}
                  {payrollResult.earnings.allowances.family > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Allocation familiale</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">Forfait</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.allowances.family)}</td>
                    </tr>
                  )}
                  {payrollResult.earnings.allowances.housing > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Indemnité de logement</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">Forfait</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.allowances.housing)}</td>
                    </tr>
                  )}
                  {payrollResult.earnings.allowances.other > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">Autres indemnités</td>
                      <td className="border border-gray-300 px-4 py-2 text-center">-</td>
                      <td className="border border-gray-300 px-4 py-2 text-right font-medium">{formatCurrency(payrollResult.earnings.allowances.other)}</td>
                    </tr>
                  )}
                  <tr className="bg-blue-50 font-semibold">
                    <td className="border border-gray-300 px-4 py-2 font-bold">TOTAL GAINS</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">-</td>
                    <td className="border border-gray-300 px-4 py-2 text-right font-bold">{formatCurrency(payrollResult.earnings.grossSalary)}</td>
                  </tr>
                  <tr>
                    <td className="border border-gray-300 px-4 py-2 font-medium text-red-600">Cotisation CNAS Employé</td>
                    <td className="border border-gray-300 px-4 py-2 text-center text-red-600">9%</td>
                    <td className="border border-gray-300 px-4 py-2 text-right text-red-600 font-medium">-{formatCurrency(payrollResult.deductions.cnas.employee)}</td>
                  </tr>
                  {payrollResult.deductions.irg.amount > 0 && (
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium text-red-600">Impôt sur le Revenu Global (IRG)</td>
                      <td className="border border-gray-300 px-4 py-2 text-center text-red-600">Progressif</td>
                      <td className="border border-gray-300 px-4 py-2 text-right text-red-600 font-medium">-{formatCurrency(payrollResult.deductions.irg.amount)}</td>
                    </tr>
                  )}
                  <tr className="bg-red-50 font-semibold">
                    <td className="border border-gray-300 px-4 py-2 font-bold text-red-700">TOTAL RETENUES</td>
                    <td className="border border-gray-300 px-4 py-2 text-center">-</td>
                    <td className="border border-gray-300 px-4 py-2 text-right font-bold text-red-700">-{formatCurrency(payrollResult.deductions.totalDeductions)}</td>
                  </tr>
                  <tr className="print-net bg-green-50 border-t-2">
                    <td className="border border-gray-300 px-4 py-3 font-bold text-green-700 text-lg">NET À PAYER</td>
                    <td className="border border-gray-300 px-4 py-3 text-center">-</td>
                    <td className="border border-gray-300 px-4 py-3 text-right font-bold text-green-700 text-lg">{formatCurrency(payrollResult.netSalary)}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <Separator />

            {/* Charges patronales */}
            <div>
              <h3 className="font-semibold mb-2">Charges Patronales</h3>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell>CNAS Employeur (25%)</TableCell>
                    <TableCell className="text-right">{formatCurrency(payrollResult.employerCharges.cnas)}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell>CACOBATPH (1.5%)</TableCell>
                    <TableCell className="text-right">{formatCurrency(payrollResult.employerCharges.cacobatph)}</TableCell>
                  </TableRow>
                  <TableRow className="bg-muted/50">
                    <TableCell className="font-semibold">Total charges patronales</TableCell>
                    <TableCell className="text-right font-semibold">{formatCurrency(payrollResult.employerCharges.total)}</TableCell>
                  </TableRow>
                  <TableRow className="bg-blue-50">
                    <TableCell className="font-bold text-blue-700">Coût total employeur</TableCell>
                    <TableCell className="text-right font-bold text-blue-700">{formatCurrency(payrollResult.totalCost)}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>

            <Separator className="my-6" />

            {/* Détail IRG */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 print-section">
              <h4 className="font-semibold text-blue-900 mb-2">📊 Détail calcul IRG (Barème 2025)</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p><span className="font-medium">Salaire brut annuel:</span> {formatCurrency(payrollResult.earnings.grossSalary * 12)} DZD</p>
                <p><span className="font-medium">CNAS annuelle:</span> -{formatCurrency(payrollResult.deductions.cnas.employee * 12)} DZD</p>
                <p><span className="font-medium">Abattement (10% max 12000):</span> -{formatCurrency(Math.min(payrollResult.earnings.grossSalary * 12 * 0.10, 12000))} DZD</p>
                <p><span className="font-medium">Revenu imposable annuel:</span> {formatCurrency(payrollResult.deductions.irg.annualTaxableIncome || 0)} DZD</p>
                <div className="mt-2 p-2 bg-white rounded border">
                  <p className="font-medium text-blue-900 mb-1">Barème IRG 2025:</p>
                  <div className="text-xs space-y-1">
                    <p>• 0 - 240 000 DZD: 0%</p>
                    <p>• 240 001 - 480 000 DZD: 23%</p>
                    <p>• 480 001 - 960 000 DZD: 27%</p>
                    <p>• 960 001 - 1 920 000 DZD: 30%</p>
                    <p>• Au-dessus de 1 920 000 DZD: 35%</p>
                  </div>
                </div>
                <p><span className="font-medium">IRG annuel:</span> {formatCurrency(payrollResult.deductions.irg.annualIRG || 0)} DZD</p>
                <p><span className="font-medium">IRG mensuel:</span> {formatCurrency(payrollResult.deductions.irg.amount)} DZD</p>
                <p><span className="font-medium">Taux effectif:</span> {payrollResult.deductions.irg.rate.toFixed(2)}%</p>
              </div>
            </div>

            {/* Pied de page avec signatures - visible uniquement à l'impression */}
            <div className="print-footer hidden print:block">
              <div className="print-signatures">
                <div className="text-center">
                  <div className="print-signature-box">
                    Cachet de l'entreprise
                  </div>
                  <p className="text-xs mt-2">Cachet et signature</p>
                </div>
                <div className="text-center">
                  <div className="print-signature-box">
                    Signature du Directeur
                  </div>
                  <p className="text-xs mt-2">Directeur Général</p>
                </div>
                <div className="text-center">
                  <div className="print-signature-box">
                    <div>
                      <div style={{width: '40px', height: '40px', background: '#f3f4f6', margin: '0 auto 5px', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '10px'}}>QR</div>
                      <div style={{fontSize: '8px', fontFamily: 'monospace'}}>
                        {payrollResult.employee.id}-{payrollResult.period.month}-{payrollResult.period.year}
                      </div>
                    </div>
                  </div>
                  <p className="text-xs mt-2">Code de vérification</p>
                </div>
              </div>

              <div className="print-legal">
                <p>Ce bulletin de paie est conforme à la réglementation algérienne en vigueur.</p>
                <p>Document généré le {new Date().toLocaleDateString('fr-DZ')} par Forsa Finance</p>
                <p>Net à payer en lettres: {/* Ici on pourrait ajouter le montant en lettres */}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        </div>
      )}
    </div>
  );
}
