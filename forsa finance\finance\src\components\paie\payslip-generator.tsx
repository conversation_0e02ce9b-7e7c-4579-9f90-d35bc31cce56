'use client';

import { useState, useTransition, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { generatePayslip, type GeneratePayslipInput, type GeneratePayslipOutput } from '../../ai/flows/generate-payslip';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Loader2, Wand2, Printer, Users, CalendarDays, KeyRound, ReceiptText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { employees, type Employee } from '@/data/employees';
import { Separator } from '../ui/separator';
import Image from 'next/image';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const formSchema = z.object({
  employeeId: z.string().min(1, { message: "Veuillez sélectionner un employé." }),
  netSalary: z.coerce.number().min(1, { message: 'Le salaire doit être positif.' }),
});

type FormValues = z.infer<typeof formSchema>;

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

type PayslipGeneratorProps = {
  selectedEmployee?: Employee | null;
};

export function PayslipGenerator({ selectedEmployee: initialSelectedEmployee }: PayslipGeneratorProps) {
  const [isPending, startTransition] = useTransition();
  const [payslipResult, setPayslipResult] = useState<GeneratePayslipOutput | null>(null);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(initialSelectedEmployee || null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      employeeId: '',
      netSalary: 0,
    },
  });
  
  const employeeIdValue = form.watch('employeeId');

  useEffect(() => {
    if (initialSelectedEmployee) {
      form.setValue('employeeId', String(initialSelectedEmployee.id));
      setSelectedEmployee(initialSelectedEmployee);
      setPayslipResult(null);
    }
  }, [initialSelectedEmployee, form]);

  useEffect(() => {
    const employee = employees.find(emp => String(emp.id) === employeeIdValue);
    setSelectedEmployee(employee || null);
    setPayslipResult(null); // Clear previous result when employee changes
  }, [employeeIdValue]);

  const onSubmit = (values: FormValues) => {
    setErrorMessage(null);
    if (!selectedEmployee) {
      toast({
        variant: 'destructive',
        title: 'Erreur de validation',
        description: 'Aucun employé sélectionné.',
      });
      setErrorMessage('Aucun employé sélectionné.');
      return;
    }

    setPayslipResult(null);
    startTransition(async () => {
      try {
        // Vérification basique de la configuration IA
        if (!process.env.GOOGLEAI_API_KEY && !process.env.GEMINI_API_KEY) {
          setErrorMessage("La configuration IA est manquante. Veuillez vérifier vos clés API dans le fichier d'environnement.");
          return;
        }
        const input: GeneratePayslipInput = {
          employeeName: selectedEmployee.name,
          employeeRole: selectedEmployee.role,
          netSalary: values.netSalary,
          employeeNSS: selectedEmployee.nss,
          hireDate: selectedEmployee.hireDate,
          children: selectedEmployee.children,
        };
        const result = await generatePayslip(input);
        setPayslipResult(result);
      } catch (error: any) {
        console.error('Payslip generation failed:', error);
        let message = 'Impossible de générer la fiche de paie. Veuillez réessayer.';
        if (error?.message?.includes("IA n'a pas réussi")) {
          message = "Erreur IA : " + error.message;
        }
        setErrorMessage(message);
        toast({
          variant: 'destructive',
          title: 'Erreur de Génération',
          description: message,
        });
      }
    });
  };

  return (
    <div className="grid gap-8 lg:grid-cols-2">
      <Card>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardHeader>
              <CardTitle>Générateur de Fiche de Paie par IA</CardTitle>
              <CardDescription>
                Sélectionnez un employé et entrez le salaire net souhaité.
              </CardDescription>
              {errorMessage && (
                <div className="mt-2 text-sm text-red-600">
                  {errorMessage}
                </div>
              )}
            </CardHeader>
            <CardContent className="space-y-6">
               <FormField
                control={form.control}
                name="employeeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employé</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionnez un employé..." />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {employees.map(emp => (
                          <SelectItem key={emp.id} value={String(emp.id)}>
                            {emp.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {selectedEmployee && (
                <div className="space-y-4 rounded-lg border bg-muted/50 p-4">
                  <h4 className="font-semibold text-sm">Informations de l'employé</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                        <ReceiptText className="h-4 w-4" />
                        <span>{selectedEmployee.role}</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <KeyRound className="h-4 w-4" />
                        <span>NSS: {selectedEmployee.nss || 'N/A'}</span>
                    </div>
                    <div className="flex items-center gap-2">
                        <CalendarDays className="h-4 w-4" />
                        <span>Embauché le: {selectedEmployee.hireDate || 'N/A'}</span>
                    </div>
                     <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span>{selectedEmployee.children || 0} enfant(s)</span>
                    </div>
                  </div>
                </div>
              )}

              <FormField
                control={form.control}
                name="netSalary"
                render={({ field }) => (
                <FormItem>
                    <FormLabel>Net à Payer (DZD)</FormLabel>
                    <FormControl>
                    <Input type="number" placeholder="100000" {...field} disabled={!selectedEmployee} />
                    </FormControl>
                    <FormMessage />
                </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" disabled={isPending || !selectedEmployee}>
                {isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Wand2 className="mr-2 h-4 w-4" />
                )}
                Générer la Fiche
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
      
      <div className="lg:col-span-1">
        {isPending && (
            <Card className="flex items-center justify-center min-h-[400px]">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </Card>
        )}
        {payslipResult && (
            <Card>
                <CardHeader>
                    <div className="flex flex-col items-center text-center space-y-2">
                        <h2 className="text-2xl font-bold font-headline">BULLETIN DE PAIE</h2>
                        <p className="text-sm text-muted-foreground">Période de paie: {payslipResult.period}</p>
                    </div>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="space-y-1">
                            <h3 className="font-semibold">{payslipResult.companyInfo.name}</h3>
                            <p className="text-muted-foreground">{payslipResult.companyInfo.address}</p>
                            <p className="text-muted-foreground">NIF: {payslipResult.companyInfo.nif}</p>
                        </div>
                         <div className="space-y-1 text-right">
                            <h3 className="font-semibold">{payslipResult.employeeInfo.name}</h3>
                            <p className="text-muted-foreground">{payslipResult.employeeInfo.role}</p>
                            <p className="text-muted-foreground">Date d'embauche: {payslipResult.employeeInfo.hireDate}</p>
                            <p className="text-muted-foreground">NSS: {payslipResult.employeeInfo.nss}</p>
                        </div>
                    </div>
                    <Separator />
                    <Table>
                        <TableHeader>
                            <TableRow>
                            <TableHead>Description</TableHead>
                            <TableHead className="text-right">Gains (DZD)</TableHead>
                            <TableHead className="text-right">Retenues (DZD)</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {payslipResult.earnings.map((item: { description: string; amount: number }, index: number) => (
                              <TableRow key={`earning-${index}`}>
                                <TableCell>{item.description}</TableCell>
                                <TableCell className="text-right font-mono text-green-600">
                                  {formatCurrency(item.amount)}
                                </TableCell>
                                <TableCell></TableCell>
                              </TableRow>
                            ))}
                            {payslipResult.deductions.map((item: { description: string; amount: number }, index: number) => (
                              <TableRow key={`deduction-${index}`}>
                                <TableCell>{item.description}</TableCell>
                                <TableCell></TableCell>
                                <TableCell className="text-right font-mono text-red-600">
                                  {formatCurrency(item.amount)}
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                        <TableFooter>
                            <TableRow className="font-bold">
                                <TableCell>Totaux</TableCell>
                                <TableCell className="text-right font-mono text-green-600">
                                    {formatCurrency(payslipResult.summary.grossSalary)}
                                </TableCell>
                                <TableCell className="text-right font-mono text-red-600">
                                    {formatCurrency(payslipResult.summary.totalDeductions)}
                                </TableCell>
                            </TableRow>
                             <TableRow className="text-lg font-bold bg-muted/50 hover:bg-muted">
                                <TableCell colSpan={2}>Net à Payer</TableCell>
                                <TableCell className="text-right font-mono">
                                    {formatCurrency(payslipResult.summary.netSalary)}
                                </TableCell>
                            </TableRow>
                        </TableFooter>
                    </Table>
                    <Separator />
                    <div className="flex justify-between items-end pt-4">
                        <div className="text-center">
                            <Image src={payslipResult.qrCodeDataUri} alt="Payslip QR Code" width={80} height={80} />
                            <p className="text-xs text-muted-foreground mt-1">Vérification</p>
                        </div>
                        <div className="space-y-10 text-center text-sm">
                            <div className="space-y-1">
                                <p className="font-semibold">Cachet de l'entreprise</p>
                                <div className="h-16 w-32 border-dashed border-2 rounded-md mx-auto"></div>
                            </div>
                            <div className="space-y-1">
                                <p className="font-semibold">Signature du Directeur</p>
                                <div className="h-12 w-48 border-b border-foreground"></div>
                            </div>
                        </div>
                    </div>
                </CardContent>
                <CardFooter className="justify-end">
                    <Button variant="outline">
                        <Printer className="mr-2 h-4 w-4" />
                        Imprimer
                    </Button>
                </CardFooter>
            </Card>
        )}
        {!isPending && !payslipResult && (
            <Card className="min-h-full">
                <CardHeader>
                    <CardTitle>Résultat</CardTitle>
                    <CardDescription>La fiche de paie générée s&apos;affichera ici.</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex flex-col items-center justify-center text-center text-muted-foreground h-64 border-2 border-dashed rounded-lg">
                        <p>Sélectionnez un employé pour commencer.</p>
                    </div>
                </CardContent>
            </Card>
        )}
      </div>
    </div>
  );
}
