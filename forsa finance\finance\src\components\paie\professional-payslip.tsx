'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Printer, Download, QrCode } from 'lucide-react';
import { PayrollOutput } from '@/lib/payroll-calculations';
import { useAuth } from '@/components/providers/auth-provider';

interface ProfessionalPayslipProps {
  payrollData: PayrollOutput;
  onPrint?: () => void;
  onDownload?: () => void;
}

export function ProfessionalPayslip({ payrollData, onPrint, onDownload }: ProfessionalPayslipProps) {
  const { company } = useAuth();
  const [showSignature, setShowSignature] = useState(false);

  // Générer un code QR unique pour cette fiche de paie
  const generateQRCode = () => {
    const qrData = `${payrollData.employee.id}-${payrollData.period.month}-${payrollData.period.year}-${payrollData.netSalary}`;
    return btoa(qrData); // Encodage base64 simple
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { 
      style: 'decimal', 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    }) + ' DZD';
  };

  const getMonthName = (month: number) => {
    const months = [
      'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
      'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];
    return months[month - 1];
  };

  const qrCodeData = generateQRCode();

  return (
    <div className="max-w-4xl mx-auto bg-white">
      {/* Actions */}
      <div className="flex justify-end gap-2 mb-4 print:hidden">
        <Button onClick={onPrint} variant="outline" size="sm">
          <Printer className="w-4 h-4 mr-2" />
          Imprimer
        </Button>
        <Button onClick={onDownload} variant="outline" size="sm">
          <Download className="w-4 h-4 mr-2" />
          Télécharger PDF
        </Button>
      </div>

      <Card className="print:shadow-none print:border-none">
        <CardContent className="p-8">
          {/* En-tête Entreprise */}
          <div className="flex justify-between items-start mb-8">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {company?.name || 'Entreprise Demo'}
              </h1>
              <div className="text-sm text-gray-600 space-y-1">
                <p>{company?.address || 'Alger, Algérie'}</p>
                <p>Tél: {company?.phone || '+213 21 XX XX XX'}</p>
                <p>Email: {company?.email || '<EMAIL>'}</p>
                <div className="flex gap-4 mt-2">
                  <span>NIF: {company?.nif || '123456789012345'}</span>
                  <span>RC: {company?.rc || '16/00-1234567'}</span>
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h2 className="text-lg font-semibold text-blue-900 mb-1">
                  BULLETIN DE PAIE
                </h2>
                <p className="text-sm text-blue-700">
                  {getMonthName(payrollData.period.month)} {payrollData.period.year}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  N° {payrollData.employee.id}-{payrollData.period.month.toString().padStart(2, '0')}-{payrollData.period.year}
                </p>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Informations Employé */}
          <div className="grid grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">INFORMATIONS EMPLOYÉ</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Nom et Prénom:</span>
                  <span className="font-medium">{payrollData.employee.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Poste:</span>
                  <span>{payrollData.employee.position}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">N° Sécurité Sociale:</span>
                  <span>{payrollData.employee.socialSecurityNumber || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Matricule:</span>
                  <span>{payrollData.employee.id}</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">PÉRIODE DE PAIE</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Mois:</span>
                  <span>{getMonthName(payrollData.period.month)} {payrollData.period.year}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jours ouvrables:</span>
                  <span>{payrollData.period.workingDays}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Jours travaillés:</span>
                  <span>{payrollData.period.workedDays}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Statut:</span>
                  <Badge variant="secondary">Actif</Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Détail des Gains */}
          <div className="mb-8">
            <h3 className="font-semibold text-gray-900 mb-4">DÉTAIL DES GAINS</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-3 gap-4 text-sm font-medium text-gray-700 mb-3">
                <span>DÉSIGNATION</span>
                <span className="text-center">BASE</span>
                <span className="text-right">MONTANT</span>
              </div>
              <Separator className="mb-3" />
              
              {/* Salaire de base */}
              <div className="grid grid-cols-3 gap-4 text-sm py-2">
                <span>Salaire de base</span>
                <span className="text-center">{payrollData.period.workedDays}/{payrollData.period.workingDays}</span>
                <span className="text-right font-medium">{formatCurrency(payrollData.earnings.basicSalary)}</span>
              </div>

              {/* Heures supplémentaires */}
              {payrollData.earnings.overtime > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Heures supplémentaires</span>
                  <span className="text-center">-</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.overtime)}</span>
                </div>
              )}

              {/* Indemnités */}
              {payrollData.earnings.allowances.transport > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Indemnité de transport</span>
                  <span className="text-center">Forfait</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.allowances.transport)}</span>
                </div>
              )}

              {payrollData.earnings.allowances.meal > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Indemnité de panier</span>
                  <span className="text-center">Forfait</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.allowances.meal)}</span>
                </div>
              )}

              {payrollData.earnings.allowances.housing > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Indemnité de logement</span>
                  <span className="text-center">Forfait</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.allowances.housing)}</span>
                </div>
              )}

              {payrollData.earnings.allowances.family > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Allocation familiale</span>
                  <span className="text-center">Forfait</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.allowances.family)}</span>
                </div>
              )}

              {payrollData.earnings.allowances.other > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Autres indemnités</span>
                  <span className="text-center">-</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.earnings.allowances.other)}</span>
                </div>
              )}

              <Separator className="my-3" />
              <div className="grid grid-cols-3 gap-4 text-sm font-semibold py-2 bg-blue-50 rounded px-2">
                <span>TOTAL GAINS</span>
                <span></span>
                <span className="text-right">{formatCurrency(payrollData.earnings.grossSalary)}</span>
              </div>
            </div>
          </div>

          {/* Détail des Retenues */}
          <div className="mb-8">
            <h3 className="font-semibold text-gray-900 mb-4">DÉTAIL DES RETENUES</h3>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="grid grid-cols-3 gap-4 text-sm font-medium text-gray-700 mb-3">
                <span>DÉSIGNATION</span>
                <span className="text-center">TAUX</span>
                <span className="text-right">MONTANT</span>
              </div>
              <Separator className="mb-3" />
              
              {/* Cotisations sociales */}
              <div className="grid grid-cols-3 gap-4 text-sm py-2">
                <span>Cotisation CNAS</span>
                <span className="text-center">9%</span>
                <span className="text-right font-medium">{formatCurrency(payrollData.deductions.cnas.employee)}</span>
              </div>

              {/* IRG */}
              <div className="grid grid-cols-3 gap-4 text-sm py-2">
                <span>Impôt sur le Revenu Global (IRG)</span>
                <span className="text-center">Progressif</span>
                <span className="text-right font-medium">{formatCurrency(payrollData.deductions.irg.amount)}</span>
              </div>

              {/* Autres retenues */}
              {payrollData.deductions.other > 0 && (
                <div className="grid grid-cols-3 gap-4 text-sm py-2">
                  <span>Autres retenues</span>
                  <span className="text-center">-</span>
                  <span className="text-right font-medium">{formatCurrency(payrollData.deductions.other)}</span>
                </div>
              )}

              <Separator className="my-3" />
              <div className="grid grid-cols-3 gap-4 text-sm font-semibold py-2 bg-red-100 rounded px-2">
                <span>TOTAL RETENUES</span>
                <span></span>
                <span className="text-right">{formatCurrency(payrollData.deductions.totalDeductions)}</span>
              </div>
            </div>
          </div>

          {/* Récapitulatif */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Total Gains:</span>
                  <span className="font-medium">{formatCurrency(payrollData.earnings.grossSalary)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Total Retenues:</span>
                  <span className="font-medium">-{formatCurrency(payrollData.deductions.totalDeductions)}</span>
                </div>
                <Separator />
                <div className="flex justify-between text-lg font-bold text-green-800">
                  <span>NET À PAYER:</span>
                  <span>{formatCurrency(payrollData.netSalary)}</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>Charges patronales CNAS (25%):</span>
                  <span className="font-medium">{formatCurrency(payrollData.employerCharges?.cnas || 0)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Coût total employeur:</span>
                  <span className="font-medium">{formatCurrency(payrollData.totalCost || 0)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Pied de page avec signatures et QR Code */}
          <div className="mt-12 pt-8 border-t">
            <div className="grid grid-cols-3 gap-8 items-end">
              {/* Cachet entreprise */}
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-2 min-h-[100px] flex items-center justify-center">
                  <span className="text-gray-400 text-sm">Cachet de l'entreprise</span>
                </div>
                <p className="text-xs text-gray-600">Cachet et signature</p>
              </div>

              {/* Signature directeur */}
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 mb-2 min-h-[100px] flex items-center justify-center">
                  <span className="text-gray-400 text-sm">Signature du Directeur</span>
                </div>
                <p className="text-xs text-gray-600">Directeur Général</p>
              </div>

              {/* QR Code */}
              <div className="text-center">
                <div className="border border-gray-300 rounded-lg p-4 mb-2 bg-white flex items-center justify-center min-h-[100px]">
                  <div className="text-center">
                    <QrCode className="w-16 h-16 mx-auto mb-2 text-gray-600" />
                    <p className="text-xs text-gray-500 font-mono break-all">{qrCodeData.substring(0, 16)}...</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Code de vérification</p>
              </div>
            </div>

            {/* Mentions légales */}
            <div className="mt-8 pt-4 border-t text-xs text-gray-500 text-center">
              <p>Ce bulletin de paie est conforme à la réglementation algérienne en vigueur.</p>
              <p>Document généré le {new Date().toLocaleDateString('fr-DZ')} - Code QR: {qrCodeData.substring(0, 8)}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
