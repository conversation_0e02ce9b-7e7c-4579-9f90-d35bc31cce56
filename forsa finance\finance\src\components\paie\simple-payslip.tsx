'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Printer, Download, QrCode } from 'lucide-react';
import { useAuth } from '@/components/providers/auth-provider';
import { PayrollOutput } from '@/lib/payroll-calculations';

interface SimplePayslipProps {
  payrollData: PayrollOutput;
  onPrint?: () => void;
  onDownload?: () => void;
}

export function SimplePayslip({ payrollData, onPrint, onDownload }: SimplePayslipProps) {
  const { company } = useAuth();

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { 
      style: 'decimal', 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    }) + ' DZD';
  };

  const getMonthName = (month: number) => {
    const months = [
      'Janvier', 'Février', '<PERSON>', 'Avril', '<PERSON>', 'Juin',
      '<PERSON><PERSON><PERSON>', 'A<PERSON>ût', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];
    return months[month - 1];
  };

  // Générer un code QR simple
  const generateQRCode = () => {
    const qrData = `${payrollData.employee.id}-${payrollData.period.month}-${payrollData.period.year}-${payrollData.netSalary}`;
    return btoa(qrData).substring(0, 16);
  };

  return (
    <div className="max-w-4xl mx-auto bg-white">
      {/* Actions */}
      <div className="flex justify-end gap-2 mb-4 print:hidden">
        <Button onClick={onPrint} variant="outline" size="sm">
          <Printer className="w-4 h-4 mr-2" />
          Imprimer
        </Button>
        <Button onClick={onDownload} variant="outline" size="sm">
          <Download className="w-4 h-4 mr-2" />
          Télécharger PDF
        </Button>
      </div>

      <Card className="print:shadow-none print:border-none">
        <CardHeader className="bg-blue-50 border-b">
          {/* En-tête Entreprise */}
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                {company?.name || 'Entreprise Demo'}
              </h1>
              <div className="text-sm text-gray-600 mt-1">
                <p>{company?.address || 'Alger, Algérie'}</p>
                <p>NIF: {company?.nif || '123456789012345'} • RC: {company?.rc || '16/00-1234567'}</p>
              </div>
            </div>
            <div className="text-right">
              <CardTitle className="text-lg text-blue-900">BULLETIN DE PAIE</CardTitle>
              <p className="text-sm text-blue-700">
                {getMonthName(payrollData.period.month)} {payrollData.period.year}
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          {/* Informations Employé */}
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">EMPLOYÉ</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Nom:</span> {payrollData.employee.name}</p>
                <p><span className="font-medium">Poste:</span> {payrollData.employee.position}</p>
                <p><span className="font-medium">Matricule:</span> {payrollData.employee.id}</p>
              </div>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">PÉRIODE</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-medium">Mois:</span> {getMonthName(payrollData.period.month)} {payrollData.period.year}</p>
                <p><span className="font-medium">Jours travaillés:</span> {payrollData.period.workedDays}/{payrollData.period.workingDays}</p>
                <Badge variant="secondary">Actif</Badge>
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Détail des Gains et Retenues */}
          <div className="grid grid-cols-2 gap-6">
            {/* Gains */}
            <div>
              <h3 className="font-semibold text-green-800 mb-3">💰 GAINS</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Salaire de base:</span>
                  <span className="font-medium">{formatCurrency(payrollData.earnings.basicSalary)}</span>
                </div>
                
                {payrollData.earnings.overtime > 0 && (
                  <div className="flex justify-between">
                    <span>Heures supplémentaires:</span>
                    <span className="font-medium">{formatCurrency(payrollData.earnings.overtime)}</span>
                  </div>
                )}

                {payrollData.earnings.allowances.transport > 0 && (
                  <div className="flex justify-between">
                    <span>Indemnité transport:</span>
                    <span className="font-medium">{formatCurrency(payrollData.earnings.allowances.transport)}</span>
                  </div>
                )}

                {payrollData.earnings.allowances.meal > 0 && (
                  <div className="flex justify-between">
                    <span>Indemnité panier:</span>
                    <span className="font-medium">{formatCurrency(payrollData.earnings.allowances.meal)}</span>
                  </div>
                )}

                {payrollData.earnings.allowances.family > 0 && (
                  <div className="flex justify-between">
                    <span>Allocation familiale:</span>
                    <span className="font-medium">{formatCurrency(payrollData.earnings.allowances.family)}</span>
                  </div>
                )}

                {payrollData.earnings.allowances.housing > 0 && (
                  <div className="flex justify-between">
                    <span>Indemnité logement:</span>
                    <span className="font-medium">{formatCurrency(payrollData.earnings.allowances.housing)}</span>
                  </div>
                )}

                <Separator className="my-2" />
                <div className="flex justify-between font-semibold text-green-800">
                  <span>TOTAL GAINS:</span>
                  <span>{formatCurrency(payrollData.earnings.grossSalary)}</span>
                </div>
              </div>
            </div>

            {/* Retenues */}
            <div>
              <h3 className="font-semibold text-red-800 mb-3">📉 RETENUES</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Cotisation CNAS (9%):</span>
                  <span className="font-medium">{formatCurrency(payrollData.deductions.cnas.employee)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span>IRG (Progressif):</span>
                  <span className="font-medium">{formatCurrency(payrollData.deductions.irg.amount)}</span>
                </div>

                {payrollData.deductions.other > 0 && (
                  <div className="flex justify-between">
                    <span>Autres retenues:</span>
                    <span className="font-medium">{formatCurrency(payrollData.deductions.other)}</span>
                  </div>
                )}

                <Separator className="my-2" />
                <div className="flex justify-between font-semibold text-red-800">
                  <span>TOTAL RETENUES:</span>
                  <span>{formatCurrency(payrollData.deductions.totalDeductions)}</span>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-6" />

          {/* Net à payer */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-green-800">NET À PAYER:</span>
              <span className="text-2xl font-bold text-green-900">{formatCurrency(payrollData.netSalary)}</span>
            </div>
            <div className="mt-2 text-sm text-green-700">
              <p>Charges patronales CNAS (25%): {formatCurrency(payrollData.employerCharges.cnas)}</p>
              <p>Coût total employeur: {formatCurrency(payrollData.totalCost)}</p>
            </div>
          </div>

          {/* Détail IRG */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-blue-900 mb-2">📊 Détail calcul IRG</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>Revenu imposable: {formatCurrency(payrollData.deductions.irg.taxableIncome)}</p>
              <p>Taux effectif: {payrollData.deductions.irg.rate}%</p>
              <p>Montant IRG: {formatCurrency(payrollData.deductions.irg.amount)}</p>
            </div>
          </div>

          {/* Pied de page avec signatures et QR */}
          <div className="mt-8 pt-6 border-t">
            <div className="grid grid-cols-3 gap-6 items-end">
              {/* Cachet */}
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded p-6 mb-2 min-h-[80px] flex items-center justify-center">
                  <span className="text-gray-400 text-xs">Cachet entreprise</span>
                </div>
                <p className="text-xs text-gray-600">Cachet et signature</p>
              </div>

              {/* Signature directeur */}
              <div className="text-center">
                <div className="border-2 border-dashed border-gray-300 rounded p-6 mb-2 min-h-[80px] flex items-center justify-center">
                  <span className="text-gray-400 text-xs">Signature Directeur</span>
                </div>
                <p className="text-xs text-gray-600">Directeur Général</p>
              </div>

              {/* QR Code */}
              <div className="text-center">
                <div className="border border-gray-300 rounded p-4 mb-2 bg-white flex items-center justify-center min-h-[80px]">
                  <div className="text-center">
                    <QrCode className="w-12 h-12 mx-auto mb-1 text-gray-600" />
                    <p className="text-xs text-gray-500 font-mono">{generateQRCode()}</p>
                  </div>
                </div>
                <p className="text-xs text-gray-600">Code vérification</p>
              </div>
            </div>

            {/* Mentions légales */}
            <div className="mt-6 pt-4 border-t text-xs text-gray-500 text-center">
              <p>Bulletin conforme à la réglementation algérienne • Généré le {new Date().toLocaleDateString('fr-DZ')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
