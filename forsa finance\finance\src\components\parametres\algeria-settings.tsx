'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Save, Building2, FileText, Calculator, Banknote } from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { ALGERIA_CONFIG, validateNIF, validateRC } from '@/lib/algeria-config';

const algeriaSettingsSchema = z.object({
  // Informations entreprise
  companyName: z.string().min(1, 'Le nom de l\'entreprise est requis'),
  nif: z.string().refine(validateNIF, 'Le NIF doit contenir 15 chiffres'),
  nis: z.string().min(18, 'Le NIS doit contenir 18 chiffres').max(18, 'Le NIS doit contenir 18 chiffres'),
  rc: z.string().refine(validateRC, 'Le RC doit être au format WW/YY-NNNNNNN'),
  ai: z.string().optional(),
  sector: z.string().min(1, 'Le secteur d\'activité est requis'),
  wilaya: z.string().min(1, 'La wilaya est requise'),
  address: z.string().min(1, 'L\'adresse est requise'),
  phone: z.string().min(1, 'Le téléphone est requis'),
  email: z.string().email('Email invalide'),
  
  // Paramètres fiscaux
  vatRegistered: z.boolean(),
  vatNumber: z.string().optional(),
  vatRegime: z.enum(['standard', 'reduced', 'exempt']),
  
  // Paramètres comptables
  fiscalYearStart: z.string(),
  accountingMethod: z.enum(['accrual', 'cash']),
  currency: z.literal('DZD'),
  
  // Paramètres sociaux
  cnasNumber: z.string().min(1, 'Le numéro CNAS est requis'),
  cacobatphApplicable: z.boolean(),
  
  // Paramètres bancaires
  mainBankAccount: z.string().optional(),
});

type AlgeriaSettingsFormValues = z.infer<typeof algeriaSettingsSchema>;

export function AlgeriaSettings() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const form = useForm<AlgeriaSettingsFormValues>({
    resolver: zodResolver(algeriaSettingsSchema),
    defaultValues: {
      companyName: '',
      nif: '',
      nis: '',
      rc: '',
      ai: '',
      sector: '',
      wilaya: '16', // Alger par défaut
      address: '',
      phone: '',
      email: '',
      vatRegistered: false,
      vatNumber: '',
      vatRegime: 'standard',
      fiscalYearStart: '01-01',
      accountingMethod: 'accrual',
      currency: 'DZD',
      cnasNumber: '',
      cacobatphApplicable: false,
      mainBankAccount: '',
    },
  });

  const watchVatRegistered = form.watch('vatRegistered');
  const watchSector = form.watch('sector');

  const onSubmit = async (values: AlgeriaSettingsFormValues) => {
    setIsLoading(true);
    
    try {
      // Ici, vous sauvegarderez les paramètres dans la base de données
      console.log('Paramètres Algérie:', values);
      
      toast({
        title: "Paramètres sauvegardés",
        description: "Les paramètres spécifiques à l'Algérie ont été mis à jour avec succès.",
      });
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de la sauvegarde des paramètres.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Paramètres Algérie
          </CardTitle>
          <CardDescription>
            Configuration spécifique aux réglementations algériennes et au système comptable financier (SCF).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              
              {/* Informations entreprise */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  <h3 className="text-lg font-semibold">Informations Entreprise</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="companyName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Raison sociale</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: SARL FORSA FINANCE" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="sector"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Secteur d'activité</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner le secteur" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {ALGERIA_CONFIG.businessSectors.map((sector) => (
                              <SelectItem key={sector.code} value={sector.code}>
                                {sector.code} - {sector.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nif"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>NIF (Numéro d'Identification Fiscale)</FormLabel>
                        <FormControl>
                          <Input placeholder="123456789012345" maxLength={15} {...field} />
                        </FormControl>
                        <FormDescription>15 chiffres</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nis"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>NIS (Numéro d'Identification Statistique)</FormLabel>
                        <FormControl>
                          <Input placeholder="123456789012345678" maxLength={18} {...field} />
                        </FormControl>
                        <FormDescription>18 chiffres</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="rc"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RC (Registre de Commerce)</FormLabel>
                        <FormControl>
                          <Input placeholder="16/00-1234567" {...field} />
                        </FormControl>
                        <FormDescription>Format: WW/YY-NNNNNNN</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="ai"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>AI (Article d'Imposition) - Optionnel</FormLabel>
                        <FormControl>
                          <Input placeholder="123456789012345678" maxLength={18} {...field} />
                        </FormControl>
                        <FormDescription>18 chiffres (si applicable)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="wilaya"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Wilaya</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner la wilaya" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {ALGERIA_CONFIG.wilayas.map((wilaya) => (
                              <SelectItem key={wilaya.code} value={wilaya.code}>
                                {wilaya.code} - {wilaya.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Adresse complète</FormLabel>
                        <FormControl>
                          <Input placeholder="123 Rue de la République, Alger" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Téléphone</FormLabel>
                        <FormControl>
                          <Input placeholder="+213 21 XX XX XX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* Paramètres fiscaux */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <h3 className="text-lg font-semibold">Paramètres Fiscaux</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vatRegistered"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Assujetti à la TVA</FormLabel>
                          <FormDescription>
                            Cochez si votre entreprise est assujettie à la TVA
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  {watchVatRegistered && (
                    <>
                      <FormField
                        control={form.control}
                        name="vatNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Numéro TVA</FormLabel>
                            <FormControl>
                              <Input placeholder="Numéro d'identification TVA" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="vatRegime"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Régime TVA</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner le régime" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="standard">
                                  Standard (19%)
                                </SelectItem>
                                <SelectItem value="reduced">
                                  Réduit (9%)
                                </SelectItem>
                                <SelectItem value="exempt">
                                  Exonéré (0%)
                                </SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </div>
              </div>

              <Separator />

              {/* Paramètres comptables */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  <h3 className="text-lg font-semibold">Paramètres Comptables</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="fiscalYearStart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Début exercice fiscal</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="01-01">1er Janvier</SelectItem>
                            <SelectItem value="07-01">1er Juillet</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="accountingMethod"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Méthode comptable</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="accrual">
                              Comptabilité d'engagement (SCF)
                            </SelectItem>
                            <SelectItem value="cash">
                              Comptabilité de trésorerie
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Devise</FormLabel>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <Input value="DZD" disabled />
                            <Badge>Dinar Algérien</Badge>
                          </div>
                        </FormControl>
                        <FormDescription>
                          Devise officielle de l'Algérie
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* Paramètres sociaux */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Banknote className="h-4 w-4" />
                  <h3 className="text-lg font-semibold">Paramètres Sociaux</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="cnasNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Numéro CNAS</FormLabel>
                        <FormControl>
                          <Input placeholder="Numéro d'affiliation CNAS" {...field} />
                        </FormControl>
                        <FormDescription>
                          Numéro d'affiliation à la Caisse Nationale des Assurances Sociales
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="cacobatphApplicable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>CACOBATPH applicable</FormLabel>
                          <FormDescription>
                            Cochez si votre entreprise est dans le secteur BTP
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                
                {/* Affichage des taux en vigueur */}
                <div className="bg-muted/50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Taux de cotisations en vigueur (2024)</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">CNAS Employé:</span> 9%
                    </div>
                    <div>
                      <span className="font-medium">CNAS Employeur:</span> 25%
                    </div>
                    {watchSector === 'F' && (
                      <div>
                        <span className="font-medium">CACOBATPH:</span> 1.5%
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Save className="mr-2 h-4 w-4 animate-spin" />
                      Sauvegarde...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Sauvegarder les paramètres
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
