'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

export function CompanyProfile() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profil de l'entreprise</CardTitle>
        <CardDescription>Mettez à jour les informations de votre entreprise.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
                <Label htmlFor="companyName">Raison Sociale</Label>
                <Input id="companyName" defaultValue="SARL ForsaTech" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="nif">NIF (Numéro d'Identification Fiscale)</Label>
                <Input id="nif" defaultValue="001234567890123" />
            </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
                <Label htmlFor="nis">NIS (Numéro d'Identification Statistique)</Label>
                <Input id="nis" defaultValue="987654321098765" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="rc">RC (Numéro de Registre de Commerce)</Label>
                <Input id="rc" defaultValue="16/00-123456 A 23" />
            </div>
        </div>
        <div className="space-y-2">
            <Label htmlFor="address">Adresse</Label>
            <Textarea id="address" defaultValue="Cité 1000 Logements, Bab Ezzouar, Alger, Algérie" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
                <Label htmlFor="phone">Téléphone</Label>
                <Input id="phone" type="tel" defaultValue="+213 21 23 45 67" />
            </div>
            <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" defaultValue="<EMAIL>" />
            </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button>Enregistrer les modifications</Button>
      </CardFooter>
    </Card>
  )
}
