import { Card, CardContent, CardDescription, CardHeader, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import Image from "next/image";

const integrations = [
    {
        name: "Synchronisation Bancaire",
        description: "Synchronisez automatiquement vos relevés bancaires.",
        icon: "https://placehold.co/40x40.png",
        hint: "bank sync"
    },
    {
        name: "Google Drive",
        description: "Sauvegardez vos rapports et documents sur Drive.",
        icon: "https://placehold.co/40x40.png",
        hint: "google drive"
    },
    {
        name: "Slack",
        description: "Recevez des notifications financières sur Slack.",
        icon: "https://placehold.co/40x40.png",
        hint: "slack logo"
    }
];

export function Integrations() {
    return (
        <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {integrations.map(integration => (
                <Card key={integration.name}>
                    <CardHeader>
                        <div className="flex items-start gap-4">
                            <Image src={integration.icon} alt={integration.name} width={40} height={40} className="rounded-md" data-ai-hint={integration.hint} />
                            <div>
                                <CardTitle>{integration.name}</CardTitle>
                                <CardDescription>{integration.description}</CardDescription>
                            </div>
                        </div>
                    </CardHeader>
                    <CardFooter className="flex justify-between items-center">
                        <Button variant="outline" size="sm">Gérer</Button>
                        <Switch />
                    </CardFooter>
                </Card>
            ))}
        </div>
    )
}
