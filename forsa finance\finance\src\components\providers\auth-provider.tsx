'use client';

import { createContext, useContext, useEffect, useState } from 'react';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
  role: string;
  permissions: string[];
  company_id: string;
  is_active: boolean;
  last_login?: string;
  phone?: string;
  address?: string;
}

interface Company {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  nif?: string;
  rc?: string;
  logo_url?: string;
  website?: string;
  industry?: string;
  employee_count?: number;
  settings?: Record<string, any>;
}

interface AuthContextType {
  user: User | null;
  company: Company | null;
  session: any | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [session, setSession] = useState<any | null>(null);
  const [loading, setLoading] = useState(false);

  // Données par défaut pour le mode démo
  const defaultUser: User = {
    id: 'offline-user',
    email: '<EMAIL>',
    first_name: 'Admin',
    last_name: 'Local',
    role: 'admin',
    permissions: ['all'],
    company_id: 'default-company',
    is_active: true,
    last_login: new Date().toISOString(),
  };

  const defaultCompany: Company = {
    id: 'default-company',
    name: 'Entreprise Demo',
    address: 'Alger, Algérie',
    phone: '+213 21 XX XX XX',
    email: '<EMAIL>',
    nif: '123456789012345',
    rc: '16/00-1234567',
    industry: 'Services',
    employee_count: 10,
  };

  const defaultSession = {
    user: {
      id: defaultUser.id,
      email: defaultUser.email,
    },
    access_token: 'local-token',
    expires_at: Date.now() + (24 * 60 * 60 * 1000),
  };

  // Connexion (mode démo - accepte n'importe quels identifiants)
  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      
      // Simuler un délai de connexion
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSession(defaultSession);
      setUser(defaultUser);
      setCompany(defaultCompany);
      
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Déconnexion
  const signOut = async () => {
    try {
      setSession(null);
      setUser(null);
      setCompany(null);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    }
  };

  // Actualiser les données utilisateur
  const refreshUser = async () => {
    // En mode démo, rien à actualiser
  };

  // Initialiser automatiquement la session au démarrage
  useEffect(() => {
    setSession(defaultSession);
    setUser(defaultUser);
    setCompany(defaultCompany);
  }, []);

  const value: AuthContextType = {
    user,
    company,
    session,
    loading,
    signIn,
    signOut,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
