'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export type NewEmployee = {
    name: string;
    email: string;
    phone: string;
    position: string;
    department: string;
    salary: number;
    hireDate: string;
    socialSecurityNumber: string;
};

type AddEmployeeDialogProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onAddEmployee: (employee: NewEmployee) => void;
    editingEmployee?: any;
};

export function AddEmployeeDialog({ open, onOpenChange, onAddEmployee, editingEmployee }: AddEmployeeDialogProps) {
  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const formData = new FormData(event.currentTarget);
    
    const newEmployee: NewEmployee = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      position: formData.get('position') as string,
      department: formData.get('department') as string,
      salary: parseInt(formData.get('salary') as string, 10) || 0,
      hireDate: formData.get('hireDate') as string,
      socialSecurityNumber: formData.get('socialSecurityNumber') as string,
    };
    onAddEmployee(newEmployee);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{editingEmployee ? 'Modifier l\'employé' : 'Ajouter un employé'}</DialogTitle>
          <DialogDescription>
            Remplissez les informations de l'employé.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nom complet</Label>
              <Input 
                id="name" 
                name="name" 
                defaultValue={editingEmployee?.name || ''} 
                required 
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  defaultValue={editingEmployee?.email || ''} 
                  required 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Téléphone</Label>
                <Input 
                  id="phone" 
                  name="phone" 
                  defaultValue={editingEmployee?.phone || ''} 
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="position">Poste</Label>
                <Input 
                  id="position" 
                  name="position" 
                  defaultValue={editingEmployee?.position || ''} 
                  required 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">Département</Label>
                <Select name="department" defaultValue={editingEmployee?.department || ''}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionnez le département" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Direction">Direction</SelectItem>
                    <SelectItem value="Ressources Humaines">Ressources Humaines</SelectItem>
                    <SelectItem value="Comptabilité">Comptabilité</SelectItem>
                    <SelectItem value="Commercial">Commercial</SelectItem>
                    <SelectItem value="Technique">Technique</SelectItem>
                    <SelectItem value="Production">Production</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="salary">Salaire (DZD)</Label>
                <Input 
                  id="salary" 
                  name="salary" 
                  type="number" 
                  defaultValue={editingEmployee?.salary || ''} 
                  required 
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="hireDate">Date d'embauche</Label>
                <Input 
                  id="hireDate" 
                  name="hireDate" 
                  type="date" 
                  defaultValue={editingEmployee?.hireDate || new Date().toISOString().split('T')[0]} 
                  required 
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="socialSecurityNumber">N° Sécurité Sociale</Label>
              <Input
                id="socialSecurityNumber"
                name="socialSecurityNumber"
                defaultValue={editingEmployee?.socialSecurityNumber || ''}
                placeholder="123456789012345"
                maxLength={15}
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Annuler
              </Button>
            </DialogClose>
            <Button type="submit">
              {editingEmployee ? 'Mettre à jour' : 'Ajouter l\'employé'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
