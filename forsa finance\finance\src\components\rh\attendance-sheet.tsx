'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Calendar, Printer, Download, Users, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { localDatabase } from '@/lib/database';

type Employee = {
  id: string;
  name: string;
  position: string;
  department: string;
  employee_number: string;
};

type AttendanceRecord = {
  employeeId: string;
  employeeName: string;
  signature: string;
  present: boolean;
  notes: string;
};

export function AttendanceSheet() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedMonth, setSelectedMonth] = useState(new Date().toISOString().substring(0, 7));
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    try {
      const employeesData = localDatabase.getTable('employees');
      const formattedEmployees = employeesData.map(emp => ({
        id: emp.id,
        name: `${emp.first_name} ${emp.last_name}`,
        position: emp.position,
        department: emp.department,
        employee_number: emp.employee_number,
      }));
      setEmployees(formattedEmployees);
      
      // Initialiser les enregistrements de présence
      const initialRecords = formattedEmployees.map(emp => ({
        employeeId: emp.id,
        employeeName: emp.name,
        signature: '',
        present: false,
        notes: '',
      }));
      setAttendanceRecords(initialRecords);
    } catch (error) {
      console.error('Erreur lors du chargement des employés:', error);
    }
  };

  const generateDailyAttendanceSheet = () => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feuille d'Émargement - ${new Date(selectedDate).toLocaleDateString('fr-FR')}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        @page {
            size: A4;
            margin: 20mm;
        }
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 15px;
        }
        .header h1 {
            font-size: 18px;
            color: #1e40af;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        .info-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 25px;
        }
        .info-box {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
        }
        .info-box label {
            font-weight: bold;
            color: #1e40af;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #1e40af;
        }
        .signature-cell {
            width: 150px;
            height: 40px;
            border: 1px solid #ddd;
        }
        .notes-cell {
            width: 120px;
        }
        .footer {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        .signature-section {
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-top: 40px;
            padding-top: 5px;
            font-size: 10px;
        }
        @media print {
            body { print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>FEUILLE D'ÉMARGEMENT</h1>
        <p>Entreprise Demo SARL</p>
        <p>123 Rue de l'Indépendance, Alger 16000, Algérie</p>
    </div>

    <div class="info-section">
        <div class="info-box">
            <label>Date:</label> ${new Date(selectedDate).toLocaleDateString('fr-FR', { 
              weekday: 'long', 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric' 
            })}
        </div>
        <div class="info-box">
            <label>Nombre d'employés:</label> ${employees.length}
        </div>
    </div>

    <table>
        <thead>
            <tr>
                <th style="width: 40px;">N°</th>
                <th style="width: 80px;">Matricule</th>
                <th>Nom et Prénom</th>
                <th>Poste</th>
                <th>Département</th>
                <th>Heure d'arrivée</th>
                <th>Heure de départ</th>
                <th class="signature-cell">Signature</th>
                <th class="notes-cell">Observations</th>
            </tr>
        </thead>
        <tbody>
            ${employees.map((employee, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td>${employee.employee_number}</td>
                    <td><strong>${employee.name}</strong></td>
                    <td>${employee.position}</td>
                    <td>${employee.department}</td>
                    <td style="height: 35px;"></td>
                    <td style="height: 35px;"></td>
                    <td class="signature-cell"></td>
                    <td class="notes-cell"></td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="footer">
        <div class="signature-section">
            <p><strong>Responsable RH</strong></p>
            <div class="signature-line">Nom et Signature</div>
        </div>
        <div class="signature-section">
            <p><strong>Directeur</strong></p>
            <div class="signature-line">Nom et Signature</div>
        </div>
    </div>

    <div style="margin-top: 30px; text-align: center; font-size: 10px; color: #666;">
        Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}
    </div>
</body>
</html>`;

    printWindow.document.write(html);
    printWindow.document.close();
    
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

    toast({
      title: "Feuille d'émargement générée",
      description: "La feuille d'émargement a été ouverte pour impression.",
    });
  };

  const generateMonthlyAttendanceSheet = () => {
    const daysInMonth = new Date(
      parseInt(selectedMonth.split('-')[0]), 
      parseInt(selectedMonth.split('-')[1]), 
      0
    ).getDate();
    
    const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);

    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const html = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Feuille de Présence Mensuelle - ${new Date(selectedMonth).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' })}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        @page {
            size: A4 landscape;
            margin: 15mm;
        }
        body {
            font-family: 'Arial', sans-serif;
            font-size: 10px;
            line-height: 1.2;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #1e40af;
            padding-bottom: 10px;
        }
        .header h1 {
            font-size: 16px;
            color: #1e40af;
            margin-bottom: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 3px;
            text-align: center;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #1e40af;
        }
        .employee-name {
            text-align: left;
            font-weight: bold;
            width: 120px;
        }
        .day-cell {
            width: 20px;
            height: 25px;
        }
        .totals {
            background-color: #f0f9ff;
            font-weight: bold;
        }
        @media print {
            body { print-color-adjust: exact; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>FEUILLE DE PRÉSENCE MENSUELLE</h1>
        <p>Entreprise Demo SARL - ${new Date(selectedMonth).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' })}</p>
    </div>

    <table>
        <thead>
            <tr>
                <th rowspan="2" class="employee-name">Employé</th>
                <th rowspan="2">Poste</th>
                ${days.map(day => `<th class="day-cell">${day}</th>`).join('')}
                <th rowspan="2">Total</th>
            </tr>
        </thead>
        <tbody>
            ${employees.map(employee => `
                <tr>
                    <td class="employee-name">${employee.name}</td>
                    <td>${employee.position}</td>
                    ${days.map(() => `<td class="day-cell"></td>`).join('')}
                    <td class="totals"></td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div style="margin-top: 20px; font-size: 8px;">
        <p><strong>Légende:</strong> P = Présent, A = Absent, R = Retard, C = Congé, M = Maladie</p>
        <p style="margin-top: 10px;">Document généré le ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}</p>
    </div>
</body>
</html>`;

    printWindow.document.write(html);
    printWindow.document.close();
    
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

    toast({
      title: "Feuille de présence mensuelle générée",
      description: "La feuille de présence mensuelle a été ouverte pour impression.",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Feuilles d'Émargement</h2>
          <p className="text-muted-foreground">
            Génération des feuilles de présence et d'émargement
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Feuille d'émargement quotidienne */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Feuille d'Émargement Quotidienne
            </CardTitle>
            <CardDescription>
              Générer une feuille d'émargement pour une date spécifique
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="daily-date">Date</Label>
              <Input
                id="daily-date"
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Aperçu</Label>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>• Date: {new Date(selectedDate).toLocaleDateString('fr-FR')}</p>
                <p>• Employés: {employees.length}</p>
                <p>• Colonnes: Matricule, Nom, Poste, Heures, Signature</p>
              </div>
            </div>

            <Button 
              onClick={generateDailyAttendanceSheet}
              className="w-full"
            >
              <Printer className="h-4 w-4 mr-2" />
              Générer et Imprimer
            </Button>
          </CardContent>
        </Card>

        {/* Feuille de présence mensuelle */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Feuille de Présence Mensuelle
            </CardTitle>
            <CardDescription>
              Générer une feuille de présence pour tout un mois
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="monthly-date">Mois</Label>
              <Input
                id="monthly-date"
                type="month"
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Aperçu</Label>
              <div className="text-sm text-muted-foreground space-y-1">
                <p>• Mois: {new Date(selectedMonth).toLocaleDateString('fr-FR', { year: 'numeric', month: 'long' })}</p>
                <p>• Employés: {employees.length}</p>
                <p>• Format: Paysage A4 avec grille quotidienne</p>
              </div>
            </div>

            <Button 
              onClick={generateMonthlyAttendanceSheet}
              className="w-full"
            >
              <Printer className="h-4 w-4 mr-2" />
              Générer et Imprimer
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Liste des employés */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Employés Actifs ({employees.length})
          </CardTitle>
          <CardDescription>
            Liste des employés qui apparaîtront sur les feuilles
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Matricule</TableHead>
                <TableHead>Nom et Prénom</TableHead>
                <TableHead>Poste</TableHead>
                <TableHead>Département</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {employees.map((employee) => (
                <TableRow key={employee.id}>
                  <TableCell className="font-mono">{employee.employee_number}</TableCell>
                  <TableCell className="font-medium">{employee.name}</TableCell>
                  <TableCell>{employee.position}</TableCell>
                  <TableCell>{employee.department}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
