
'use client';

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, TableFooter } from "@/components/ui/table";
import { Printer, FileDown, ArrowLeft } from "lucide-react";
import { employees, type Employee } from "@/data/employees";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import QRCode from 'qrcode';


const reportData = employees.map(emp => {
    // These are simplified annual calculations for the main report view.
    const annualNet = (emp.salary || 0) * 12;
    // Reverse calculation to get an approximate annual gross
    const annualGross = annualNet / 0.75; // Approximation, assuming ~25% total deductions
    const annualCnas = annualGross * 0.09;
    const annualIrg = (annualGross - annualCnas) * 0.15; // Approximation
    return {
        ...emp,
        gross: annualGross,
        net: annualNet,
        cnas: annualCnas,
        irg: annualIrg,
    };
});

const formatCurrency = (amount: number) => {
    if (amount === 0) return '0.00';
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const generateMonthlyBreakdown = (employee: Employee) => {
    const monthlyData = [];
    const baseNetSalary = employee.salary || 0; 
    const childAllowance = (employee.children || 0) * 600; // Simplified child allowance calculation

    for (let i = 0; i < 12; i++) {
        const month = new Date(0, i).toLocaleString('fr-FR', { month: 'long' });
        
        // This is a simulation based on reverse-calculating from a net salary
        // It mirrors the logic one might use for payslip generation
        const nonTaxableAllowance = 5000 + childAllowance;
        const netToPay = baseNetSalary;
        const grossTaxableEstimate = (netToPay - nonTaxableAllowance + 8000) / 0.85; // A more refined estimation
        const cnas = grossTaxableEstimate * 0.09;
        const taxableIncome = grossTaxableEstimate - cnas;
        const irg = taxableIncome * 0.18; // Simplified IRG rate for simulation
        const grossSubjectToContributions = grossTaxableEstimate;
        const iep = grossSubjectToContributions * 0.1; // 10% IEP
        const base = grossSubjectToContributions - iep;
        const postSalary = base + iep;

        monthlyData.push({
            month,
            baseSalary: base,
            iep: iep,
            postSalary: postSalary,
            grossSubjectToContributions: grossSubjectToContributions,
            cnas: cnas,
            taxableIncome: taxableIncome,
            irg: irg,
            nonTaxableAllowance: nonTaxableAllowance,
            net: netToPay
        });
    }
    return monthlyData;
}

const companyInfo = {
    name: "SARL ForsaTech",
    address: "Cité 1000 Logements, Bab Ezzouar, Alger, Algérie",
    nif: "001234567890123",
};

export function EmolumentsReport() {
    const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
    const [employeeDetails, setEmployeeDetails] = useState<any[]>([]);
    const [qrCodeDataUri, setQrCodeDataUri] = useState<string>('');

    useEffect(() => {
        if (selectedEmployee) {
            const details = generateMonthlyBreakdown(selectedEmployee);
            setEmployeeDetails(details);
            
            const generateQr = async () => {
                const totalNet = details.reduce((acc, item) => acc + item.net, 0);
                const qrContent = `Relevé des Émoluments: ${selectedEmployee.name} - Année ${new Date().getFullYear()}. Net Annuel: ${formatCurrency(totalNet)} DZD. Société: ${companyInfo.name}`;
                const dataUri = await QRCode.toDataURL(qrContent);
                setQrCodeDataUri(dataUri);
            };
            generateQr();
        }
    }, [selectedEmployee]);


    const handleSelectEmployee = (employee: Employee) => {
        setSelectedEmployee(employee);
    };

    const handleBackToList = () => {
        setSelectedEmployee(null);
        setEmployeeDetails([]);
        setQrCodeDataUri('');
    };

    const handlePrintReport = () => {
        if (!selectedEmployee) return;

        // Créer une nouvelle fenêtre pour l'impression
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) return;

        // Générer le HTML du relevé pour impression
        const printHTML = generatePrintableReport();

        printWindow.document.write(printHTML);
        printWindow.document.close();

        // Attendre que le contenu soit chargé puis imprimer
        printWindow.onload = () => {
            printWindow.print();
            printWindow.close();
        };
    };

    const generatePrintableReport = () => {
        if (!selectedEmployee) return '';

        return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relevé des Émoluments - ${selectedEmployee.name}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
        }
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }
        .header {
            background: #1e40af;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        .company-info h1 { font-size: 18px; margin-bottom: 8px; }
        .company-info p { font-size: 11px; margin-bottom: 4px; opacity: 0.9; }
        .report-title { text-align: right; }
        .report-title h2 { font-size: 16px; margin-bottom: 8px; }
        .employee-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .info-section h3 { font-size: 14px; margin-bottom: 10px; color: #1e40af; }
        .info-section p { margin-bottom: 5px; }
        .emoluments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .emoluments-table th, .emoluments-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .emoluments-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .emoluments-table .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        .emoluments-table .total-row {
            background: #e9ecef;
            font-weight: bold;
        }
        .signatures {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
        }
        .signature-box {
            text-align: center;
        }
        .signature-area {
            height: 80px;
            border: 2px dashed #ccc;
            border-radius: 5px;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 10px;
        }
        .qr-code {
            height: 80px;
            border: 1px solid #ccc;
            border-radius: 5px;
            margin-bottom: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: white;
        }
        .qr-placeholder {
            width: 40px;
            height: 40px;
            background: #e5e7eb;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
        }
        .signature-box p {
            font-size: 10px;
            color: #666;
        }
        .legal-mentions {
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        .legal-mentions p {
            margin-bottom: 3px;
        }
        @media print {
            body { padding: 0; margin: 0; }
            .report-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- En-tête -->
        <div class="header">
            <div class="company-info">
                <h1>${companyInfo.name}</h1>
                <p>${companyInfo.address}</p>
                <p>NIF: ${companyInfo.nif}</p>
            </div>
            <div class="report-title">
                <h2>RELEVÉ DES ÉMOLUMENTS</h2>
                <p>Année ${new Date().getFullYear()}</p>
            </div>
        </div>

        <!-- Informations Employé -->
        <div class="employee-info">
            <div class="info-section">
                <h3>Informations Employé</h3>
                <p><strong>Nom:</strong> ${selectedEmployee.name}</p>
                <p><strong>Poste:</strong> ${selectedEmployee.position || 'Non spécifié'}</p>
                <p><strong>Date d'embauche:</strong> ${selectedEmployee.hireDate || 'Non spécifiée'}</p>
            </div>
            <div class="info-section">
                <h3>Informations Entreprise</h3>
                <p><strong>Entreprise:</strong> ${companyInfo.name}</p>
                <p><strong>Adresse:</strong> ${companyInfo.address}</p>
                <p><strong>NIF:</strong> ${companyInfo.nif}</p>
            </div>
        </div>

        <!-- Tableau des émoluments -->
        <table class="emoluments-table">
            <thead>
                <tr>
                    <th>Mois</th>
                    <th style="text-align: right;">Sal. de Base</th>
                    <th style="text-align: right;">Brut Soumis</th>
                    <th style="text-align: right;">Ret. CNAS</th>
                    <th style="text-align: right;">Ret. IRG</th>
                    <th style="text-align: right;">Indemnités NS</th>
                    <th style="text-align: right;">Net Payé</th>
                </tr>
            </thead>
            <tbody>
                ${employeeDetails.map(item => `
                    <tr>
                        <td style="text-transform: capitalize;">${item.month}</td>
                        <td class="amount">${formatCurrency(item.baseSalary)}</td>
                        <td class="amount">${formatCurrency(item.grossSubjectToContributions)}</td>
                        <td class="amount">${formatCurrency(item.cnas)}</td>
                        <td class="amount">${formatCurrency(item.irg)}</td>
                        <td class="amount">${formatCurrency(item.nonTaxableAllowance)}</td>
                        <td class="amount">${formatCurrency(item.net)}</td>
                    </tr>
                `).join('')}
                <tr class="total-row">
                    <td><strong>Total Annuel</strong></td>
                    <td></td>
                    <td class="amount"><strong>${formatCurrency(detailTotals.gross)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(detailTotals.cnas)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(detailTotals.irg)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(detailTotals.nonTaxable)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(detailTotals.net)}</strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Signatures -->
        <div class="signatures">
            <div class="signature-box">
                <div class="signature-area">Cachet de l'entreprise</div>
                <p>Cachet et signature</p>
            </div>
            <div class="signature-box">
                <div class="signature-area">Signature du Directeur</div>
                <p>Directeur Général</p>
            </div>
            <div class="signature-box">
                <div class="qr-code">
                    <div class="qr-placeholder">QR</div>
                    <div style="font-size: 8px; font-family: monospace;">${selectedEmployee.id}-${new Date().getFullYear()}</div>
                </div>
                <p>Code de vérification</p>
            </div>
        </div>

        <!-- Mentions légales -->
        <div class="legal-mentions">
            <p>Ce relevé des émoluments est conforme à la législation algérienne en vigueur.</p>
            <p>Document généré le ${new Date().toLocaleDateString('fr-DZ')} par Forsa Finance</p>
        </div>
    </div>
</body>
</html>`;
    };

    const handlePrintSummary = () => {
        // Créer une nouvelle fenêtre pour l'impression du résumé
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        if (!printWindow) return;

        // Générer le HTML du résumé pour impression
        const printHTML = generatePrintableSummary();

        printWindow.document.write(printHTML);
        printWindow.document.close();

        // Attendre que le contenu soit chargé puis imprimer
        printWindow.onload = () => {
            printWindow.print();
            printWindow.close();
        };
    };

    const generatePrintableSummary = () => {
        return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relevé des Émoluments - Résumé</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
            padding: 20px;
        }
        .summary-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }
        .header {
            background: #1e40af;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        .header h1 { font-size: 18px; margin-bottom: 8px; }
        .header p { font-size: 11px; opacity: 0.9; }
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .summary-table th, .summary-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .summary-table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .summary-table .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        .summary-table .total-row {
            background: #e9ecef;
            font-weight: bold;
        }
        .legal-mentions {
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        @media print {
            body { padding: 0; margin: 0; }
            .summary-container { max-width: none; }
        }
    </style>
</head>
<body>
    <div class="summary-container">
        <!-- En-tête -->
        <div class="header">
            <h1>RELEVÉ DES ÉMOLUMENTS - RÉSUMÉ</h1>
            <p>${companyInfo.name}</p>
            <p>Année ${new Date().getFullYear()}</p>
        </div>

        <!-- Tableau résumé -->
        <table class="summary-table">
            <thead>
                <tr>
                    <th>Employé</th>
                    <th style="text-align: right;">Brut Annuel</th>
                    <th style="text-align: right;">Cotisation CNAS</th>
                    <th style="text-align: right;">IRG Annuel</th>
                    <th style="text-align: right;">Net Annuel</th>
                </tr>
            </thead>
            <tbody>
                ${reportData.map(item => `
                    <tr>
                        <td>${item.name}</td>
                        <td class="amount">${formatCurrency(item.gross)}</td>
                        <td class="amount">${formatCurrency(item.cnas)}</td>
                        <td class="amount">${formatCurrency(item.irg)}</td>
                        <td class="amount">${formatCurrency(item.net)}</td>
                    </tr>
                `).join('')}
                <tr class="total-row">
                    <td><strong>TOTAUX</strong></td>
                    <td class="amount"><strong>${formatCurrency(totals.gross)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(totals.cnas)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(totals.irg)}</strong></td>
                    <td class="amount"><strong>${formatCurrency(totals.net)}</strong></td>
                </tr>
            </tbody>
        </table>

        <!-- Mentions légales -->
        <div class="legal-mentions">
            <p>Ce relevé des émoluments est conforme à la législation algérienne en vigueur.</p>
            <p>Document généré le ${new Date().toLocaleDateString('fr-DZ')} par Forsa Finance</p>
        </div>
    </div>
</body>
</html>`;
    };

    const totals = reportData.reduce((acc, item) => ({
        gross: acc.gross + item.gross,
        net: acc.net + item.net,
        cnas: acc.cnas + item.cnas,
        irg: acc.irg + item.irg,
    }), { gross: 0, net: 0, cnas: 0, irg: 0 });

    const detailTotals = employeeDetails.reduce((acc, item) => ({
        gross: acc.gross + item.grossSubjectToContributions,
        net: acc.net + item.net,
        cnas: acc.cnas + item.cnas,
        irg: acc.irg + item.irg,
        nonTaxable: acc.nonTaxable + item.nonTaxableAllowance
    }), { gross: 0, net: 0, cnas: 0, irg: 0, nonTaxable: 0 });

    if (selectedEmployee) {
        return (
             <Card>
                <CardHeader>
                    <div className="flex justify-between items-start">
                        <div className="flex items-center gap-4">
                            <Button variant="ghost" size="icon" onClick={handleBackToList}>
                                <ArrowLeft className="h-4 w-4" />
                            </Button>
                            <div>
                                <CardTitle>Relevé des Émoluments - Année {new Date().getFullYear()}</CardTitle>
                                <CardDescription>Détail mensuel pour {selectedEmployee.name}.</CardDescription>
                            </div>
                        </div>
                         <Button variant="outline" onClick={() => handlePrintReport()}>
                            <Printer className="mr-2 h-4 w-4" />
                            Imprimer le Relevé
                        </Button>
                    </div>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="grid grid-cols-2 gap-4 text-sm p-4 border rounded-lg">
                        <div className="space-y-1">
                            <h3 className="font-semibold">{companyInfo.name}</h3>
                            <p className="text-muted-foreground">{companyInfo.address}</p>
                            <p className="text-muted-foreground">NIF: {companyInfo.nif}</p>
                        </div>
                         <div className="space-y-1 text-right">
                            <h3 className="font-semibold">{selectedEmployee.name}</h3>
                            <p className="text-muted-foreground">{selectedEmployee.role}</p>
                            <p className="text-muted-foreground">Date d'embauche: {selectedEmployee.hireDate}</p>
                            <p className="text-muted-foreground">NSS: {selectedEmployee.nss}</p>
                        </div>
                    </div>

                     <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Mois</TableHead>
                                <TableHead className="text-right">Sal. de Base</TableHead>
                                <TableHead className="text-right">Brut Soumis</TableHead>
                                <TableHead className="text-right">Ret. CNAS</TableHead>
                                <TableHead className="text-right">Ret. IRG</TableHead>
                                <TableHead className="text-right">Indemnités NS</TableHead>
                                <TableHead className="text-right">Net Payé</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {employeeDetails.map((item) => (
                                <TableRow key={item.month}>
                                    <TableCell className="font-medium capitalize">{item.month}</TableCell>
                                    <TableCell className="text-right font-mono">{formatCurrency(item.baseSalary)}</TableCell>
                                    <TableCell className="text-right font-mono">{formatCurrency(item.grossSubjectToContributions)}</TableCell>
                                    <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.cnas)}</TableCell>
                                    <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.irg)}</TableCell>
                                    <TableCell className="text-right font-mono">{formatCurrency(item.nonTaxableAllowance)}</TableCell>
                                    <TableCell className="text-right font-mono text-primary">{formatCurrency(item.net)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                         <TableFooter>
                            <TableRow>
                                <TableCell className="font-bold text-lg">Total Annuel</TableCell>
                                <TableCell></TableCell>
                                <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(detailTotals.gross)}</TableCell>
                                <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(detailTotals.cnas)}</TableCell>
                                <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(detailTotals.irg)}</TableCell>
                                <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(detailTotals.nonTaxable)}</TableCell>
                                <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(detailTotals.net)}</TableCell>
                            </TableRow>
                        </TableFooter>
                    </Table>
                    <Separator />
                     <div className="flex justify-between items-end pt-4">
                        <div className="text-center">
                            {qrCodeDataUri && <Image src={qrCodeDataUri} alt="Report QR Code" width={80} height={80} />}
                            <p className="text-xs text-muted-foreground mt-1">Vérification</p>
                        </div>
                        <div className="space-y-10 text-center text-sm">
                            <div className="space-y-1">
                                <p className="font-semibold">Cachet de l'entreprise</p>
                                <div className="h-16 w-32 border-dashed border-2 rounded-md mx-auto"></div>
                            </div>
                            <div className="space-y-1">
                                <p className="font-semibold">Signature du Directeur</p>
                                <div className="h-12 w-48 border-b border-foreground"></div>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        )
    }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Relevé des Émoluments</CardTitle>
        <CardDescription>Générez le relevé annuel des émoluments. Cliquez sur un employé pour voir le détail.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex gap-4 items-end">
            <div className="space-y-2">
                <Label htmlFor="start-date">Date de début</Label>
                <Input id="start-date" type="date" defaultValue={`${new Date().getFullYear()}-01-01`} />
            </div>
             <div className="space-y-2">
                <Label htmlFor="end-date">Date de fin</Label>
                <Input id="end-date" type="date" defaultValue={new Date().toISOString().split('T')[0]} />
            </div>
            <Button>Générer</Button>
        </div>
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Employé</TableHead>
                    <TableHead className="text-right">Brut Annuel</TableHead>
                    <TableHead className="text-right">Cotisation CNAS</TableHead>
                    <TableHead className="text-right">IRG Annuel</TableHead>
                    <TableHead className="text-right">Net Annuel</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                {reportData.map((item) => (
                    <TableRow key={item.id} onClick={() => handleSelectEmployee(item)} className="cursor-pointer">
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell className="text-right font-mono">{formatCurrency(item.gross)}</TableCell>
                        <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.cnas)}</TableCell>
                        <TableCell className="text-right font-mono text-destructive">{formatCurrency(item.irg)}</TableCell>
                        <TableCell className="text-right font-mono text-primary">{formatCurrency(item.net)}</TableCell>
                    </TableRow>
                ))}
            </TableBody>
             <TableFooter>
                <TableRow>
                    <TableCell className="font-bold text-lg">Totaux</TableCell>
                    <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totals.gross)}</TableCell>
                    <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totals.cnas)}</TableCell>
                    <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totals.irg)}</TableCell>
                    <TableCell className="text-right font-bold font-mono text-lg">{formatCurrency(totals.net)}</TableCell>
                </TableRow>
            </TableFooter>
        </Table>
      </CardContent>
       <CardFooter className="flex justify-end gap-2">
        <Button variant="outline">
            <FileDown className="mr-2 h-4 w-4" />
            Exporter (Excel)
        </Button>
        <Button onClick={() => handlePrintSummary()}>
            <Printer className="mr-2 h-4 w-4" />
            Imprimer le Relevé
        </Button>
      </CardFooter>
    </Card>
  )
}
