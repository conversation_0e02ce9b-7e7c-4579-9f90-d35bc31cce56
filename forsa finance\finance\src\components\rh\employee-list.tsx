
'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, UserPlus, ReceiptText } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AddEmployeeDialog, type NewEmployee } from './add-employee-dialog';
import { employees as initialEmployees, type Employee } from '@/data/employees';
import { localDatabase } from '@/lib/database';
import { useToast } from '@/hooks/use-toast';

type EmployeeListProps = {
    onGeneratePayslip: (employee: Employee) => void;
};

export function EmployeeList({ onGeneratePayslip }: EmployeeListProps) {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [employees, setEmployees] = useState<Employee[]>([]);
    const [loading, setLoading] = useState(true);
    const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
    const { toast } = useToast();

    // Charger les employés depuis la base de données locale
    const loadEmployees = async () => {
        try {
            setLoading(true);
            const employeesData = await localDatabase.getEmployees();

            if (employeesData.length === 0) {
                // Si aucun employé en base, initialiser avec les données par défaut
                await localDatabase.initializeEmployees();
                const newEmployeesData = await localDatabase.getEmployees();
                const formattedEmployees = newEmployeesData.map(emp => ({
                    id: emp.id,
                    name: `${emp.first_name} ${emp.last_name}`,
                    email: emp.email,
                    phone: emp.phone,
                    position: emp.position,
                    department: emp.department,
                    salary: emp.salary,
                    hireDate: emp.hire_date,
                    status: emp.status === 'active' ? 'Actif' : 'Inactif',
                    avatar: emp.avatar_url || `https://placehold.co/100x100`,
                    first_name: emp.first_name,
                    last_name: emp.last_name,
                    socialSecurityNumber: emp.social_security_number || '',
                }));
                setEmployees(formattedEmployees);
            } else {
                const formattedEmployees = employeesData.map(emp => ({
                    id: emp.id,
                    name: `${emp.first_name} ${emp.last_name}`,
                    email: emp.email,
                    phone: emp.phone,
                    position: emp.position,
                    department: emp.department,
                    salary: emp.salary,
                    hireDate: emp.hire_date,
                    status: emp.status === 'active' ? 'Actif' : 'Inactif',
                    avatar: emp.avatar_url || `https://placehold.co/100x100`,
                    first_name: emp.first_name,
                    last_name: emp.last_name,
                    socialSecurityNumber: emp.social_security_number || '',
                }));
                setEmployees(formattedEmployees);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des employés:', error);
            // Fallback vers les données initiales
            setEmployees(initialEmployees);
        } finally {
            setLoading(false);
        }
    };

    // Charger les employés au démarrage
    useEffect(() => {
        loadEmployees();
    }, []);

    const handleAddEmployee = async (newEmployee: NewEmployee) => {
        try {
            // Ajouter à la base de données locale
            const employeeData = {
                id: `EMP-${Date.now()}`,
                employee_number: `EMP${Date.now()}`,
                first_name: newEmployee.name.split(' ')[0],
                last_name: newEmployee.name.split(' ').slice(1).join(' ') || '',
                email: newEmployee.email,
                phone: newEmployee.phone,
                address: '',
                position: newEmployee.position,
                department: newEmployee.department,
                hire_date: newEmployee.hireDate,
                salary: newEmployee.salary,
                status: 'active',
                company_id: 'default-company',
                social_security_number: newEmployee.socialSecurityNumber || '',
            };

            await localDatabase.addEmployee(employeeData);

            // Recharger la liste
            await loadEmployees();

            // Fermer le dialog
            setIsDialogOpen(false);

            // Notification de succès
            toast({
                title: "Succès",
                description: "Employé ajouté avec succès",
            });
        } catch (error) {
            console.error('Erreur lors de l\'ajout de l\'employé:', error);
            toast({
                title: "Erreur",
                description: "Erreur lors de l'ajout de l'employé",
                variant: "destructive",
            });
        }
    };

    const handleEditEmployee = (employee: Employee) => {
        setEditingEmployee(employee);
        setIsDialogOpen(true);
    };

    const handleDeleteEmployee = async (employeeId: string) => {
        if (!confirm('Êtes-vous sûr de vouloir supprimer cet employé ?')) {
            return;
        }

        try {
            await localDatabase.deleteEmployee(employeeId);
            await loadEmployees();

            toast({
                title: "Succès",
                description: "Employé supprimé avec succès",
            });
        } catch (error) {
            console.error('Erreur lors de la suppression de l\'employé:', error);
            toast({
                title: "Erreur",
                description: "Erreur lors de la suppression de l'employé",
                variant: "destructive",
            });
        }
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
        setEditingEmployee(null);
    };

    return (
        <>
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Liste des Employés</CardTitle>
                    <CardDescription>Affichez et gérez les informations de vos employés.</CardDescription>
                </div>
                <Button onClick={() => { setEditingEmployee(null); setIsDialogOpen(true); }}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Ajouter un employé
                </Button>
            </CardHeader>
            <CardContent>
                {loading ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="text-muted-foreground">Chargement des employés...</div>
                    </div>
                ) : employees.length === 0 ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="text-center">
                            <div className="text-muted-foreground mb-2">Aucun employé trouvé</div>
                            <Button onClick={() => { setEditingEmployee(null); setIsDialogOpen(true); }} variant="outline">
                                <UserPlus className="mr-2 h-4 w-4" />
                                Ajouter le premier employé
                            </Button>
                        </div>
                    </div>
                ) : (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Nom</TableHead>
                                <TableHead>Poste</TableHead>
                                <TableHead>Département</TableHead>
                                <TableHead>Salaire</TableHead>
                                <TableHead>N° Sécurité Sociale</TableHead>
                                <TableHead>Statut</TableHead>
                                <TableHead><span className="sr-only">Actions</span></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {employees.map((user) => (
                            <TableRow key={user.id}>
                                <TableCell className="font-medium">
                                    <div className="flex items-center gap-3">
                                        <Avatar className="h-9 w-9">
                                            <AvatarImage src={user.avatar} alt={user.name} data-ai-hint="person portrait" />
                                            <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                                        </Avatar>
                                        <div>
                                            {user.name}
                                            <div className="text-sm text-muted-foreground">{user.email}</div>
                                        </div>
                                    </div>
                                </TableCell>
                                <TableCell>{user.position}</TableCell>
                                <TableCell>{user.department}</TableCell>
                                <TableCell>{user.salary?.toLocaleString('fr-DZ')} DZD</TableCell>
                                <TableCell>
                                    <span className="font-mono text-sm">
                                        {user.socialSecurityNumber || 'Non renseigné'}
                                    </span>
                                </TableCell>
                                <TableCell>
                                    <Badge variant={user.status === 'Actif' ? 'secondary' : 'outline'}>
                                        {user.status}
                                    </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                <span className="sr-only">Ouvrir le menu</span>
                                                <MoreHorizontal className="h-4 w-4" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => onGeneratePayslip(user)}>
                                                <ReceiptText className="mr-2 h-4 w-4" />
                                                Générer la fiche de paie
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => handleEditEmployee(user)}>Modifier</DropdownMenuItem>
                                            <DropdownMenuItem
                                                className="text-destructive"
                                                onClick={() => handleDeleteEmployee(user.id)}
                                            >
                                                Supprimer
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
                )}
            </CardContent>
        </Card>
        <AddEmployeeDialog
            open={isDialogOpen}
            onOpenChange={handleCloseDialog}
            onAddEmployee={handleAddEmployee}
            editingEmployee={editingEmployee}
        />
        </>
    );
}
