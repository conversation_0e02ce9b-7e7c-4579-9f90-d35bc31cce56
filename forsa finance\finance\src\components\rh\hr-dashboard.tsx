'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Clock, 
  Calculator, 
  BarChart3, 
  TrendingUp, 
  AlertCircle, 
  CheckCircle,
  Calendar,
  DollarSign,
  Timer,
  FileText
} from 'lucide-react';
import { localDatabase } from '@/lib/database';

type DashboardStats = {
  totalEmployees: number;
  activeEmployees: number;
  todayAttendance: number;
  pendingOvertimeApprovals: number;
  monthlyPayrollTotal: number;
  averageWorkingHours: number;
  departmentCount: number;
  recentActivities: Array<{
    id: string;
    type: 'employee' | 'timesheet' | 'payroll' | 'overtime';
    message: string;
    timestamp: string;
  }>;
};

export function HRDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalEmployees: 0,
    activeEmployees: 0,
    todayAttendance: 0,
    pendingOvertimeApprovals: 0,
    monthlyPayrollTotal: 0,
    averageWorkingHours: 0,
    departmentCount: 0,
    recentActivities: [],
  });

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = () => {
    try {
      // Charger les employés
      const employees = localDatabase.getTable('employees');
      const activeEmployees = employees.filter(emp => emp.status === 'active');
      
      // Charger les pointages du jour
      const today = new Date().toISOString().split('T')[0];
      const timeEntries = JSON.parse(localStorage.getItem('timeEntries') || '[]');
      const todayEntries = timeEntries.filter((entry: any) => entry.date === today);
      
      // Charger les heures supplémentaires en attente
      const overtimeEntries = JSON.parse(localStorage.getItem('overtimeEntries') || '[]');
      const pendingOvertime = overtimeEntries.filter((entry: any) => !entry.approved);
      
      // Calculer le total des salaires mensuels
      const monthlyPayrollTotal = activeEmployees.reduce((sum, emp) => sum + (emp.salary || 0), 0);
      
      // Calculer la moyenne des heures travaillées ce mois
      const currentMonth = new Date().toISOString().substring(0, 7);
      const monthlyEntries = timeEntries.filter((entry: any) => entry.date.startsWith(currentMonth));
      const averageWorkingHours = monthlyEntries.length > 0 
        ? monthlyEntries.reduce((sum: number, entry: any) => sum + entry.totalHours, 0) / monthlyEntries.length
        : 0;
      
      // Compter les départements uniques
      const departments = new Set(activeEmployees.map(emp => emp.department));
      
      // Activités récentes simulées
      const recentActivities = [
        {
          id: '1',
          type: 'employee' as const,
          message: 'Nouvel employé ajouté',
          timestamp: new Date().toISOString(),
        },
        {
          id: '2',
          type: 'timesheet' as const,
          message: `${todayEntries.length} pointages enregistrés aujourd'hui`,
          timestamp: new Date().toISOString(),
        },
        {
          id: '3',
          type: 'overtime' as const,
          message: `${pendingOvertime.length} heures supplémentaires en attente`,
          timestamp: new Date().toISOString(),
        },
      ];

      setStats({
        totalEmployees: employees.length,
        activeEmployees: activeEmployees.length,
        todayAttendance: todayEntries.length,
        pendingOvertimeApprovals: pendingOvertime.length,
        monthlyPayrollTotal,
        averageWorkingHours,
        departmentCount: departments.size,
        recentActivities,
      });
    } catch (error) {
      console.error('Erreur lors du chargement des statistiques:', error);
    }
  };

  const attendanceRate = stats.activeEmployees > 0 
    ? (stats.todayAttendance / stats.activeEmployees) * 100 
    : 0;

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'employee': return <Users className="h-4 w-4" />;
      case 'timesheet': return <Clock className="h-4 w-4" />;
      case 'payroll': return <Calculator className="h-4 w-4" />;
      case 'overtime': return <Timer className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Tableau de Bord RH</h2>
          <p className="text-muted-foreground">
            Vue d'ensemble des ressources humaines et activités
          </p>
        </div>
        <Badge variant="outline" className="text-green-600 border-green-600">
          <CheckCircle className="w-3 h-3 mr-1" />
          Système Opérationnel
        </Badge>
      </div>

      {/* Statistiques principales */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Employés Actifs</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeEmployees}</div>
            <p className="text-xs text-muted-foreground">
              sur {stats.totalEmployees} total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Présence Aujourd'hui</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.todayAttendance}</div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Progress value={attendanceRate} className="flex-1 h-1" />
              <span>{attendanceRate.toFixed(0)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Masse Salariale</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.monthlyPayrollTotal.toLocaleString('fr-DZ')}
            </div>
            <p className="text-xs text-muted-foreground">
              DZD/mois
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Heures Sup. Pending</CardTitle>
            <Timer className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingOvertimeApprovals}</div>
            <p className="text-xs text-muted-foreground">
              en attente d'approbation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Graphiques et détails */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Répartition par département */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Départements</CardTitle>
            <CardDescription>
              Répartition des employés
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Départements actifs</span>
                <Badge variant="secondary">{stats.departmentCount}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Moyenne par département</span>
                <span className="text-sm font-medium">
                  {stats.departmentCount > 0 
                    ? Math.round(stats.activeEmployees / stats.departmentCount)
                    : 0} employés
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Heures moyennes/jour</span>
                <span className="text-sm font-medium">
                  {stats.averageWorkingHours.toFixed(1)}h
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Activités récentes */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Activités Récentes</CardTitle>
            <CardDescription>
              Dernières actions du système
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">
                      {activity.message}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleTimeString('fr-FR')}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Actions rapides */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Actions Rapides</CardTitle>
            <CardDescription>
              Raccourcis vers les fonctions principales
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start" size="sm">
                <Users className="h-4 w-4 mr-2" />
                Ajouter un employé
              </Button>
              <Button variant="outline" className="w-full justify-start" size="sm">
                <Clock className="h-4 w-4 mr-2" />
                Pointage rapide
              </Button>
              <Button variant="outline" className="w-full justify-start" size="sm">
                <Calculator className="h-4 w-4 mr-2" />
                Calculer paie
              </Button>
              <Button variant="outline" className="w-full justify-start" size="sm">
                <FileText className="h-4 w-4 mr-2" />
                Feuille d'émargement
              </Button>
              <Button variant="outline" className="w-full justify-start" size="sm">
                <BarChart3 className="h-4 w-4 mr-2" />
                Rapports mensuels
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alertes et notifications */}
      {stats.pendingOvertimeApprovals > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2 text-orange-800">
              <AlertCircle className="h-5 w-5" />
              Attention Requise
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-orange-700">
              Vous avez {stats.pendingOvertimeApprovals} demande(s) d'heures supplémentaires 
              en attente d'approbation. Consultez l'onglet "Pointage & Heures" pour les traiter.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
