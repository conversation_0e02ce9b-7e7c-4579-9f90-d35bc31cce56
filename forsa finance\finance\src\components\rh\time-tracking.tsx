'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Calendar, Clock, Plus, Edit, Trash2, Download, Users, Timer, FileText } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { localDatabase } from '@/lib/database';
import { AttendanceSheet } from './attendance-sheet';

type Employee = {
  id: string;
  name: string;
  position: string;
  department: string;
};

type TimeEntry = {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  checkIn: string;
  checkOut: string;
  breakTime: number; // en minutes
  overtimeHours: number;
  regularHours: number;
  totalHours: number;
  status: 'present' | 'absent' | 'late' | 'half-day';
  notes: string;
};

type OvertimeEntry = {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  hours: number;
  reason: string;
  approved: boolean;
  approvedBy?: string;
  approvedAt?: string;
};

export function TimeTracking() {
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [timeEntries, setTimeEntries] = useState<TimeEntry[]>([]);
  const [overtimeEntries, setOvertimeEntries] = useState<OvertimeEntry[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const { toast } = useToast();

  // Charger les employés
  useEffect(() => {
    loadEmployees();
    loadTimeEntries();
    loadOvertimeEntries();
  }, []);

  const loadEmployees = async () => {
    try {
      const employeesData = localDatabase.getTable('employees');
      const formattedEmployees = employeesData.map(emp => ({
        id: emp.id,
        name: `${emp.first_name} ${emp.last_name}`,
        position: emp.position,
        department: emp.department,
      }));
      setEmployees(formattedEmployees);
    } catch (error) {
      console.error('Erreur lors du chargement des employés:', error);
    }
  };

  const loadTimeEntries = () => {
    const entries = JSON.parse(localStorage.getItem('timeEntries') || '[]');
    setTimeEntries(entries);
  };

  const loadOvertimeEntries = () => {
    const entries = JSON.parse(localStorage.getItem('overtimeEntries') || '[]');
    setOvertimeEntries(entries);
  };

  const saveTimeEntries = (entries: TimeEntry[]) => {
    localStorage.setItem('timeEntries', JSON.stringify(entries));
    setTimeEntries(entries);
  };

  const saveOvertimeEntries = (entries: OvertimeEntry[]) => {
    localStorage.setItem('overtimeEntries', JSON.stringify(entries));
    setOvertimeEntries(entries);
  };

  const calculateHours = (checkIn: string, checkOut: string, breakTime: number) => {
    if (!checkIn || !checkOut) return { regularHours: 0, totalHours: 0, overtimeHours: 0 };
    
    const start = new Date(`2000-01-01T${checkIn}`);
    const end = new Date(`2000-01-01T${checkOut}`);
    const totalMinutes = (end.getTime() - start.getTime()) / (1000 * 60) - breakTime;
    const totalHours = totalMinutes / 60;
    
    const regularHours = Math.min(totalHours, 8); // 8h = journée normale
    const overtimeHours = Math.max(0, totalHours - 8);
    
    return { regularHours, totalHours, overtimeHours };
  };

  const handleTimeEntry = (formData: FormData) => {
    const employeeId = formData.get('employeeId') as string;
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const checkIn = formData.get('checkIn') as string;
    const checkOut = formData.get('checkOut') as string;
    const breakTime = parseInt(formData.get('breakTime') as string) || 0;
    const status = formData.get('status') as 'present' | 'absent' | 'late' | 'half-day';
    const notes = formData.get('notes') as string;

    const { regularHours, totalHours, overtimeHours } = calculateHours(checkIn, checkOut, breakTime);

    const newEntry: TimeEntry = {
      id: `time-${Date.now()}`,
      employeeId,
      employeeName: employee.name,
      date: selectedDate,
      checkIn,
      checkOut,
      breakTime,
      regularHours,
      totalHours,
      overtimeHours,
      status,
      notes,
    };

    const updatedEntries = [...timeEntries, newEntry];
    saveTimeEntries(updatedEntries);

    // Si heures supplémentaires, créer une entrée automatiquement
    if (overtimeHours > 0) {
      const overtimeEntry: OvertimeEntry = {
        id: `overtime-${Date.now()}`,
        employeeId,
        employeeName: employee.name,
        date: selectedDate,
        hours: overtimeHours,
        reason: 'Heures supplémentaires automatiques',
        approved: false,
      };
      const updatedOvertimeEntries = [...overtimeEntries, overtimeEntry];
      saveOvertimeEntries(updatedOvertimeEntries);
    }

    toast({
      title: "Pointage enregistré",
      description: `Pointage de ${employee.name} enregistré avec succès.`,
    });
  };

  const handleOvertimeEntry = (formData: FormData) => {
    const employeeId = formData.get('overtimeEmployeeId') as string;
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const overtimeEntry: OvertimeEntry = {
      id: `overtime-${Date.now()}`,
      employeeId,
      employeeName: employee.name,
      date: formData.get('overtimeDate') as string,
      hours: parseFloat(formData.get('overtimeHours') as string),
      reason: formData.get('overtimeReason') as string,
      approved: false,
    };

    const updatedEntries = [...overtimeEntries, overtimeEntry];
    saveOvertimeEntries(updatedEntries);

    toast({
      title: "Heures supplémentaires ajoutées",
      description: `Heures supplémentaires de ${employee.name} enregistrées.`,
    });
  };

  const approveOvertime = (id: string) => {
    const updatedEntries = overtimeEntries.map(entry =>
      entry.id === id
        ? { ...entry, approved: true, approvedBy: 'Admin', approvedAt: new Date().toISOString() }
        : entry
    );
    saveOvertimeEntries(updatedEntries);

    toast({
      title: "Heures supplémentaires approuvées",
      description: "Les heures supplémentaires ont été approuvées.",
    });
  };

  const deleteTimeEntry = (id: string) => {
    const updatedEntries = timeEntries.filter(entry => entry.id !== id);
    saveTimeEntries(updatedEntries);
    toast({
      title: "Pointage supprimé",
      description: "L'entrée de pointage a été supprimée.",
    });
  };

  const deleteOvertimeEntry = (id: string) => {
    const updatedEntries = overtimeEntries.filter(entry => entry.id !== id);
    saveOvertimeEntries(updatedEntries);
    toast({
      title: "Heures supplémentaires supprimées",
      description: "L'entrée d'heures supplémentaires a été supprimée.",
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      present: 'default',
      absent: 'destructive',
      late: 'secondary',
      'half-day': 'outline',
    } as const;
    
    const labels = {
      present: 'Présent',
      absent: 'Absent',
      late: 'Retard',
      'half-day': 'Demi-journée',
    };

    return (
      <Badge variant={variants[status as keyof typeof variants]}>
        {labels[status as keyof typeof labels]}
      </Badge>
    );
  };

  const todayEntries = timeEntries.filter(entry => entry.date === selectedDate);
  const pendingOvertime = overtimeEntries.filter(entry => !entry.approved);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Pointage et Heures</h2>
          <p className="text-muted-foreground">
            Gestion du temps de travail et des heures supplémentaires
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <Input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="w-auto"
          />
        </div>
      </div>

      <Tabs defaultValue="pointage" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pointage" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Pointage Quotidien
          </TabsTrigger>
          <TabsTrigger value="heures-sup" className="flex items-center gap-2">
            <Timer className="h-4 w-4" />
            Heures Supplémentaires
          </TabsTrigger>
          <TabsTrigger value="rapports" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Rapports
          </TabsTrigger>
          <TabsTrigger value="emargement" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Émargement
          </TabsTrigger>
        </TabsList>

        <TabsContent value="pointage" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Formulaire de pointage */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Nouveau Pointage
                </CardTitle>
                <CardDescription>
                  Enregistrer l'arrivée et le départ d'un employé
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={(e) => {
                  e.preventDefault();
                  handleTimeEntry(new FormData(e.currentTarget));
                  e.currentTarget.reset();
                }} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="employeeId">Employé</Label>
                    <Select name="employeeId" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un employé" />
                      </SelectTrigger>
                      <SelectContent>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            {employee.name} - {employee.position}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="checkIn">Heure d'arrivée</Label>
                      <Input
                        id="checkIn"
                        name="checkIn"
                        type="time"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="checkOut">Heure de départ</Label>
                      <Input
                        id="checkOut"
                        name="checkOut"
                        type="time"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="breakTime">Pause (minutes)</Label>
                      <Input
                        id="breakTime"
                        name="breakTime"
                        type="number"
                        defaultValue="60"
                        min="0"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="status">Statut</Label>
                      <Select name="status" defaultValue="present">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="present">Présent</SelectItem>
                          <SelectItem value="late">Retard</SelectItem>
                          <SelectItem value="half-day">Demi-journée</SelectItem>
                          <SelectItem value="absent">Absent</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes (optionnel)</Label>
                    <Input
                      id="notes"
                      name="notes"
                      placeholder="Remarques particulières..."
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Enregistrer le pointage
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Résumé du jour */}
            <Card>
              <CardHeader>
                <CardTitle>Résumé du {new Date(selectedDate).toLocaleDateString('fr-FR')}</CardTitle>
                <CardDescription>
                  Pointages enregistrés aujourd'hui
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Total employés</p>
                      <p className="text-2xl font-bold">{employees.length}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Pointages du jour</p>
                      <p className="text-2xl font-bold">{todayEntries.length}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Heures supplémentaires</p>
                      <p className="text-2xl font-bold">
                        {todayEntries.reduce((sum, entry) => sum + entry.overtimeHours, 0).toFixed(1)}h
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">En attente</p>
                      <p className="text-2xl font-bold">{pendingOvertime.length}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Liste des pointages du jour */}
          <Card>
            <CardHeader>
              <CardTitle>Pointages du {new Date(selectedDate).toLocaleDateString('fr-FR')}</CardTitle>
            </CardHeader>
            <CardContent>
              {todayEntries.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Aucun pointage enregistré pour cette date.
                </p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employé</TableHead>
                      <TableHead>Arrivée</TableHead>
                      <TableHead>Départ</TableHead>
                      <TableHead>Pause</TableHead>
                      <TableHead>Total</TableHead>
                      <TableHead>H. Sup.</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {todayEntries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">{entry.employeeName}</TableCell>
                        <TableCell>{entry.checkIn}</TableCell>
                        <TableCell>{entry.checkOut}</TableCell>
                        <TableCell>{entry.breakTime}min</TableCell>
                        <TableCell>{entry.totalHours.toFixed(1)}h</TableCell>
                        <TableCell>{entry.overtimeHours.toFixed(1)}h</TableCell>
                        <TableCell>{getStatusBadge(entry.status)}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteTimeEntry(entry.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="heures-sup" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Formulaire heures supplémentaires */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Timer className="h-4 w-4" />
                  Ajouter Heures Supplémentaires
                </CardTitle>
                <CardDescription>
                  Enregistrer des heures supplémentaires manuellement
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={(e) => {
                  e.preventDefault();
                  handleOvertimeEntry(new FormData(e.currentTarget));
                  e.currentTarget.reset();
                }} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="overtimeEmployeeId">Employé</Label>
                    <Select name="overtimeEmployeeId" required>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner un employé" />
                      </SelectTrigger>
                      <SelectContent>
                        {employees.map((employee) => (
                          <SelectItem key={employee.id} value={employee.id}>
                            {employee.name} - {employee.position}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="overtimeDate">Date</Label>
                      <Input
                        id="overtimeDate"
                        name="overtimeDate"
                        type="date"
                        defaultValue={selectedDate}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="overtimeHours">Heures</Label>
                      <Input
                        id="overtimeHours"
                        name="overtimeHours"
                        type="number"
                        step="0.5"
                        min="0.5"
                        max="12"
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="overtimeReason">Motif</Label>
                    <Input
                      id="overtimeReason"
                      name="overtimeReason"
                      placeholder="Raison des heures supplémentaires..."
                      required
                    />
                  </div>

                  <Button type="submit" className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Ajouter les heures
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Statistiques heures supplémentaires */}
            <Card>
              <CardHeader>
                <CardTitle>Statistiques Heures Supplémentaires</CardTitle>
                <CardDescription>
                  Résumé mensuel des heures supplémentaires
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Total ce mois</p>
                      <p className="text-2xl font-bold">
                        {overtimeEntries
                          .filter(entry => entry.date.startsWith(selectedDate.substring(0, 7)))
                          .reduce((sum, entry) => sum + entry.hours, 0)
                          .toFixed(1)}h
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">En attente</p>
                      <p className="text-2xl font-bold text-orange-600">{pendingOvertime.length}</p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Approuvées</p>
                      <p className="text-2xl font-bold text-green-600">
                        {overtimeEntries.filter(entry => entry.approved).length}
                      </p>
                    </div>
                    <div className="space-y-1">
                      <p className="text-muted-foreground">Coût estimé</p>
                      <p className="text-2xl font-bold">
                        {(overtimeEntries
                          .filter(entry => entry.approved)
                          .reduce((sum, entry) => sum + entry.hours, 0) * 1500
                        ).toLocaleString('fr-DZ')} DZD
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Liste des heures supplémentaires */}
          <Card>
            <CardHeader>
              <CardTitle>Heures Supplémentaires</CardTitle>
            </CardHeader>
            <CardContent>
              {overtimeEntries.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  Aucune heure supplémentaire enregistrée.
                </p>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Employé</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Heures</TableHead>
                      <TableHead>Motif</TableHead>
                      <TableHead>Statut</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {overtimeEntries.map((entry) => (
                      <TableRow key={entry.id}>
                        <TableCell className="font-medium">{entry.employeeName}</TableCell>
                        <TableCell>{new Date(entry.date).toLocaleDateString('fr-FR')}</TableCell>
                        <TableCell>{entry.hours}h</TableCell>
                        <TableCell>{entry.reason}</TableCell>
                        <TableCell>
                          <Badge variant={entry.approved ? 'default' : 'secondary'}>
                            {entry.approved ? 'Approuvé' : 'En attente'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            {!entry.approved && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => approveOvertime(entry.id)}
                              >
                                Approuver
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteOvertimeEntry(entry.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="rapports" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            {/* Rapport par employé */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Rapport par Employé</CardTitle>
                <CardDescription>
                  Heures travaillées ce mois
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {employees.map((employee) => {
                    const employeeEntries = timeEntries.filter(
                      entry => entry.employeeId === employee.id &&
                      entry.date.startsWith(selectedDate.substring(0, 7))
                    );
                    const totalHours = employeeEntries.reduce((sum, entry) => sum + entry.totalHours, 0);
                    const overtimeHours = employeeEntries.reduce((sum, entry) => sum + entry.overtimeHours, 0);

                    return (
                      <div key={employee.id} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <p className="font-medium text-sm">{employee.name}</p>
                          <p className="text-xs text-muted-foreground">{employee.position}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{totalHours.toFixed(1)}h</p>
                          <p className="text-xs text-orange-600">+{overtimeHours.toFixed(1)}h sup.</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Rapport par département */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Par Département</CardTitle>
                <CardDescription>
                  Répartition des heures
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Array.from(new Set(employees.map(emp => emp.department))).map((department, index) => {
                    const deptEmployees = employees.filter(emp => emp.department === department);
                    const deptEntries = timeEntries.filter(
                      entry => deptEmployees.some(emp => emp.id === entry.employeeId) &&
                      entry.date.startsWith(selectedDate.substring(0, 7))
                    );
                    const totalHours = deptEntries.reduce((sum, entry) => sum + entry.totalHours, 0);

                    return (
                      <div key={`dept-${department}-${index}`} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <p className="font-medium text-sm">{department}</p>
                          <p className="text-xs text-muted-foreground">{deptEmployees.length} employés</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">{totalHours.toFixed(1)}h</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Actions rapides */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Actions Rapides</CardTitle>
                <CardDescription>
                  Exports et rapports
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Exporter pointages
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Download className="h-4 w-4 mr-2" />
                    Rapport mensuel
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="h-4 w-4 mr-2" />
                    Feuille de présence
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Timer className="h-4 w-4 mr-2" />
                    Heures supplémentaires
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="emargement" className="space-y-4">
          <AttendanceSheet />
        </TabsContent>
      </Tabs>
    </div>
  );
}
