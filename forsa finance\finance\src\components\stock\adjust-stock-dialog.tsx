
'use client';

import { useState, useMemo, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { ArrowUpCircle, ArrowDownCircle } from 'lucide-react';
import type { Product } from '@/data/products';

type AdjustStockDialogProps = {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    product: Product;
    onAdjustStock: (productId: number, adjustmentInBaseUnits: number) => void;
};

type Unit = 'unite' | 'carton' | 'palette';

export function AdjustStockDialog({ open, onOpenChange, product, onAdjustStock }: AdjustStockDialogProps) {
    const [quantity, setQuantity] = useState<number>(1);
    const [unit, setUnit] = useState<Unit>('unite');
    const [movementType, setMovementType] = useState<'in' | 'out'>('in');
    
    // Reset state when dialog opens for a new product
    useEffect(() => {
        if (open) {
            setQuantity(1);
            setUnit('unite');
            setMovementType('in');
        }
    }, [open]);

    const conversionFactors = {
        unite: 1,
        carton: product.unitsPerCarton,
        palette: product.unitsPerCarton * product.cartonsPerPalette,
    };

    const equivalentInBaseUnits = useMemo(() => {
        return quantity * (conversionFactors[unit] || 0);
    }, [quantity, unit, conversionFactors]);
    
    const formattedConversion = useMemo(() => {
        if (!equivalentInBaseUnits) return "0 Unités";
        
        const { unitsPerCarton, cartonsPerPalette } = product;
        const unitsPerPalette = cartonsPerPalette * unitsPerCarton;
        
        let remainingUnits = equivalentInBaseUnits;
        
        const palettes = Math.floor(remainingUnits / unitsPerPalette);
        remainingUnits %= unitsPerPalette;
        
        const cartons = Math.floor(remainingUnits / unitsPerCarton);
        remainingUnits %= unitsPerCarton;
        
        const units = remainingUnits;

        const parts = [];
        if (palettes > 0) parts.push(`${palettes} Palette(s)`);
        if (cartons > 0) parts.push(`${cartons} Carton(s)`);
        if (units > 0) parts.push(`${units} Unité(s)`);

        if (parts.length === 0) return "0 Unités";
        if (parts.length > 1) return `Équivaut à ${parts.join(', ')}`;
        return `${equivalentInBaseUnits} Unité(s)`;

    }, [equivalentInBaseUnits, product]);

    const handleSubmit = () => {
        const adjustment = movementType === 'in' ? equivalentInBaseUnits : -equivalentInBaseUnits;
        if (movementType === 'out' && equivalentInBaseUnits > product.quantity) {
            // Basic validation to prevent negative stock
            alert("La quantité à sortir est supérieure au stock disponible.");
            return;
        }
        onAdjustStock(product.id, adjustment);
        onOpenChange(false);
    };

    return (
        <Dialog open={open} onOpenChange={onOpenChange}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>Ajuster le Stock</DialogTitle>
                    <DialogDescription>
                        Ajuster la quantité en stock pour le produit : <span className="font-semibold">{product.name}</span>
                    </DialogDescription>
                </DialogHeader>
                <div className="py-4 space-y-6">
                    <div className="space-y-2">
                        <Label>Type de Mouvement</Label>
                        <ToggleGroup type="single" value={movementType} onValueChange={(value: 'in' | 'out') => value && setMovementType(value)} className="w-full">
                            <ToggleGroupItem value="in" className="w-full data-[state=on]:bg-green-100 data-[state=on]:text-green-800">
                                <ArrowUpCircle className="mr-2 h-4 w-4" />
                                Entrée
                            </ToggleGroupItem>
                            <ToggleGroupItem value="out" className="w-full data-[state=on]:bg-red-100 data-[state=on]:text-red-800">
                                <ArrowDownCircle className="mr-2 h-4 w-4" />
                                Sortie
                            </ToggleGroupItem>
                        </ToggleGroup>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="quantity">Quantité</Label>
                            <Input
                                id="quantity"
                                type="number"
                                value={quantity}
                                onChange={(e) => setQuantity(Math.max(0, parseInt(e.target.value, 10) || 0))}
                                min="0"
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="unit">Unité</Label>
                            <Select value={unit} onValueChange={(value: Unit) => setUnit(value)}>
                                <SelectTrigger id="unit">
                                    <SelectValue placeholder="Choisir une unité" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="unite">Unité</SelectItem>
                                    <SelectItem value="carton">Carton ({product.unitsPerCarton} u.)</SelectItem>
                                    <SelectItem value="palette">Palette ({product.cartonsPerPalette} c.)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                    
                    <div className="text-center text-sm text-muted-foreground bg-muted p-3 rounded-md">
                        <p className="font-semibold">{formattedConversion}</p>
                    </div>

                    <div className="text-center text-sm">
                        <p>Stock Actuel : <span className="font-bold">{product.quantity}</span> unités</p>
                        <p>Stock Après Mouvement : <span className="font-bold">{product.quantity + (movementType === 'in' ? equivalentInBaseUnits : -equivalentInBaseUnits)}</span> unités</p>
                    </div>
                </div>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button type="button" variant="secondary">Annuler</Button>
                    </DialogClose>
                    <Button onClick={handleSubmit} disabled={quantity <= 0}>Enregistrer le Mouvement</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
