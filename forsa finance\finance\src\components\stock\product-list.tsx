
'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { MoreHorizontal, PlusCircle, Pencil, PackagePlus } from "lucide-react";
import { products as initialProducts, type Product } from '@/data/products';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { AdjustStockDialog } from './adjust-stock-dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const getStockVariant = (quantity: number): 'destructive' | 'secondary' | 'default' => {
  if (quantity === 0) return 'destructive';
  if (quantity < 10) return 'secondary';
  return 'default';
};

const formatStockDisplay = (totalUnits: number, unitsPerCarton: number, cartonsPerPalette: number) => {
    if (totalUnits === 0) return "0 Palettes, 0 Cartons, 0 Unités";
    
    const unitsPerPalette = cartonsPerPalette * unitsPerCarton;
    
    let remainingUnits = totalUnits;
    
    const palettes = Math.floor(remainingUnits / unitsPerPalette);
    remainingUnits %= unitsPerPalette;
    
    const cartons = Math.floor(remainingUnits / unitsPerCarton);
    remainingUnits %= unitsPerCarton;
    
    const units = remainingUnits;

    const parts = [];
    if (palettes > 0) parts.push(`${palettes} Palette(s)`);
    if (cartons > 0) parts.push(`${cartons} Carton(s)`);
    if (units > 0) parts.push(`${units} Unité(s)`);

    return parts.join(', ');
};

export function ProductList() {
    const [products, setProducts] = useState(initialProducts);
    const [isAdjustDialogOpen, setIsAdjustDialogOpen] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

    const handleOpenAdjustDialog = (product: Product) => {
        setSelectedProduct(product);
        setIsAdjustDialogOpen(true);
    };
    
    const handleAdjustStock = (productId: number, adjustmentInBaseUnits: number) => {
        setProducts(prevProducts => 
            prevProducts.map(p => 
                p.id === productId 
                    ? { ...p, quantity: p.quantity + adjustmentInBaseUnits }
                    : p
            )
        );
    };

    return (
        <TooltipProvider>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                    <div>
                        <CardTitle>Inventaire des Produits</CardTitle>
                        <CardDescription>Liste complète des articles en stock.</CardDescription>
                    </div>
                    <Button>
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Ajouter un produit
                    </Button>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Produit</TableHead>
                                <TableHead>Catégorie</TableHead>
                                <TableHead className="text-right">Prix U. HT</TableHead>
                                <TableHead className="text-right">Stock (en unités)</TableHead>
                                <TableHead className="text-right">Valeur totale</TableHead>
                                <TableHead><span className="sr-only">Actions</span></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {products.map((product) => (
                                <TableRow key={product.id}>
                                    <TableCell className="font-medium">
                                        <div className="flex items-center gap-3">
                                            <Avatar className="h-10 w-10 rounded-md">
                                                <AvatarImage src={product.imageUrl} alt={product.name} data-ai-hint="product photo" className="object-cover" />
                                                <AvatarFallback>{product.name.substring(0, 2)}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                                {product.name}
                                                <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
                                            </div>
                                        </div>
                                    </TableCell>
                                    <TableCell>{product.category}</TableCell>
                                    <TableCell className="text-right font-mono">{formatCurrency(product.price)} DZD</TableCell>
                                    <TableCell className="text-right">
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Badge variant={getStockVariant(product.quantity)} className="cursor-help">
                                                    {product.quantity} unités
                                                </Badge>
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <p>{formatStockDisplay(product.quantity, product.unitsPerCarton, product.cartonsPerPalette)}</p>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TableCell>
                                    <TableCell className="text-right font-mono font-semibold">{formatCurrency(product.price * product.quantity)} DZD</TableCell>
                                    <TableCell className="text-right">
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <span className="sr-only">Ouvrir le menu</span>
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuItem onClick={() => handleOpenAdjustDialog(product)}>
                                                    <PackagePlus className="mr-2 h-4 w-4" />
                                                    Ajuster le stock
                                                </DropdownMenuItem>
                                                <DropdownMenuItem>
                                                    <Pencil className="mr-2 h-4 w-4" />
                                                    Modifier le produit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem className="text-destructive">Supprimer</DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
             {selectedProduct && (
                <AdjustStockDialog
                    open={isAdjustDialogOpen}
                    onOpenChange={setIsAdjustDialogOpen}
                    product={selectedProduct}
                    onAdjustStock={handleAdjustStock}
                />
            )}
        </TooltipProvider>
    );
}
