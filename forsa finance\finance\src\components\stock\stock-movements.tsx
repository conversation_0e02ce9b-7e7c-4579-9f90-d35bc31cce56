
'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Filter, ArrowUpCircle, ArrowDownCircle } from "lucide-react";

const movements = [
  { id: 1, date: '2023-10-28', product: "Souris Ergonomique", type: "Vente", quantity: -2, reference: "FACT-2023-0001" },
  { id: 2, date: '2023-10-27', product: "Clavier Mécanique RGB", type: "Achat Fournisseur", quantity: 10, reference: "CMD-2023-10-15" },
  { id: 3, date: '2023-10-26', product: "Pack de 5 Ramettes Papier A4", type: "Ajustement (+)", quantity: 5, reference: "AJUST-MANUEL" },
  { id: 4, date: '2023-10-25', product: "Licence Logiciel de Design", type: "Vente", quantity: -1, reference: "FACT-2023-0002" },
  { id: 5, date: '2023-10-24', product: "Souris Ergonomique", type: "Retour Client", quantity: 1, reference: "AVOIR-2023-001" },
];

const getTypeVariant = (type: string): 'default' | 'secondary' | 'destructive' | 'outline' => {
    if (type.includes('Vente')) return 'destructive';
    if (type.includes('Achat')) return 'default';
    if (type.includes('Retour')) return 'secondary';
    return 'outline';
};

const getTypeIcon = (quantity: number) => {
    return quantity > 0 ? <ArrowUpCircle className="h-4 w-4 text-green-500" /> : <ArrowDownCircle className="h-4 w-4 text-red-500" />;
}

export function StockMovements() {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Mouvements de Stock</CardTitle>
          <CardDescription>Historique de toutes les entrées et sorties de stock.</CardDescription>
        </div>
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filtrer
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Produit</TableHead>
              <TableHead>Type</TableHead>
              <TableHead className="text-right">Quantité</TableHead>
              <TableHead>Référence</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {movements.map((movement) => (
              <TableRow key={movement.id}>
                <TableCell>{movement.date}</TableCell>
                <TableCell className="font-medium">{movement.product}</TableCell>
                <TableCell>
                    <Badge variant={getTypeVariant(movement.type)}>{movement.type}</Badge>
                </TableCell>
                <TableCell className="text-right font-mono flex items-center justify-end gap-2">
                    {getTypeIcon(movement.quantity)}
                    {movement.quantity}
                </TableCell>
                <TableCell>{movement.reference}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
