
'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, FileJson } from "lucide-react";

const reports = [
    { 
        name: "Rapport de Valorisation du Stock", 
        description: "Obtenez la valeur comptable de votre stock à une date donnée." 
    },
    { 
        name: "Analyse des Ventes par Produit", 
        description: "Identifiez vos produits les plus performants et les moins populaires." 
    },
    { 
        name: "Rapport sur le Faible Niveau de Stock", 
        description: "Listez tous les articles qui nécessitent un réapprovisionnement." 
    },
    {
        name: "Historique des Mouvements par Article",
        description: "Suivez la traçabilité complète d'un produit spécifique."
    }
];

export function StockReports() {
  return (
    <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {reports.map((report) => (
             <Card key={report.name}>
                <CardHeader>
                    <BarChart className="h-8 w-8 text-muted-foreground mb-2" />
                    <CardTitle>{report.name}</CardTitle>
                </CardHeader>
                <CardContent>
                    <p className="text-muted-foreground">{report.description}</p>
                </CardContent>
                <CardContent>
                    <Button>
                        <FileJson className="mr-2 h-4 w-4" />
                        Générer le rapport
                    </Button>
                </CardContent>
            </Card>
        ))}
    </div>
  );
}
