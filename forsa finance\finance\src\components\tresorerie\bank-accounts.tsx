'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Search, Edit, Trash2, Building2, CreditCard } from "lucide-react";
import { useToast } from '@/hooks/use-toast';

// Banques algériennes principales
const algerianBanks = [
  'Banque Nationale d\'Algérie (BNA)',
  'Crédit Populaire d\'Algérie (CPA)',
  'Banque Extérieure d\'Algérie (BEA)',
  'Banque de l\'Agriculture et du Développement Rural (BADR)',
  'Banque de Développement Local (BDL)',
  'Société Générale Algérie',
  'BNP Paribas El Djazair',
  'Natixis Algérie',
  'Trust Bank Algeria',
  'Al Baraka Bank Algeria',
  'Al Salam Bank Algeria',
  'Housing Bank for Trade and Finance Algeria',
  'Arab Banking Corporation Algeria',
  'Gulf Bank Algeria',
  'Fransabank El Djazair',
  'Autre',
];

const defaultBankAccounts = [
  {
    id: '1',
    name: 'Compte Principal BNA',
    bankName: 'Banque Nationale d\'Algérie (BNA)',
    accountNumber: '***************',
    rib: '001 234 ********* 45',
    currency: 'DZD',
    balance: 2500000,
    isActive: true,
  },
  {
    id: '2',
    name: 'Compte Épargne CPA',
    bankName: 'Crédit Populaire d\'Algérie (CPA)',
    accountNumber: '00234*********4',
    rib: '002 345 ********* 56',
    currency: 'DZD',
    balance: 850000,
    isActive: true,
  },
  {
    id: '3',
    name: 'Compte Devises BEA',
    bankName: 'Banque Extérieure d\'Algérie (BEA)',
    accountNumber: '0034*********45',
    rib: '003 456 ********* 67',
    currency: 'EUR',
    balance: 15000,
    isActive: true,
  },
];

const bankAccountSchema = z.object({
  name: z.string().min(1, 'Le nom du compte est requis').max(100, 'Le nom ne peut pas dépasser 100 caractères'),
  bankName: z.string().min(1, 'Le nom de la banque est requis'),
  accountNumber: z.string().min(10, 'Le numéro de compte doit contenir au moins 10 caractères').max(30, 'Le numéro de compte ne peut pas dépasser 30 caractères'),
  rib: z.string().min(20, 'Le RIB doit contenir au moins 20 caractères').max(30, 'Le RIB ne peut pas dépasser 30 caractères'),
  currency: z.enum(['DZD', 'EUR', 'USD'], {
    required_error: 'La devise est requise',
  }),
  balance: z.coerce.number().min(0, 'Le solde ne peut pas être négatif'),
});

type BankAccountFormValues = z.infer<typeof bankAccountSchema>;

const formatCurrency = (amount: number, currency: string) => {
  const formatter = new Intl.NumberFormat('fr-DZ', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return `${formatter.format(amount)} ${currency}`;
};

const getCurrencyColor = (currency: string) => {
  switch (currency) {
    case 'DZD':
      return 'bg-green-100 text-green-800';
    case 'EUR':
      return 'bg-blue-100 text-blue-800';
    case 'USD':
      return 'bg-purple-100 text-purple-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export function BankAccounts() {
  const [bankAccounts, setBankAccounts] = useState(defaultBankAccounts);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<any>(null);
  const { toast } = useToast();

  const form = useForm<BankAccountFormValues>({
    resolver: zodResolver(bankAccountSchema),
    defaultValues: {
      name: '',
      bankName: '',
      accountNumber: '',
      rib: '',
      currency: 'DZD',
      balance: 0,
    },
  });

  const filteredAccounts = bankAccounts.filter(account =>
    account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.bankName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    account.accountNumber.includes(searchTerm) ||
    account.rib.includes(searchTerm)
  );

  const totalBalance = bankAccounts.reduce((total, account) => {
    if (account.currency === 'DZD') {
      return total + account.balance;
    }
    return total;
  }, 0);

  const onSubmit = (values: BankAccountFormValues) => {
    try {
      if (editingAccount) {
        // Modifier un compte existant
        setBankAccounts(prev => prev.map(account =>
          account.id === editingAccount.id
            ? { ...account, ...values, isActive: true }
            : account
        ));
        toast({
          title: "Compte modifié",
          description: `Le compte ${values.name} a été modifié avec succès.`,
        });
      } else {
        // Ajouter un nouveau compte
        const newAccount = {
          id: Date.now().toString(),
          ...values,
          isActive: true,
        };
        setBankAccounts(prev => [...prev, newAccount]);
        toast({
          title: "Compte ajouté",
          description: `Le compte ${values.name} a été ajouté avec succès.`,
        });
      }

      setIsDialogOpen(false);
      setEditingAccount(null);
      form.reset();
    } catch (error) {
      toast({
        title: "Erreur",
        description: "Une erreur est survenue lors de l'enregistrement du compte.",
        variant: "destructive",
      });
    }
  };

  const handleEdit = (account: any) => {
    setEditingAccount(account);
    form.reset({
      name: account.name,
      bankName: account.bankName,
      accountNumber: account.accountNumber,
      rib: account.rib,
      currency: account.currency,
      balance: account.balance,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (accountId: string) => {
    setBankAccounts(prev => prev.filter(account => account.id !== accountId));
    toast({
      title: "Compte supprimé",
      description: "Le compte bancaire a été supprimé avec succès.",
    });
  };

  const toggleAccountStatus = (accountId: string) => {
    setBankAccounts(prev => prev.map(account =>
      account.id === accountId
        ? { ...account, isActive: !account.isActive }
        : account
    ));
  };

  const openNewAccountDialog = () => {
    setEditingAccount(null);
    form.reset();
    setIsDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Résumé des comptes */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Comptes</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{bankAccounts.length}</div>
            <p className="text-xs text-muted-foreground">
              {bankAccounts.filter(acc => acc.isActive).length} actifs
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Solde Total (DZD)</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalBalance, 'DZD')}</div>
            <p className="text-xs text-muted-foreground">
              Comptes en dinars algériens
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Devises</CardTitle>
            <Badge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(bankAccounts.map(acc => acc.currency)).size}
            </div>
            <p className="text-xs text-muted-foreground">
              Devises différentes
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Liste des comptes */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Comptes Bancaires</CardTitle>
            <CardDescription>
              Gestion des comptes bancaires de l'entreprise.
            </CardDescription>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openNewAccountDialog}>
                <Plus className="mr-2 h-4 w-4" />
                Nouveau compte
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingAccount ? 'Modifier le compte' : 'Nouveau compte bancaire'}
                </DialogTitle>
                <DialogDescription>
                  {editingAccount 
                    ? 'Modifiez les informations du compte bancaire.'
                    : 'Ajoutez un nouveau compte bancaire.'
                  }
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom du compte</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Compte Principal BNA" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Banque</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Sélectionner la banque" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {algerianBanks.map((bank) => (
                              <SelectItem key={bank} value={bank}>
                                {bank}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="accountNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Numéro de compte</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: ***************" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="rib"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>RIB</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: 001 234 ********* 45" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="currency"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Devise</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Devise" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="DZD">DZD</SelectItem>
                              <SelectItem value="EUR">EUR</SelectItem>
                              <SelectItem value="USD">USD</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="balance"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Solde initial</FormLabel>
                          <FormControl>
                            <Input type="number" step="0.01" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <DialogFooter>
                    <Button type="submit">
                      {editingAccount ? 'Modifier' : 'Ajouter'}
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un compte..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nom du compte</TableHead>
                <TableHead>Banque</TableHead>
                <TableHead>Numéro de compte</TableHead>
                <TableHead>RIB</TableHead>
                <TableHead>Devise</TableHead>
                <TableHead className="text-right">Solde</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAccounts.map((account) => (
                <TableRow key={account.id}>
                  <TableCell className="font-medium">{account.name}</TableCell>
                  <TableCell className="text-sm">{account.bankName}</TableCell>
                  <TableCell className="font-mono text-sm">{account.accountNumber}</TableCell>
                  <TableCell className="font-mono text-sm">{account.rib}</TableCell>
                  <TableCell>
                    <Badge className={getCurrencyColor(account.currency)}>
                      {account.currency}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(account.balance, account.currency)}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleAccountStatus(account.id)}
                    >
                      <Badge variant={account.isActive ? 'default' : 'secondary'}>
                        {account.isActive ? 'Actif' : 'Inactif'}
                      </Badge>
                    </Button>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(account)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(account.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
