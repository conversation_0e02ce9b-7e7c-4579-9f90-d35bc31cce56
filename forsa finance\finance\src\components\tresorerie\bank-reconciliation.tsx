'use client';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, CheckCircle2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";

const bankTransactions = [
    { id: 'BANK01', date: '2023-10-25', label: 'VIREMENT RECU INNOVASARL', amount: 120000 },
    { id: 'BANK02', date: '2023-10-26', label: 'PAIEMENT FOURNISSEUR TECHCORP', amount: -45000 },
    { id: 'BANK03', date: '2023-10-26', label: 'PAIEMENT CB/TPE FOURNITURES', amount: -7500 },
    { id: 'BANK04', date: '2023-10-27', label: 'REMB ECHEANCE PRET', amount: -80000 },
    { id: 'BANK05', date: '2023-10-28', label: 'COMMISSIONS BANCAIRES', amount: -2500 },
];

const companyTransactions = [
    { id: 'COMP01', date: '2023-10-25', label: 'Virement client Innova SARL', amount: 120000 },
    { id: 'COMP02', date: '2023-10-26', label: 'Paiement fournisseur TechCorp', amount: -45000 },
    { id: 'COMP03', date: '2023-10-26', label: 'Achat de fournitures', amount: -7500 },
    { id: 'COMP04', date: '2023-10-27', label: 'Remboursement prêt bancaire', amount: -80000 },
    { id: 'COMP05', date: '2023-10-29', label: 'Salaire Octobre', amount: -1500000 },
];

const formatCurrency = (amount: number) => {
    return amount.toLocaleString('fr-DZ', { style: 'decimal', minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

export function BankReconciliation() {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Rapprochement Bancaire</CardTitle>
                <CardDescription>Associez les transactions de votre relevé bancaire avec vos opérations comptables.</CardDescription>
            </CardHeader>
            <CardContent className="grid md:grid-cols-2 gap-8">
                <div className="space-y-2">
                    <h3 className="font-semibold">Relevé Bancaire (BNA)</h3>
                    <div className="border rounded-lg">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[40px]"></TableHead>
                                    <TableHead>Libellé</TableHead>
                                    <TableHead className="text-right">Montant</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {bankTransactions.map(t => (
                                    <TableRow key={t.id}>
                                        <TableCell><Checkbox id={`bank-${t.id}`} /></TableCell>
                                        <TableCell>
                                            <p className="font-medium">{t.label}</p>
                                            <p className="text-xs text-muted-foreground">{t.date}</p>
                                        </TableCell>
                                        <TableCell className="text-right font-mono">{formatCurrency(t.amount)}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
                <div className="space-y-2">
                    <h3 className="font-semibold">Opérations Comptables</h3>
                     <div className="border rounded-lg">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead className="w-[40px]"></TableHead>
                                    <TableHead>Libellé</TableHead>
                                    <TableHead className="text-right">Montant</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {companyTransactions.map(t => (
                                    <TableRow key={t.id}>
                                        <TableCell><Checkbox id={`comp-${t.id}`} /></TableCell>
                                        <TableCell>
                                            <p className="font-medium">{t.label}</p>
                                            <p className="text-xs text-muted-foreground">{t.date}</p>
                                        </TableCell>
                                        <TableCell className="text-right font-mono">{formatCurrency(t.amount)}</TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                </div>
            </CardContent>
            <CardFooter className="flex flex-col items-center gap-4">
                <div className="flex items-center gap-4">
                    <div>
                        <p className="font-semibold">2 transactions sélectionnées</p>
                        <p className="text-sm text-muted-foreground">Total: 75 000.00 DZD</p>
                    </div>
                    <ArrowRight className="h-5 w-5 text-muted-foreground" />
                     <div>
                        <p className="font-semibold">2 transactions sélectionnées</p>
                        <p className="text-sm text-muted-foreground">Total: 75 000.00 DZD</p>
                    </div>
                </div>
                 <Button>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Rapprocher les transactions
                </Button>
            </CardFooter>
        </Card>
    );
}
