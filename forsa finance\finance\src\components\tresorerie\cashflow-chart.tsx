'use client';

import { Line, LineChart, CartesianGrid, XAxis, YAxis, Tooltip } from 'recharts';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltipContent,
  type ChartConfig,
} from '@/components/ui/chart';

const chartData = [
  { date: '2023-10-01', cashIn: 400000, cashOut: 240000 },
  { date: '2023-10-04', cashIn: 300000, cashOut: 139800 },
  { date: '2023-10-07', cashIn: 200000, cashOut: 980000 },
  { date: '2023-10-10', cashIn: 278000, cashOut: 390800 },
  { date: '2023-10-13', cashIn: 189000, cashOut: 480000 },
  { date: '2023-10-16', cashIn: 239000, cashOut: 380000 },
  { date: '2023-10-19', cashIn: 600000, cashOut: 430000 },
  { date: '2023-10-22', cashIn: 349000, cashOut: 200000 },
  { date: '2023-10-25', cashIn: 120000, cashOut: 80000 },
  { date: '2023-10-28', cashIn: 250000, cashOut: 45000 },
];

const chartConfig = {
  cashIn: {
    label: 'Encaissements',
    color: 'hsl(var(--chart-1))',
  },
  cashOut: {
    label: 'Décaissements',
    color: 'hsl(var(--destructive))',
  },
} satisfies ChartConfig;

export function CashflowChart() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Flux de Trésorerie</CardTitle>
        <CardDescription>Évolution des encaissements et décaissements sur le mois.</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="h-72 w-full">
          <LineChart data={chartData} accessibilityLayer
            margin={{ top: 5, right: 20, left: 20, bottom: 5 }}
          >
            <CartesianGrid vertical={false} strokeDasharray="3 3"/>
            <XAxis
              dataKey="date"
              tickLine={false}
              axisLine={false}
              tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })}
            />
            <YAxis
              tickFormatter={(value) => `${Number(value) / 1000}k`}
              tickLine={false}
              axisLine={false}
            />
            <Tooltip
              cursor={true}
              content={<ChartTooltipContent
                formatter={(value, name) => `${new Intl.NumberFormat('fr-DZ').format(Number(value))} DZD`}
                indicator="dot"
              />}
            />
            <Line type="monotone" dataKey="cashIn" stroke="var(--color-cashIn)" strokeWidth={2} dot={false} />
            <Line type="monotone" dataKey="cashOut" stroke="var(--color-cashOut)" strokeWidth={2} dot={false} />
          </LineChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
