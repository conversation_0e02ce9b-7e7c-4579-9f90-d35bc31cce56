
export type Employee = {
    id: string;
    name: string;
    email: string;
    role: string;
    status: string;
    avatar: string;
    salary?: number;
    nss?: string;
    hireDate?: string;
    maritalStatus?: 'Célibataire' | '<PERSON><PERSON>(e)';
    children?: number;
    first_name?: string;
    last_name?: string;
    position?: string;
    department?: string;
    phone?: string;
    socialSecurityNumber?: string;
};

export const employees: Employee[] = [
    { 
        id: "1", 
        name: "<PERSON>", 
        email: "<EMAIL>", 
        role: "Chef de service", 
        status: "Actif", 
        avatar: "https://placehold.co/100x100", 
        salary: 120000, 
        nss: "123456789012", 
        hireDate: "01/03/2020", 
        maritalStatus: '<PERSON><PERSON>(e)', 
        children: 2,
        first_name: "<PERSON>",
        last_name: "<PERSON><PERSON><PERSON><PERSON>",
        position: "Chef de service",
        department: "Direction",
        socialSecurityNumber: "123456789012"
    },
    { 
        id: "2", 
        name: "<PERSON><PERSON>", 
        email: "<EMAIL>", 
        role: "Comptable", 
        status: "Actif", 
        avatar: "https://placehold.co/100x100", 
        salary: 95000, 
        nss: "234567890123", 
        hireDate: "15/07/2021", 
        maritalStatus: 'Célibataire', 
        children: 0,
        first_name: "Fatima",
        last_name: "Zohra",
        position: "Comptable",
        department: "Comptabilité",
        socialSecurityNumber: "234567890123"
    },
    { 
        id: "3", 
        name: "Karim Belkacem", 
        email: "<EMAIL>", 
        role: "Gestionnaire de Paie", 
        status: "Actif", 
        avatar: "https://placehold.co/100x100", 
        salary: 80000, 
        nss: "345678901234", 
        hireDate: "01/11/2022", 
        maritalStatus: 'Marié(e)', 
        children: 1,
        first_name: "Karim",
        last_name: "Belkacem",
        position: "Gestionnaire de Paie",
        department: "RH",
        socialSecurityNumber: "345678901234"
    },
    { 
        id: "4", 
        name: "Amina Hadj", 
        email: "<EMAIL>", 
        role: "Auditeur", 
        status: "En congé", 
        avatar: "https://placehold.co/100x100", 
        salary: 110000, 
        nss: "456789012345", 
        hireDate: "01/02/2019", 
        maritalStatus: 'Célibataire', 
        children: 0,
        first_name: "Amina",
        last_name: "Hadj",
        position: "Auditeur",
        department: "Audit",
        socialSecurityNumber: "456789012345"
    },
    { 
        id: "5", 
        name: "Yacine Bouzid", 
        email: "<EMAIL>", 
        role: "Développeur", 
        status: "Ancien employé", 
        avatar: "https://placehold.co/100x100", 
        salary: 100000, 
        nss: "567890123456", 
        hireDate: "10/10/2023", 
        maritalStatus: 'Marié(e)', 
        children: 3,
        first_name: "Yacine",
        last_name: "Bouzid",
        position: "Développeur",
        department: "IT",
        socialSecurityNumber: "567890123456"
    },
];
