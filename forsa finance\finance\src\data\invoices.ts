

export type InvoiceLineItem = {
    id: number;
    description: string;
    quantity: number;
    unitPriceHT: number;
    tvaRate: number; // e.g., 0.19 for 19%
};

export type Invoice = {
    id: number;
    number: string;
    clientName: string;
    clientAddress: string;
    clientNif: string;
    issueDate: string;
    dueDate: string;
    items: InvoiceLineItem[];
    totalHT: number;
    totalTVA: number;
    timbre: number;
    totalTTC: number;
    status: 'Payée' | 'En attente' | 'En retard';
};

export const invoices: Invoice[] = [
    { 
        id: 1, 
        number: "FACT-2023-0001", 
        clientName: "SARL Tech Innov", 
        clientAddress: "Cité 1200 Logements, Alger",
        clientNif: "001122334455667",
        issueDate: "2023-10-15", 
        dueDate: "2023-11-14",
        items: [
            { id: 1, description: "Développement d'une application web", quantity: 1, unitPriceHT: 120000.00, tvaRate: 0.19 },
            { id: 2, description: "Hébergement annuel", quantity: 1, unitPriceHT: 30000.00, tvaRate: 0.19 },
        ],
        totalHT: 150000.00,
        totalTVA: 28500.00,
        timbre: 100.00,
        totalTTC: 178600.00,
        status: "Payée" 
    },
    { 
        id: 2, 
        number: "FACT-2023-0002", 
        clientName: "EURL Global Export", 
        clientAddress: "Zone Industrielle, Oran",
        clientNif: "002233445566778",
        issueDate: "2023-10-20", 
        dueDate: "2023-11-19",
        items: [
            { id: 1, description: "50x Souris Ergonomique Sans-Fil", quantity: 50, unitPriceHT: 5386.55, tvaRate: 0.19 },
        ],
        totalHT: 269327.50,
        totalTVA: 51172.23,
        timbre: 100.00,
        totalTTC: 320600.00,
        status: "En attente" 
    },
    { 
        id: 3, 
        number: "FACT-2023-0003", 
        clientName: "SARL BTP Plus", 
        clientAddress: "Rue de la République, Constantine",
        clientNif: "003344556677889",
        issueDate: "2023-09-01", 
        dueDate: "2023-09-30",
        items: [
            { id: 1, description: "Consulting en ingénierie (Prestation non soumise)", quantity: 10, unitPriceHT: 8500.00, tvaRate: 0 },
        ],
        totalHT: 85000.00,
        totalTVA: 0.00,
        timbre: 100.00,
        totalTTC: 85100.00,
        status: "En retard" 
    },
];
