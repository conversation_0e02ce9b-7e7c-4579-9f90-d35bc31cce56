
export type Product = {
    id: number;
    name: string;
    sku: string;
    category: string;
    price: number; // Price per base unit (HT)
    tvaRate: number; // e.g., 0.19 for 19%
    quantity: number; // Total stock in base units
    imageUrl: string;
    unitsPerCarton: number;
    cartonsPerPalette: number;
};

export const products: Product[] = [
    { 
        id: 1, 
        name: "Souris Ergonomique Sans-Fil", 
        sku: "LOG-MX-M1",
        category: "Matériel Informatique",
        price: 12500.00, 
        tvaRate: 0.19,
        quantity: 50,
        imageUrl: "https://placehold.co/100x100.png",
        unitsPerCarton: 10,
        cartonsPerPalette: 5,
    },
    { 
        id: 2, 
        name: "Clavier Mécanique RGB", 
        sku: "CRSR-K95-RGB",
        category: "Matériel Informatique",
        price: 28000.00, 
        tvaRate: 0.19,
        quantity: 25,
        imageUrl: "https://placehold.co/100x100.png",
        unitsPerCarton: 5,
        cartonsPerPalette: 4,
    },
    { 
        id: 3, 
        name: "Licence Logiciel de Design (1 an)", 
        sku: "ADB-PHSP-1Y",
        category: "Logiciels",
        price: 65000.00, 
        tvaRate: 0.09,
        quantity: 100,
        imageUrl: "https://placehold.co/100x100.png",
        unitsPerCarton: 1, // Single unit
        cartonsPerPalette: 1,
    },
    { 
        id: 4, 
        name: "Pack de 5 Ramettes Papier A4", 
        sku: "PAP-A4-5X",
        category: "Fournitures de bureau",
        price: 3500.00, 
        tvaRate: 0.09,
        quantity: 8,
        imageUrl: "https://placehold.co/100x100.png",
        unitsPerCarton: 50, // 50 ramettes in a carton
        cartonsPerPalette: 20,
    },
];
