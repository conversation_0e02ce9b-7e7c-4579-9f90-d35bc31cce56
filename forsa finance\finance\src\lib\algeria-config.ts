// Configuration spécifique à l'Algérie pour Forsa Finance
// Conforme aux réglementations algériennes en vigueur (2024)

export const ALGERIA_CONFIG = {
  // Informations générales
  country: 'Algérie',
  countryCode: 'DZ',
  currency: 'DZD',
  currencySymbol: 'د.ج',
  locale: 'fr-DZ',
  timezone: 'Africa/Algiers',
  
  // Système comptable
  accountingStandard: 'SCF', // Système Comptable Financier
  fiscalYearStart: '01-01', // 1er janvier
  fiscalYearEnd: '12-31', // 31 décembre
  
  // TVA (Taxe sur la Valeur Ajoutée)
  vat: {
    standardRate: 0.19, // 19%
    reducedRate: 0.09, // 9%
    exemptRate: 0.00, // 0%
    registrationThreshold: ********, // 30 millions DZD
    declarationFrequency: 'monthly', // Déclaration mensuelle
    paymentDeadline: 20, // 20 du mois suivant
  },
  
  // IRG (Impôt sur le Revenu Global)
  irg: {
    brackets: [
      { min: 0, max: 15000, rate: 0.00 }, // Exonéré
      { min: 15001, max: 30000, rate: 0.20 }, // 20%
      { min: 30001, max: 120000, rate: 0.30 }, // 30%
      { min: 120001, max: Infinity, rate: 0.35 }, // 35%
    ],
    personalAllowance: 15000, // Abattement personnel
    familyAllowance: 300, // Par personne à charge
    declarationDeadline: '04-30', // 30 avril
  },
  
  // Sécurité sociale (CNAS)
  socialSecurity: {
    employee: {
      rate: 0.09, // 9%
      ceiling: 0, // Pas de plafond
    },
    employer: {
      rate: 0.25, // 25%
      ceiling: 0, // Pas de plafond
    },
    // CACOBATPH (Caisse d'Assurance Chômage-Intempéries du Bâtiment, des Travaux Publics et de l'Hydraulique)
    cacobatph: {
      rate: 0.015, // 1.5%
      applicableToAllSectors: false, // Uniquement BTP
    },
  },
  
  // Salaire minimum
  minimumWage: {
    snmg: 20000, // Salaire National Minimum Garanti (DZD)
    lastUpdate: '2024-01-01',
    workingHoursPerWeek: 40,
    workingDaysPerWeek: 5,
    workingDaysPerMonth: 22,
  },
  
  // Congés et jours fériés
  leave: {
    annualLeave: 30, // 30 jours ouvrables
    sickLeave: 15, // 15 jours par an
    maternityLeave: 98, // 14 semaines
    paternityLeave: 3, // 3 jours
  },
  
  // Jours fériés algériens
  publicHolidays: [
    { date: '01-01', name: 'Nouvel An' },
    { date: '05-01', name: 'Fête du Travail' },
    { date: '07-05', name: 'Fête de l\'Indépendance' },
    { date: '11-01', name: 'Anniversaire de la Révolution' },
    // Fêtes religieuses (dates variables)
    { name: 'Aïd el-Fitr', variable: true },
    { name: 'Aïd el-Adha', variable: true },
    { name: 'Mawlid an-Nabawi', variable: true },
    { name: 'Muharram', variable: true },
  ],
  
  // Banques algériennes
  banks: [
    { code: 'BNA', name: 'Banque Nationale d\'Algérie', type: 'public' },
    { code: 'CPA', name: 'Crédit Populaire d\'Algérie', type: 'public' },
    { code: 'BEA', name: 'Banque Extérieure d\'Algérie', type: 'public' },
    { code: 'BADR', name: 'Banque de l\'Agriculture et du Développement Rural', type: 'public' },
    { code: 'BDL', name: 'Banque de Développement Local', type: 'public' },
    { code: 'SGA', name: 'Société Générale Algérie', type: 'private' },
    { code: 'BNPPA', name: 'BNP Paribas El Djazair', type: 'private' },
    { code: 'NATIXIS', name: 'Natixis Algérie', type: 'private' },
    { code: 'TBA', name: 'Trust Bank Algeria', type: 'private' },
    { code: 'ABC', name: 'Arab Banking Corporation Algeria', type: 'private' },
  ],
  
  // Organismes officiels
  institutions: {
    tax: {
      name: 'Direction Générale des Impôts (DGI)',
      website: 'http://www.mfdgi.gov.dz',
    },
    customs: {
      name: 'Direction Générale des Douanes (DGD)',
      website: 'http://www.douane.gov.dz',
    },
    socialSecurity: {
      name: 'Caisse Nationale des Assurances Sociales (CNAS)',
      website: 'http://www.cnas.dz',
    },
    commerce: {
      name: 'Centre National du Registre de Commerce (CNRC)',
      website: 'http://www.cnrc.org.dz',
    },
  },
  
  // Formats de documents
  documentFormats: {
    invoiceNumber: 'FAC-{YYYY}-{MM}-{NNNN}', // FAC-2024-01-0001
    receiptNumber: 'REC-{YYYY}-{MM}-{NNNN}', // REC-2024-01-0001
    payslipNumber: 'PAY-{YYYY}-{MM}-{EMP}-{NN}', // PAY-2024-01-EMP001-01
    journalEntry: 'JE-{YYYY}-{NNNN}', // JE-2024-0001
  },
  
  // Numéros d'identification
  identificationNumbers: {
    nif: {
      name: 'Numéro d\'Identification Fiscale',
      format: '999999999999999', // 15 chiffres
      required: true,
    },
    nis: {
      name: 'Numéro d\'Identification Statistique',
      format: '999999999999999999', // 18 chiffres
      required: true,
    },
    rc: {
      name: 'Registre de Commerce',
      format: '99/99-9999999', // Format: WW/YY-NNNNNNN
      required: true,
    },
    ai: {
      name: 'Article d\'Imposition',
      format: '999999999999999999', // 18 chiffres
      required: false,
    },
  },
  
  // Secteurs d'activité
  businessSectors: [
    { code: 'A', name: 'Agriculture, sylviculture et pêche' },
    { code: 'B', name: 'Industries extractives' },
    { code: 'C', name: 'Industrie manufacturière' },
    { code: 'D', name: 'Production et distribution d\'électricité, de gaz, de vapeur et d\'air conditionné' },
    { code: 'E', name: 'Production et distribution d\'eau; assainissement, gestion des déchets et dépollution' },
    { code: 'F', name: 'Construction' },
    { code: 'G', name: 'Commerce; réparation d\'automobiles et de motocycles' },
    { code: 'H', name: 'Transports et entreposage' },
    { code: 'I', name: 'Hébergement et restauration' },
    { code: 'J', name: 'Information et communication' },
    { code: 'K', name: 'Activités financières et d\'assurance' },
    { code: 'L', name: 'Activités immobilières' },
    { code: 'M', name: 'Activités spécialisées, scientifiques et techniques' },
    { code: 'N', name: 'Activités de services administratifs et de soutien' },
    { code: 'O', name: 'Administration publique' },
    { code: 'P', name: 'Enseignement' },
    { code: 'Q', name: 'Santé humaine et action sociale' },
    { code: 'R', name: 'Arts, spectacles et activités récréatives' },
    { code: 'S', name: 'Autres activités de services' },
    { code: 'T', name: 'Activités des ménages en tant qu\'employeurs' },
    { code: 'U', name: 'Activités des organisations et organismes extraterritoriaux' },
  ],
  
  // Wilayas (provinces) d'Algérie
  wilayas: [
    { code: '01', name: 'Adrar' },
    { code: '02', name: 'Chlef' },
    { code: '03', name: 'Laghouat' },
    { code: '04', name: 'Oum El Bouaghi' },
    { code: '05', name: 'Batna' },
    { code: '06', name: 'Béjaïa' },
    { code: '07', name: 'Biskra' },
    { code: '08', name: 'Béchar' },
    { code: '09', name: 'Blida' },
    { code: '10', name: 'Bouira' },
    { code: '11', name: 'Tamanrasset' },
    { code: '12', name: 'Tébessa' },
    { code: '13', name: 'Tlemcen' },
    { code: '14', name: 'Tiaret' },
    { code: '15', name: 'Tizi Ouzou' },
    { code: '16', name: 'Alger' },
    { code: '17', name: 'Djelfa' },
    { code: '18', name: 'Jijel' },
    { code: '19', name: 'Sétif' },
    { code: '20', name: 'Saïda' },
    { code: '21', name: 'Skikda' },
    { code: '22', name: 'Sidi Bel Abbès' },
    { code: '23', name: 'Annaba' },
    { code: '24', name: 'Guelma' },
    { code: '25', name: 'Constantine' },
    { code: '26', name: 'Médéa' },
    { code: '27', name: 'Mostaganem' },
    { code: '28', name: 'M\'Sila' },
    { code: '29', name: 'Mascara' },
    { code: '30', name: 'Ouargla' },
    { code: '31', name: 'Oran' },
    { code: '32', name: 'El Bayadh' },
    { code: '33', name: 'Illizi' },
    { code: '34', name: 'Bordj Bou Arréridj' },
    { code: '35', name: 'Boumerdès' },
    { code: '36', name: 'El Tarf' },
    { code: '37', name: 'Tindouf' },
    { code: '38', name: 'Tissemsilt' },
    { code: '39', name: 'El Oued' },
    { code: '40', name: 'Khenchela' },
    { code: '41', name: 'Souk Ahras' },
    { code: '42', name: 'Tipaza' },
    { code: '43', name: 'Mila' },
    { code: '44', name: 'Aïn Defla' },
    { code: '45', name: 'Naâma' },
    { code: '46', name: 'Aïn Témouchent' },
    { code: '47', name: 'Ghardaïa' },
    { code: '48', name: 'Relizane' },
    { code: '49', name: 'Timimoun' },
    { code: '50', name: 'Bordj Badji Mokhtar' },
    { code: '51', name: 'Ouled Djellal' },
    { code: '52', name: 'Béni Abbès' },
    { code: '53', name: 'In Salah' },
    { code: '54', name: 'In Guezzam' },
    { code: '55', name: 'Touggourt' },
    { code: '56', name: 'Djanet' },
    { code: '57', name: 'El M\'Ghair' },
    { code: '58', name: 'El Meniaa' },
  ],
} as const;

// Fonctions utilitaires pour l'Algérie
export const formatAlgerianCurrency = (amount: number): string => {
  return new Intl.NumberFormat('fr-DZ', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount) + ' DZD';
};

export const formatAlgerianDate = (date: Date): string => {
  return new Intl.DateTimeFormat('fr-DZ', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }).format(date);
};

export const validateNIF = (nif: string): boolean => {
  // Validation du NIF algérien (15 chiffres)
  const nifRegex = /^\d{15}$/;
  return nifRegex.test(nif);
};

export const validateRC = (rc: string): boolean => {
  // Validation du Registre de Commerce algérien
  const rcRegex = /^\d{2}\/\d{2}-\d{7}$/;
  return rcRegex.test(rc);
};

export const getWilayaByCode = (code: string) => {
  return ALGERIA_CONFIG.wilayas.find(wilaya => wilaya.code === code);
};

export const getSectorByCode = (code: string) => {
  return ALGERIA_CONFIG.businessSectors.find(sector => sector.code === code);
};

export const calculateWorkingDays = (startDate: Date, endDate: Date): number => {
  let workingDays = 0;
  const currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const dayOfWeek = currentDate.getDay();
    // Lundi = 1, Mardi = 2, ..., Vendredi = 5 (jours ouvrables)
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      workingDays++;
    }
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return workingDays;
};
