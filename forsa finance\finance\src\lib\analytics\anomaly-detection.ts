// Service de détection d'anomalies financières local
// Remplace les fonctionnalités AI par des algorithmes de détection basés sur des règles

export interface FinancialData {
  date: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  description: string;
  account?: string;
}

export interface Anomaly {
  id: string;
  type: 'amount' | 'frequency' | 'pattern' | 'duplicate' | 'timing';
  severity: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  data: FinancialData;
  suggestion: string;
  confidence: number; // 0-1
}

export interface AnomalyDetectionConfig {
  amountThresholds: {
    highAmount: number; // Montant considéré comme élevé
    lowAmount: number; // Montant considéré comme faible
    percentageChange: number; // Pourcentage de changement significatif
  };
  frequencyThresholds: {
    maxDailyTransactions: number;
    maxWeeklyTransactions: number;
    unusualGapDays: number; // Nombre de jours sans transaction considéré comme inhabituel
  };
  duplicateDetection: {
    timeWindowHours: number; // Fenêtre de temps pour détecter les doublons
    amountTolerance: number; // Tolérance pour les montants similaires
  };
}

const defaultConfig: AnomalyDetectionConfig = {
  amountThresholds: {
    highAmount: 1000000, // 1M DZD
    lowAmount: 100, // 100 DZD
    percentageChange: 0.5, // 50%
  },
  frequencyThresholds: {
    maxDailyTransactions: 50,
    maxWeeklyTransactions: 200,
    unusualGapDays: 7,
  },
  duplicateDetection: {
    timeWindowHours: 24,
    amountTolerance: 0.01, // 1%
  },
};

export class AnomalyDetectionService {
  private config: AnomalyDetectionConfig;

  constructor(config: AnomalyDetectionConfig = defaultConfig) {
    this.config = config;
  }

  // Fonction principale de détection d'anomalies
  detectAnomalies(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];

    // Trier les données par date
    const sortedData = [...data].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // Détecter les anomalies de montant
    anomalies.push(...this.detectAmountAnomalies(sortedData));

    // Détecter les anomalies de fréquence
    anomalies.push(...this.detectFrequencyAnomalies(sortedData));

    // Détecter les doublons
    anomalies.push(...this.detectDuplicates(sortedData));

    // Détecter les anomalies de timing
    anomalies.push(...this.detectTimingAnomalies(sortedData));

    // Détecter les anomalies de pattern
    anomalies.push(...this.detectPatternAnomalies(sortedData));

    return anomalies.sort((a, b) => b.confidence - a.confidence);
  }

  // Détection d'anomalies de montant
  private detectAmountAnomalies(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];

    // Calculer les statistiques par catégorie
    const categoryStats = this.calculateCategoryStatistics(data);

    data.forEach((transaction, index) => {
      const stats = categoryStats[transaction.category];
      if (!stats) return;

      // Montant exceptionnellement élevé
      if (transaction.amount > this.config.amountThresholds.highAmount) {
        anomalies.push({
          id: `amount-high-${index}`,
          type: 'amount',
          severity: 'high',
          title: 'Montant exceptionnellement élevé',
          description: `Transaction de ${transaction.amount.toLocaleString()} DZD, bien au-dessus de la normale`,
          data: transaction,
          suggestion: 'Vérifiez la validité de cette transaction et assurez-vous qu\'elle est correcte.',
          confidence: 0.9,
        });
      }

      // Montant significativement différent de la moyenne
      const deviationFromMean = Math.abs(transaction.amount - stats.mean) / stats.mean;
      if (deviationFromMean > this.config.amountThresholds.percentageChange && transaction.amount > stats.mean * 2) {
        anomalies.push({
          id: `amount-deviation-${index}`,
          type: 'amount',
          severity: deviationFromMean > 1 ? 'high' : 'medium',
          title: 'Montant inhabituel pour cette catégorie',
          description: `Montant de ${transaction.amount.toLocaleString()} DZD, ${(deviationFromMean * 100).toFixed(0)}% au-dessus de la moyenne (${stats.mean.toLocaleString()} DZD)`,
          data: transaction,
          suggestion: 'Vérifiez si ce montant est correct ou s\'il y a une erreur de saisie.',
          confidence: Math.min(0.8, deviationFromMean),
        });
      }
    });

    return anomalies;
  }

  // Détection d'anomalies de fréquence
  private detectFrequencyAnomalies(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];

    // Grouper par jour
    const dailyTransactions = this.groupByDay(data);

    Object.entries(dailyTransactions).forEach(([date, transactions]) => {
      if (transactions.length > this.config.frequencyThresholds.maxDailyTransactions) {
        anomalies.push({
          id: `frequency-daily-${date}`,
          type: 'frequency',
          severity: 'medium',
          title: 'Nombre de transactions quotidiennes élevé',
          description: `${transactions.length} transactions le ${date}, au-dessus de la limite normale (${this.config.frequencyThresholds.maxDailyTransactions})`,
          data: transactions[0],
          suggestion: 'Vérifiez s\'il n\'y a pas de doublons ou d\'erreurs de saisie.',
          confidence: 0.7,
        });
      }
    });

    // Détecter les gaps inhabituels
    const dates = Object.keys(dailyTransactions).sort();
    for (let i = 1; i < dates.length; i++) {
      const prevDate = new Date(dates[i - 1]);
      const currentDate = new Date(dates[i]);
      const daysDiff = Math.floor((currentDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysDiff > this.config.frequencyThresholds.unusualGapDays) {
        anomalies.push({
          id: `frequency-gap-${dates[i]}`,
          type: 'frequency',
          severity: 'low',
          title: 'Période sans transactions',
          description: `Aucune transaction pendant ${daysDiff} jours (du ${dates[i - 1]} au ${dates[i]})`,
          data: dailyTransactions[dates[i]][0],
          suggestion: 'Vérifiez s\'il manque des transactions ou si c\'est une période normale d\'inactivité.',
          confidence: 0.5,
        });
      }
    }

    return anomalies;
  }

  // Détection de doublons
  private detectDuplicates(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];
    const timeWindow = this.config.duplicateDetection.timeWindowHours * 60 * 60 * 1000;

    for (let i = 0; i < data.length; i++) {
      for (let j = i + 1; j < data.length; j++) {
        const transaction1 = data[i];
        const transaction2 = data[j];

        const timeDiff = Math.abs(new Date(transaction1.date).getTime() - new Date(transaction2.date).getTime());
        const amountDiff = Math.abs(transaction1.amount - transaction2.amount) / Math.max(transaction1.amount, transaction2.amount);

        if (
          timeDiff <= timeWindow &&
          amountDiff <= this.config.duplicateDetection.amountTolerance &&
          transaction1.description.toLowerCase() === transaction2.description.toLowerCase()
        ) {
          anomalies.push({
            id: `duplicate-${i}-${j}`,
            type: 'duplicate',
            severity: 'high',
            title: 'Transaction potentiellement dupliquée',
            description: `Transactions similaires: ${transaction1.amount.toLocaleString()} DZD et ${transaction2.amount.toLocaleString()} DZD`,
            data: transaction2,
            suggestion: 'Vérifiez si ces transactions sont des doublons et supprimez-en une si nécessaire.',
            confidence: 1 - amountDiff,
          });
        }
      }
    }

    return anomalies;
  }

  // Détection d'anomalies de timing
  private detectTimingAnomalies(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];

    data.forEach((transaction, index) => {
      const date = new Date(transaction.date);
      const hour = date.getHours();
      const dayOfWeek = date.getDay();

      // Transactions en dehors des heures ouvrables
      if (hour < 8 || hour > 18) {
        anomalies.push({
          id: `timing-hours-${index}`,
          type: 'timing',
          severity: 'low',
          title: 'Transaction en dehors des heures ouvrables',
          description: `Transaction à ${hour}h${date.getMinutes().toString().padStart(2, '0')}`,
          data: transaction,
          suggestion: 'Vérifiez si cette transaction est normale ou si elle nécessite une attention particulière.',
          confidence: 0.4,
        });
      }

      // Transactions le weekend
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        anomalies.push({
          id: `timing-weekend-${index}`,
          type: 'timing',
          severity: 'low',
          title: 'Transaction le weekend',
          description: `Transaction le ${dayOfWeek === 0 ? 'dimanche' : 'samedi'}`,
          data: transaction,
          suggestion: 'Vérifiez si cette transaction weekend est normale pour votre activité.',
          confidence: 0.3,
        });
      }
    });

    return anomalies;
  }

  // Détection d'anomalies de pattern
  private detectPatternAnomalies(data: FinancialData[]): Anomaly[] {
    const anomalies: Anomaly[] = [];

    // Détecter les montants ronds suspects
    data.forEach((transaction, index) => {
      if (transaction.amount % 1000 === 0 && transaction.amount >= 10000) {
        anomalies.push({
          id: `pattern-round-${index}`,
          type: 'pattern',
          severity: 'low',
          title: 'Montant rond suspect',
          description: `Montant exactement rond: ${transaction.amount.toLocaleString()} DZD`,
          data: transaction,
          suggestion: 'Les montants très ronds peuvent indiquer des estimations ou des erreurs.',
          confidence: 0.3,
        });
      }
    });

    return anomalies;
  }

  // Fonctions utilitaires
  private calculateCategoryStatistics(data: FinancialData[]) {
    const categoryGroups = data.reduce((acc, transaction) => {
      if (!acc[transaction.category]) {
        acc[transaction.category] = [];
      }
      acc[transaction.category].push(transaction.amount);
      return acc;
    }, {} as Record<string, number[]>);

    const stats: Record<string, { mean: number; median: number; std: number; count: number }> = {};

    Object.entries(categoryGroups).forEach(([category, amounts]) => {
      const sorted = amounts.sort((a, b) => a - b);
      const mean = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length;
      const median = sorted[Math.floor(sorted.length / 2)];
      const variance = amounts.reduce((sum, amount) => sum + Math.pow(amount - mean, 2), 0) / amounts.length;
      const std = Math.sqrt(variance);

      stats[category] = { mean, median, std, count: amounts.length };
    });

    return stats;
  }

  private groupByDay(data: FinancialData[]) {
    return data.reduce((acc, transaction) => {
      const date = transaction.date.split('T')[0]; // Prendre seulement la partie date
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(transaction);
      return acc;
    }, {} as Record<string, FinancialData[]>);
  }

  // Méthode pour mettre à jour la configuration
  updateConfig(newConfig: Partial<AnomalyDetectionConfig>) {
    this.config = { ...this.config, ...newConfig };
  }

  // Méthode pour obtenir un résumé des anomalies
  getAnomalySummary(anomalies: Anomaly[]) {
    const summary = {
      total: anomalies.length,
      high: anomalies.filter(a => a.severity === 'high').length,
      medium: anomalies.filter(a => a.severity === 'medium').length,
      low: anomalies.filter(a => a.severity === 'low').length,
      byType: anomalies.reduce((acc, anomaly) => {
        acc[anomaly.type] = (acc[anomaly.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return summary;
  }
}

// Instance par défaut
export const anomalyDetectionService = new AnomalyDetectionService();
