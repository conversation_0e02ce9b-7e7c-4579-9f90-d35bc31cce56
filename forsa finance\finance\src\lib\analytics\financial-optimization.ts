// Service d'optimisation financière local
// Remplace les fonctionnalités AI par des algorithmes d'analyse financière

export interface FinancialMetrics {
  revenue: number;
  expenses: number;
  profit: number;
  cashFlow: number;
  assets: number;
  liabilities: number;
  equity: number;
}

export interface OptimizationSuggestion {
  id: string;
  category: 'cost_reduction' | 'revenue_increase' | 'cash_flow' | 'tax_optimization' | 'efficiency';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  potentialSavings: number;
  implementationCost: number;
  timeframe: string;
  steps: string[];
  kpis: string[];
  confidence: number;
}

export interface CashFlowForecast {
  month: string;
  projectedIncome: number;
  projectedExpenses: number;
  netCashFlow: number;
  cumulativeCashFlow: number;
  confidence: number;
}

export class FinancialOptimizationService {
  
  // Analyse financière complète et génération de suggestions
  analyzeAndOptimize(
    currentMetrics: FinancialMetrics,
    historicalData: FinancialMetrics[],
    industryBenchmarks?: Partial<FinancialMetrics>
  ): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Analyse de la rentabilité
    suggestions.push(...this.analyzeProfitability(currentMetrics, historicalData));

    // Analyse des coûts
    suggestions.push(...this.analyzeCosts(currentMetrics, historicalData));

    // Analyse de la trésorerie
    suggestions.push(...this.analyzeCashFlow(currentMetrics, historicalData));

    // Optimisation fiscale
    suggestions.push(...this.analyzeTaxOptimization(currentMetrics));

    // Analyse d'efficacité
    suggestions.push(...this.analyzeEfficiency(currentMetrics, historicalData));

    // Comparaison avec les benchmarks de l'industrie
    if (industryBenchmarks) {
      suggestions.push(...this.compareWithBenchmarks(currentMetrics, industryBenchmarks));
    }

    return suggestions.sort((a, b) => {
      // Trier par priorité puis par économies potentielles
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.potentialSavings - a.potentialSavings;
    });
  }

  // Analyse de la rentabilité
  private analyzeProfitability(current: FinancialMetrics, historical: FinancialMetrics[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const profitMargin = current.profit / current.revenue;
    const avgHistoricalMargin = historical.length > 0 
      ? historical.reduce((sum, m) => sum + (m.profit / m.revenue), 0) / historical.length 
      : profitMargin;

    // Marge bénéficiaire faible
    if (profitMargin < 0.1) {
      suggestions.push({
        id: 'low-profit-margin',
        category: 'revenue_increase',
        priority: 'high',
        title: 'Améliorer la marge bénéficiaire',
        description: `Votre marge bénéficiaire actuelle est de ${(profitMargin * 100).toFixed(1)}%, ce qui est relativement faible.`,
        potentialSavings: current.revenue * 0.05, // 5% d'amélioration potentielle
        implementationCost: current.revenue * 0.02,
        timeframe: '3-6 mois',
        steps: [
          'Analyser la structure des coûts par produit/service',
          'Identifier les produits/services les plus rentables',
          'Revoir la stratégie de prix',
          'Optimiser les processus de production/livraison',
          'Négocier avec les fournisseurs'
        ],
        kpis: ['Marge bénéficiaire', 'Chiffre d\'affaires par produit', 'Coût de revient'],
        confidence: 0.8
      });
    }

    // Baisse de rentabilité
    if (historical.length > 0 && profitMargin < avgHistoricalMargin * 0.9) {
      suggestions.push({
        id: 'declining-profitability',
        category: 'cost_reduction',
        priority: 'high',
        title: 'Inverser la tendance de baisse de rentabilité',
        description: `La rentabilité a diminué de ${((avgHistoricalMargin - profitMargin) * 100).toFixed(1)}% par rapport à la moyenne historique.`,
        potentialSavings: current.revenue * (avgHistoricalMargin - profitMargin),
        implementationCost: current.revenue * 0.01,
        timeframe: '2-4 mois',
        steps: [
          'Identifier les causes de la baisse de rentabilité',
          'Analyser l\'évolution des coûts et des prix',
          'Mettre en place un plan d\'action correctif',
          'Surveiller les indicateurs clés mensuellement'
        ],
        kpis: ['Évolution de la marge', 'Coûts variables', 'Prix de vente moyens'],
        confidence: 0.9
      });
    }

    return suggestions;
  }

  // Analyse des coûts
  private analyzeCosts(current: FinancialMetrics, historical: FinancialMetrics[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];
    const costRatio = current.expenses / current.revenue;

    // Ratio de coûts élevé
    if (costRatio > 0.8) {
      suggestions.push({
        id: 'high-cost-ratio',
        category: 'cost_reduction',
        priority: 'high',
        title: 'Réduire le ratio de coûts',
        description: `Vos coûts représentent ${(costRatio * 100).toFixed(1)}% du chiffre d\'affaires, ce qui est élevé.`,
        potentialSavings: current.expenses * 0.1, // 10% de réduction potentielle
        implementationCost: current.expenses * 0.02,
        timeframe: '3-6 mois',
        steps: [
          'Analyser les postes de coûts les plus importants',
          'Identifier les coûts non essentiels',
          'Négocier avec les fournisseurs',
          'Automatiser les processus répétitifs',
          'Optimiser l\'utilisation des ressources'
        ],
        kpis: ['Ratio coûts/CA', 'Coût par unité produite', 'Productivité'],
        confidence: 0.7
      });
    }

    // Coûts en augmentation
    if (historical.length > 0) {
      const avgHistoricalCostRatio = historical.reduce((sum, m) => sum + (m.expenses / m.revenue), 0) / historical.length;
      if (costRatio > avgHistoricalCostRatio * 1.1) {
        suggestions.push({
          id: 'increasing-costs',
          category: 'cost_reduction',
          priority: 'medium',
          title: 'Contrôler l\'augmentation des coûts',
          description: `Les coûts ont augmenté de ${((costRatio - avgHistoricalCostRatio) * 100).toFixed(1)}% par rapport à la moyenne.`,
          potentialSavings: current.revenue * (costRatio - avgHistoricalCostRatio),
          implementationCost: current.revenue * 0.005,
          timeframe: '1-3 mois',
          steps: [
            'Mettre en place un suivi mensuel des coûts',
            'Identifier les postes en dérive',
            'Établir des budgets prévisionnels',
            'Mettre en place des alertes de dépassement'
          ],
          kpis: ['Évolution mensuelle des coûts', 'Écarts budgétaires'],
          confidence: 0.8
        });
      }
    }

    return suggestions;
  }

  // Analyse de la trésorerie
  private analyzeCashFlow(current: FinancialMetrics, historical: FinancialMetrics[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Trésorerie faible
    if (current.cashFlow < current.expenses * 0.1) {
      suggestions.push({
        id: 'low-cash-flow',
        category: 'cash_flow',
        priority: 'high',
        title: 'Améliorer la gestion de trésorerie',
        description: 'Votre trésorerie est faible par rapport à vos charges mensuelles.',
        potentialSavings: current.expenses * 0.05,
        implementationCost: 0,
        timeframe: '1-2 mois',
        steps: [
          'Accélérer le recouvrement des créances',
          'Négocier des délais de paiement avec les fournisseurs',
          'Optimiser la gestion des stocks',
          'Mettre en place un prévisionnel de trésorerie'
        ],
        kpis: ['Délai de recouvrement', 'Délai de paiement fournisseurs', 'Rotation des stocks'],
        confidence: 0.9
      });
    }

    // Ratio d'endettement élevé
    const debtRatio = current.liabilities / current.assets;
    if (debtRatio > 0.7) {
      suggestions.push({
        id: 'high-debt-ratio',
        category: 'cash_flow',
        priority: 'medium',
        title: 'Réduire le ratio d\'endettement',
        description: `Votre ratio d\'endettement est de ${(debtRatio * 100).toFixed(1)}%, ce qui peut limiter votre flexibilité financière.`,
        potentialSavings: current.liabilities * 0.05, // Économies d'intérêts
        implementationCost: 0,
        timeframe: '6-12 mois',
        steps: [
          'Établir un plan de remboursement prioritaire',
          'Renégocier les conditions de crédit',
          'Augmenter les fonds propres si possible',
          'Optimiser la structure financière'
        ],
        kpis: ['Ratio d\'endettement', 'Coût de la dette', 'Capacité de remboursement'],
        confidence: 0.7
      });
    }

    return suggestions;
  }

  // Optimisation fiscale (spécifique à l'Algérie)
  private analyzeTaxOptimization(current: FinancialMetrics): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Optimisation TVA
    suggestions.push({
      id: 'vat-optimization',
      category: 'tax_optimization',
      priority: 'medium',
      title: 'Optimiser la gestion de la TVA',
      description: 'Améliorez votre gestion de la TVA pour optimiser votre trésorerie.',
      potentialSavings: current.revenue * 0.02,
      implementationCost: 0,
      timeframe: '1 mois',
      steps: [
        'Vérifier la récupération de toute la TVA déductible',
        'Optimiser le timing des déclarations',
        'S\'assurer de la conformité des factures',
        'Mettre en place un suivi mensuel de la TVA'
      ],
      kpis: ['TVA récupérée', 'Délai de récupération', 'Taux de conformité'],
      confidence: 0.6
    });

    // Optimisation charges sociales
    if (current.expenses > 0) {
      suggestions.push({
        id: 'social-charges-optimization',
        category: 'tax_optimization',
        priority: 'low',
        title: 'Optimiser les charges sociales',
        description: 'Vérifiez les possibilités d\'optimisation des charges sociales dans le respect de la réglementation algérienne.',
        potentialSavings: current.expenses * 0.01,
        implementationCost: 0,
        timeframe: '2-3 mois',
        steps: [
          'Vérifier l\'application correcte des taux CNAS',
          'S\'assurer de la conformité des déclarations',
          'Optimiser la structure des rémunérations',
          'Consulter un expert-comptable si nécessaire'
        ],
        kpis: ['Taux de charges sociales', 'Conformité déclarative'],
        confidence: 0.5
    });
    }

    return suggestions;
  }

  // Analyse d'efficacité
  private analyzeEfficiency(current: FinancialMetrics, historical: FinancialMetrics[]): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Rotation des actifs
    const assetTurnover = current.revenue / current.assets;
    if (assetTurnover < 1) {
      suggestions.push({
        id: 'low-asset-turnover',
        category: 'efficiency',
        priority: 'medium',
        title: 'Améliorer la rotation des actifs',
        description: `Votre rotation des actifs est de ${assetTurnover.toFixed(2)}, ce qui indique une sous-utilisation des actifs.`,
        potentialSavings: current.assets * 0.05,
        implementationCost: current.assets * 0.01,
        timeframe: '3-6 mois',
        steps: [
          'Analyser l\'utilisation de chaque actif',
          'Identifier les actifs sous-utilisés',
          'Optimiser les processus opérationnels',
          'Considérer la cession d\'actifs non productifs'
        ],
        kpis: ['Rotation des actifs', 'Taux d\'utilisation', 'Productivité'],
        confidence: 0.6
      });
    }

    return suggestions;
  }

  // Comparaison avec les benchmarks de l'industrie
  private compareWithBenchmarks(current: FinancialMetrics, benchmarks: Partial<FinancialMetrics>): OptimizationSuggestion[] {
    const suggestions: OptimizationSuggestion[] = [];

    // Comparer la marge bénéficiaire
    if (benchmarks.profit && benchmarks.revenue) {
      const benchmarkMargin = benchmarks.profit / benchmarks.revenue;
      const currentMargin = current.profit / current.revenue;
      
      if (currentMargin < benchmarkMargin * 0.8) {
        suggestions.push({
          id: 'below-industry-margin',
          category: 'revenue_increase',
          priority: 'high',
          title: 'Rattraper la marge de l\'industrie',
          description: `Votre marge (${(currentMargin * 100).toFixed(1)}%) est inférieure à la moyenne du secteur (${(benchmarkMargin * 100).toFixed(1)}%).`,
          potentialSavings: current.revenue * (benchmarkMargin - currentMargin),
          implementationCost: current.revenue * 0.02,
          timeframe: '6-12 mois',
          steps: [
            'Analyser les meilleures pratiques du secteur',
            'Identifier les écarts de performance',
            'Mettre en place un plan d\'amélioration',
            'Benchmarker régulièrement les performances'
          ],
          kpis: ['Marge vs secteur', 'Position concurrentielle'],
          confidence: 0.7
        });
      }
    }

    return suggestions;
  }

  // Prévision de trésorerie
  generateCashFlowForecast(
    currentCashFlow: number,
    monthlyIncome: number[],
    monthlyExpenses: number[],
    months: number = 12
  ): CashFlowForecast[] {
    const forecast: CashFlowForecast[] = [];
    let cumulativeCashFlow = currentCashFlow;

    for (let i = 0; i < months; i++) {
      const monthDate = new Date();
      monthDate.setMonth(monthDate.getMonth() + i + 1);
      
      // Utiliser les données historiques ou une moyenne pour la prévision
      const avgIncome = monthlyIncome.length > 0 
        ? monthlyIncome.reduce((sum, income) => sum + income, 0) / monthlyIncome.length
        : 0;
      const avgExpenses = monthlyExpenses.length > 0
        ? monthlyExpenses.reduce((sum, expense) => sum + expense, 0) / monthlyExpenses.length
        : 0;

      // Ajouter une variation saisonnière simple
      const seasonalFactor = 1 + 0.1 * Math.sin((i / 12) * 2 * Math.PI);
      const projectedIncome = avgIncome * seasonalFactor;
      const projectedExpenses = avgExpenses;

      const netCashFlow = projectedIncome - projectedExpenses;
      cumulativeCashFlow += netCashFlow;

      // Calculer la confiance basée sur la variance historique
      const confidence = Math.max(0.3, 1 - (i * 0.05)); // Diminue avec le temps

      forecast.push({
        month: monthDate.toISOString().substring(0, 7), // YYYY-MM
        projectedIncome,
        projectedExpenses,
        netCashFlow,
        cumulativeCashFlow,
        confidence
      });
    }

    return forecast;
  }

  // Calcul du ROI pour une suggestion
  calculateROI(suggestion: OptimizationSuggestion): number {
    if (suggestion.implementationCost === 0) return Infinity;
    return (suggestion.potentialSavings - suggestion.implementationCost) / suggestion.implementationCost;
  }

  // Priorisation des suggestions basée sur le ROI et l'impact
  prioritizeSuggestions(suggestions: OptimizationSuggestion[]): OptimizationSuggestion[] {
    return suggestions.sort((a, b) => {
      const roiA = this.calculateROI(a);
      const roiB = this.calculateROI(b);
      
      // Combiner ROI et impact potentiel
      const scoreA = roiA * a.potentialSavings * a.confidence;
      const scoreB = roiB * b.potentialSavings * b.confidence;
      
      return scoreB - scoreA;
    });
  }
}

// Instance par défaut
export const financialOptimizationService = new FinancialOptimizationService();
