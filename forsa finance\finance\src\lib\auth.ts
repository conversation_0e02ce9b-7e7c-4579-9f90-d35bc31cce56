// Système d'authentification local pour l'application desktop
import { supabase } from './supabase';
import { USER_ROLES, PERMISSIONS, ROLE_PERMISSIONS, type UserRole, type Permission } from './config';
import type { User, Company } from '@/types';

// Utilisateur par défaut pour le mode hors ligne
const DEFAULT_USER: User = {
  id: 'offline-user',
  email: '<EMAIL>',
  first_name: 'Admin',
  last_name: 'Local',
  avatar_url: null,
  role: 'admin',
  permissions: Object.values(PERMISSIONS),
  company_id: 'default-company',
  is_active: true,
  last_login: new Date().toISOString(),
  phone: null,
  address: null,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
};

// État d'authentification local
let currentUser: User | null = DEFAULT_USER;
let authState = true;

// Client-side auth utilities
export const createClientSupabaseClient = () => {
  return supabase;
};

// Get current user with full profile
export async function getCurrentUser(): Promise<User | null> {
  try {
    return currentUser;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Get user by ID
export async function getUser(): Promise<User | null> {
  return getCurrentUser();
}

// Check if user has permission
export function hasPermission(user: User | null, permission: Permission): boolean {
  if (!user) return false;
  if (user.role === 'admin') return true;
  return user.permissions.includes(permission);
}

// Check if user has role
export function hasRole(user: User | null, role: UserRole): boolean {
  if (!user) return false;
  return user.role === role;
}

// Check if user has any of the specified roles
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  if (!user) return false;
  return roles.includes(user.role);
}

// Get user permissions based on role
export function getUserPermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

// Check if user can access resource
export function canAccessResource(user: User | null, requiredPermission: Permission): boolean {
  return hasPermission(user, requiredPermission);
}

// Authentication functions for local mode
export async function signIn(email: string, password: string): Promise<{ user: User | null; error: string | null }> {
  try {
    if (email === '<EMAIL>' && password === 'admin') {
      currentUser = DEFAULT_USER;
      authState = true;
      return { user: currentUser, error: null };
    }
    return { user: null, error: 'Identifiants invalides' };
  } catch (error) {
    return { user: null, error: 'Erreur de connexion' };
  }
}

export async function signOut(): Promise<{ error: string | null }> {
  try {
    currentUser = null;
    authState = false;
    return { error: null };
  } catch (error) {
    return { error: 'Erreur de déconnexion' };
  }
}

export function isUserAuthenticated(): boolean {
  return authState && currentUser !== null;
}

// Company-related functions
export async function getUserCompany(userId: string): Promise<Company | null> {
  try {
    return {
      id: 'default-company',
      name: 'Entreprise Demo',
      address: 'Alger, Algérie',
      phone: '+213 21 XX XX XX',
      email: '<EMAIL>',
      nif: '123456789012345',
      rc: '16/00-1234567',
      logo_url: null,
      website: null,
      industry: 'Services',
      employee_count: 10,
      settings: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  } catch (error) {
    console.error('Error getting user company:', error);
    return null;
  }
}

// Middleware function for protecting routes
export function requireAuth(): User | null {
  if (!isUserAuthenticated()) {
    return null;
  }
  return currentUser;
}

// Middleware function for requiring specific permission
export function requirePermission(permission: Permission): User | null {
  const user = requireAuth();
  if (!user || !hasPermission(user, permission)) {
    return null;
  }
  return user;
}

// Middleware function for requiring specific role
export function requireRole(role: UserRole): User | null {
  const user = requireAuth();
  if (!user || !hasRole(user, role)) {
    return null;
  }
  return user;
}

// Initialize authentication state
export function initializeAuth(): void {
  currentUser = DEFAULT_USER;
  authState = true;
}

// Get all users (for admin)
export async function getAllUsers(): Promise<User[]> {
  try {
    return currentUser ? [currentUser] : [];
  } catch (error) {
    console.error('Error getting all users:', error);
    return [];
  }
}

// Session management for compatibility
export async function getSession() {
  if (authState && currentUser) {
    return {
      user: currentUser,
      access_token: 'offline-token',
      refresh_token: 'offline-refresh'
    };
  }
  return null;
}

// Check if user is authenticated (compatibility function)
export async function isAuthenticated() {
  const session = await getSession();
  return !!session;
}

// Get role permissions
export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role] || [];
}

// Initialize auth on module load
initializeAuth();
