// Configuration constants for Forsa Finance

export const APP_CONFIG = {
  name: process.env.APP_NAME || 'Forsa Finance',
  version: process.env.APP_VERSION || '1.0.0',
  environment: process.env.APP_ENVIRONMENT || 'development',
  url: process.env.NEXTAUTH_URL || 'http://localhost:9002',
} as const;

export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
} as const;

export const COMPANY_CONFIG = {
  name: process.env.DEFAULT_COMPANY_NAME || 'SARL ForsaTech',
  address: process.env.DEFAULT_COMPANY_ADDRESS || 'Cité 1000 Logements, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>',
  nif: process.env.DEFAULT_COMPANY_NIF || '001234567890123',
  rc: process.env.DEFAULT_COMPANY_RC || '12345678901234',
  phone: process.env.DEFAULT_COMPANY_PHONE || '+213 21 12 34 56',
  email: process.env.DEFAULT_COMPANY_EMAIL || '<EMAIL>',
} as const;

export const TAX_CONFIG = {
  tva: {
    standard: parseFloat(process.env.TVA_RATE_STANDARD || '0.19'),
    reduced: parseFloat(process.env.TVA_RATE_REDUCED || '0.09'),
  },
  irg: {
    threshold: parseFloat(process.env.IRG_THRESHOLD || '30000'),
    brackets: [
      { min: 0, max: 120000, rate: 0 },
      { min: 120000, max: 360000, rate: 0.20 },
      { min: 360000, max: 1440000, rate: 0.30 },
      { min: 1440000, max: Infinity, rate: 0.35 },
    ],
  },
  cnas: {
    employee: parseFloat(process.env.CNAS_RATE_EMPLOYEE || '0.09'),
    employer: parseFloat(process.env.CNAS_RATE_EMPLOYER || '0.25'),
  },
  cacobatph: parseFloat(process.env.CACOBATPH_RATE || '0.005'),
} as const;

export const FILE_CONFIG = {
  maxSize: parseInt(process.env.MAX_FILE_SIZE || '10485760'), // 10MB
  allowedTypes: (process.env.ALLOWED_FILE_TYPES || 'pdf,xlsx,csv,jpg,png').split(','),
} as const;

export const SECURITY_CONFIG = {
  jwtSecret: process.env.JWT_SECRET || 'your-jwt-secret',
  encryptionKey: process.env.ENCRYPTION_KEY || 'your-encryption-key',
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
} as const;

export const EMAIL_CONFIG = {
  smtp: {
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
  },
} as const;

// User roles and permissions
export const USER_ROLES = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  MANAGER: 'manager',
  ACCOUNTANT: 'accountant',
  HR_MANAGER: 'hr_manager',
  EMPLOYEE: 'employee',
  VIEWER: 'viewer',
} as const;

export const PERMISSIONS = {
  // Dashboard
  VIEW_DASHBOARD: 'view_dashboard',
  
  // Financial
  VIEW_ACCOUNTING: 'view_accounting',
  EDIT_ACCOUNTING: 'edit_accounting',
  VIEW_TREASURY: 'view_treasury',
  EDIT_TREASURY: 'edit_treasury',
  VIEW_INVOICING: 'view_invoicing',
  EDIT_INVOICING: 'edit_invoicing',
  
  // HR
  VIEW_HR: 'view_hr',
  EDIT_HR: 'edit_hr',
  VIEW_PAYROLL: 'view_payroll',
  EDIT_PAYROLL: 'edit_payroll',
  
  // Stock
  VIEW_STOCK: 'view_stock',
  EDIT_STOCK: 'edit_stock',
  
  // Clients/Suppliers
  VIEW_CLIENTS: 'view_clients',
  EDIT_CLIENTS: 'edit_clients',
  VIEW_SUPPLIERS: 'view_suppliers',
  EDIT_SUPPLIERS: 'edit_suppliers',
  
  // Tax
  VIEW_TAX: 'view_tax',
  EDIT_TAX: 'edit_tax',
  
  // AI Analysis
  USE_AI_ANALYSIS: 'use_ai_analysis',
  
  // Settings
  VIEW_SETTINGS: 'view_settings',
  EDIT_SETTINGS: 'edit_settings',
  MANAGE_USERS: 'manage_users',
} as const;

// Role permissions mapping
export const ROLE_PERMISSIONS = {
  [USER_ROLES.SUPER_ADMIN]: Object.values(PERMISSIONS),
  [USER_ROLES.ADMIN]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ACCOUNTING,
    PERMISSIONS.EDIT_ACCOUNTING,
    PERMISSIONS.VIEW_TREASURY,
    PERMISSIONS.EDIT_TREASURY,
    PERMISSIONS.VIEW_INVOICING,
    PERMISSIONS.EDIT_INVOICING,
    PERMISSIONS.VIEW_HR,
    PERMISSIONS.EDIT_HR,
    PERMISSIONS.VIEW_PAYROLL,
    PERMISSIONS.EDIT_PAYROLL,
    PERMISSIONS.VIEW_STOCK,
    PERMISSIONS.EDIT_STOCK,
    PERMISSIONS.VIEW_CLIENTS,
    PERMISSIONS.EDIT_CLIENTS,
    PERMISSIONS.VIEW_SUPPLIERS,
    PERMISSIONS.EDIT_SUPPLIERS,
    PERMISSIONS.VIEW_TAX,
    PERMISSIONS.EDIT_TAX,
    PERMISSIONS.USE_AI_ANALYSIS,
    PERMISSIONS.VIEW_SETTINGS,
    PERMISSIONS.EDIT_SETTINGS,
  ],
  [USER_ROLES.MANAGER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ACCOUNTING,
    PERMISSIONS.VIEW_TREASURY,
    PERMISSIONS.VIEW_INVOICING,
    PERMISSIONS.EDIT_INVOICING,
    PERMISSIONS.VIEW_HR,
    PERMISSIONS.VIEW_PAYROLL,
    PERMISSIONS.VIEW_STOCK,
    PERMISSIONS.EDIT_STOCK,
    PERMISSIONS.VIEW_CLIENTS,
    PERMISSIONS.EDIT_CLIENTS,
    PERMISSIONS.VIEW_SUPPLIERS,
    PERMISSIONS.EDIT_SUPPLIERS,
    PERMISSIONS.USE_AI_ANALYSIS,
  ],
  [USER_ROLES.ACCOUNTANT]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ACCOUNTING,
    PERMISSIONS.EDIT_ACCOUNTING,
    PERMISSIONS.VIEW_TREASURY,
    PERMISSIONS.EDIT_TREASURY,
    PERMISSIONS.VIEW_INVOICING,
    PERMISSIONS.EDIT_INVOICING,
    PERMISSIONS.VIEW_TAX,
    PERMISSIONS.EDIT_TAX,
    PERMISSIONS.USE_AI_ANALYSIS,
  ],
  [USER_ROLES.HR_MANAGER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_HR,
    PERMISSIONS.EDIT_HR,
    PERMISSIONS.VIEW_PAYROLL,
    PERMISSIONS.EDIT_PAYROLL,
  ],
  [USER_ROLES.EMPLOYEE]: [
    PERMISSIONS.VIEW_DASHBOARD,
  ],
  [USER_ROLES.VIEWER]: [
    PERMISSIONS.VIEW_DASHBOARD,
    PERMISSIONS.VIEW_ACCOUNTING,
    PERMISSIONS.VIEW_TREASURY,
    PERMISSIONS.VIEW_INVOICING,
    PERMISSIONS.VIEW_HR,
    PERMISSIONS.VIEW_PAYROLL,
    PERMISSIONS.VIEW_STOCK,
    PERMISSIONS.VIEW_CLIENTS,
    PERMISSIONS.VIEW_SUPPLIERS,
    PERMISSIONS.VIEW_TAX,
  ],
} as const;

// Database table names
export const TABLES = {
  USERS: 'users',
  COMPANIES: 'companies',
  EMPLOYEES: 'employees',
  CLIENTS: 'clients',
  SUPPLIERS: 'suppliers',
  PRODUCTS: 'products',
  INVOICES: 'invoices',
  INVOICE_ITEMS: 'invoice_items',
  JOURNAL_ENTRIES: 'journal_entries',
  ACCOUNTS: 'accounts',
  STOCK_MOVEMENTS: 'stock_movements',
  PAYSLIPS: 'payslips',
  TAX_DECLARATIONS: 'tax_declarations',
  BANK_ACCOUNTS: 'bank_accounts',
  BANK_TRANSACTIONS: 'bank_transactions',
  SETTINGS: 'settings',
  AUDIT_LOGS: 'audit_logs',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];
export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
export type TableName = typeof TABLES[keyof typeof TABLES];

// Helper function to get permissions for a role
export function getRolePermissions(role: UserRole): Permission[] {
  return [...(ROLE_PERMISSIONS[role] || [])];
}
