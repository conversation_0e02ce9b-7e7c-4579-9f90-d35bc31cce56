import { localDatabase } from './database';
import { v4 as uuidv4 } from 'uuid';

// Local database service that replaces Supabase functionality
class DatabaseService {
  private db = localDatabase;

  // Generic CRUD operations
  async create(table: string, data: Record<string, any>): Promise<any> {
    try {
      const id = data.id || uuidv4();
      const dataWithTimestamps = {
        ...data,
        id,
      };

      return this.db.insert(table, dataWithTimestamps);
    } catch (error) {
      console.error(`<PERSON>rror creating record in ${table}:`, error);
      throw error;
    }
  }

  async findById(table: string, id: string): Promise<any> {
    try {
      return this.db.findById(table, id);
    } catch (error) {
      console.error(`Error finding record by ID in ${table}:`, error);
      throw error;
    }
  }

  async findAll(table: string, filters?: Record<string, any>): Promise<any[]> {
    try {
      return this.db.findAll(table, filters);
    } catch (error) {
      console.error(`Error finding records in ${table}:`, error);
      throw error;
    }
  }

  async update(table: string, id: string, data: Record<string, any>): Promise<any> {
    try {
      const result = this.db.update(table, id, data);
      if (!result) {
        throw new Error(`No record found with id ${id} in ${table}`);
      }
      return result;
    } catch (error) {
      console.error(`Error updating record in ${table}:`, error);
      throw error;
    }
  }

  async delete(table: string, id: string): Promise<boolean> {
    try {
      return this.db.delete(table, id);
    } catch (error) {
      console.error(`Error deleting record from ${table}:`, error);
      throw error;
    }
  }

  // Advanced query methods (simplified for localStorage)
  async query(sql: string, params: any[] = []): Promise<any[]> {
    try {
      // Pour localStorage, on simule les requêtes simples
      console.warn('Complex SQL queries not supported in localStorage mode');
      return [];
    } catch (error) {
      console.error('Error executing query:', error);
      throw error;
    }
  }

  async queryOne(sql: string, params: any[] = []): Promise<any> {
    try {
      const results = await this.query(sql, params);
      return results[0] || null;
    } catch (error) {
      console.error('Error executing query:', error);
      throw error;
    }
  }

  // Transaction support (simplified)
  async transaction(callback: (db: any) => void): Promise<void> {
    try {
      // En mode localStorage, on exécute directement
      callback(this.db);
    } catch (error) {
      console.error('Transaction failed:', error);
      throw error;
    }
  }

  // Company-specific operations
  async findByCompany(table: string, companyId: string, filters?: Record<string, any>): Promise<any[]> {
    const allFilters = { company_id: companyId, ...filters };
    return this.findAll(table, allFilters);
  }

  // Pagination support
  async findWithPagination(
    table: string, 
    page: number = 1, 
    limit: number = 10, 
    filters?: Record<string, any>
  ): Promise<{ data: any[], total: number, page: number, limit: number }> {
    try {
      const offset = (page - 1) * limit;
      
      // Build base query
      let query = `SELECT * FROM ${table}`;
      let countQuery = `SELECT COUNT(*) as total FROM ${table}`;
      const values: any[] = [];

      if (filters && Object.keys(filters).length > 0) {
        const conditions = Object.keys(filters).map(key => `${key} = ?`);
        const whereClause = ` WHERE ${conditions.join(' AND ')}`;
        query += whereClause;
        countQuery += whereClause;
        values.push(...Object.values(filters));
      }

      // Add pagination
      query += ` LIMIT ? OFFSET ?`;
      
      // Get total count
      const countStmt = this.db.prepare(countQuery);
      const { total } = countStmt.get(...values) as { total: number };

      // Get paginated data
      const dataStmt = this.db.prepare(query);
      const data = dataStmt.all(...values, limit, offset);

      return {
        data,
        total,
        page,
        limit
      };
    } catch (error) {
      console.error('Error in paginated query:', error);
      throw error;
    }
  }

  // Search functionality
  async search(table: string, searchTerm: string, searchFields: string[]): Promise<any[]> {
    try {
      const conditions = searchFields.map(field => `${field} LIKE ?`);
      const query = `SELECT * FROM ${table} WHERE ${conditions.join(' OR ')}`;
      const searchValue = `%${searchTerm}%`;
      const values = searchFields.map(() => searchValue);

      const stmt = this.db.prepare(query);
      return stmt.all(...values);
    } catch (error) {
      console.error('Error in search:', error);
      throw error;
    }
  }

  // Audit logging
  async logAudit(
    userId: string,
    action: string,
    tableName: string,
    recordId: string,
    oldValues?: Record<string, any>,
    newValues?: Record<string, any>,
    companyId?: string
  ): Promise<void> {
    try {
      await this.create('audit_logs', {
        user_id: userId,
        action,
        table_name: tableName,
        record_id: recordId,
        old_values: oldValues ? JSON.stringify(oldValues) : null,
        new_values: newValues ? JSON.stringify(newValues) : null,
        company_id: companyId,
        ip_address: null,
        user_agent: null,
      });
    } catch (error) {
      console.error('Failed to log audit:', error);
    }
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();

// Export for backward compatibility with Supabase code
export const supabase = databaseService;
export const supabaseAdmin = databaseService;

// Export the class for direct instantiation if needed
export { DatabaseService };
