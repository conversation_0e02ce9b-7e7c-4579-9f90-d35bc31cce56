// Base de données locale pour le navigateur
// Utilise localStorage pour la persistance des données

export interface DatabaseRecord {
  id: string;
  [key: string]: any;
}

// Simulation d'une base de données avec localStorage
class LocalDatabase {
  private prefix = 'forsa_finance_';

  // Obtenir tous les enregistrements d'une table
  getTable(tableName: string): DatabaseRecord[] {
    if (typeof window === 'undefined') return [];
    
    try {
      const data = localStorage.getItem(`${this.prefix}${tableName}`);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error(`Error reading table ${tableName}:`, error);
      return [];
    }
  }

  // Sauvegarder une table complète
  setTable(tableName: string, records: DatabaseRecord[]): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(`${this.prefix}${tableName}`, JSON.stringify(records));
    } catch (error) {
      console.error(`Error saving table ${tableName}:`, error);
    }
  }

  // Ajouter un enregistrement
  insert(tableName: string, record: DatabaseRecord): DatabaseRecord {
    const records = this.getTable(tableName);
    const newRecord = {
      ...record,
      id: record.id || this.generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    records.push(newRecord);
    this.setTable(tableName, records);
    return newRecord;
  }

  // Trouver un enregistrement par ID
  findById(tableName: string, id: string): DatabaseRecord | null {
    const records = this.getTable(tableName);
    return records.find(record => record.id === id) || null;
  }

  // Trouver tous les enregistrements avec filtres optionnels
  findAll(tableName: string, filters?: Record<string, any>): DatabaseRecord[] {
    let records = this.getTable(tableName);
    
    if (filters) {
      records = records.filter(record => {
        return Object.entries(filters).every(([key, value]) => {
          return record[key] === value;
        });
      });
    }
    
    return records;
  }

  // Mettre à jour un enregistrement
  update(tableName: string, id: string, updates: Partial<DatabaseRecord>): DatabaseRecord | null {
    const records = this.getTable(tableName);
    const index = records.findIndex(record => record.id === id);
    
    if (index === -1) return null;
    
    records[index] = {
      ...records[index],
      ...updates,
      updated_at: new Date().toISOString(),
    };
    
    this.setTable(tableName, records);
    return records[index];
  }

  // Supprimer un enregistrement
  delete(tableName: string, id: string): boolean {
    const records = this.getTable(tableName);
    const index = records.findIndex(record => record.id === id);
    
    if (index === -1) return false;
    
    records.splice(index, 1);
    this.setTable(tableName, records);
    return true;
  }

  // Générer un ID unique
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Initialiser les données par défaut
  initializeDefaultData(): void {
    // Initialiser les tables avec des données par défaut si elles n'existent pas
    this.initializeCompanies();
    this.initializeUsers();
    this.initializeEmployees();
  }

  private initializeCompanies(): void {
    const companies = this.getTable('companies');
    if (companies.length === 0) {
      this.insert('companies', {
        id: 'default-company',
        name: 'Entreprise Demo',
        address: 'Alger, Algérie',
        phone: '+213 21 XX XX XX',
        email: '<EMAIL>',
        nif: '123456789012345',
        rc: '16/00-1234567',
        logo_url: null,
        website: null,
        industry: 'Services',
        employee_count: 10,
        settings: {},
      });
    }
  }

  private initializeUsers(): void {
    const users = this.getTable('users');
    if (users.length === 0) {
      this.insert('users', {
        id: 'offline-user',
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'Local',
        avatar_url: null,
        role: 'admin',
        permissions: ['all'],
        company_id: 'default-company',
        is_active: true,
        last_login: new Date().toISOString(),
        phone: null,
        address: null,
      });
    }
  }

  initializeEmployees(): void {
    const employees = this.getTable('employees');
    if (employees.length === 0) {
      const sampleEmployees = [
        {
          id: 'emp-001',
          employee_number: 'EMP001',
          first_name: 'Ahmed',
          last_name: 'Benali',
          email: '<EMAIL>',
          phone: '+*********** 456',
          address: 'Alger',
          position: 'Développeur',
          department: 'IT',
          hire_date: '2023-01-15',
          salary: 80000,
          status: 'active',
          company_id: 'default-company',
          social_security_number: '123456789012345',
        },
        {
          id: 'emp-002',
          employee_number: 'EMP002',
          first_name: 'Fatima',
          last_name: 'Khelifi',
          email: '<EMAIL>',
          phone: '+*********** 567',
          address: 'Oran',
          position: 'Comptable',
          department: 'Finance',
          hire_date: '2023-03-01',
          salary: 65000,
          status: 'active',
          company_id: 'default-company',
          social_security_number: '234567890123456',
        },
        {
          id: 'emp-003',
          employee_number: 'EMP003',
          first_name: 'Mohamed',
          last_name: 'Saidi',
          email: '<EMAIL>',
          phone: '+*********** 678',
          address: 'Constantine',
          position: 'Commercial',
          department: 'Ventes',
          hire_date: '2023-02-15',
          salary: 55000,
          status: 'active',
          company_id: 'default-company',
          social_security_number: '345678901234567',
        },
        {
          id: 'emp-004',
          employee_number: 'EMP004',
          first_name: 'Amina',
          last_name: 'Boudiaf',
          email: '<EMAIL>',
          phone: '+*********** 789',
          address: 'Annaba',
          position: 'Secrétaire',
          department: 'Administration',
          hire_date: '2023-04-01',
          salary: 35000,
          status: 'active',
          company_id: 'default-company',
          social_security_number: '456789012345678',
        },
        {
          id: 'emp-005',
          employee_number: 'EMP005',
          first_name: 'Karim',
          last_name: 'Meziane',
          email: '<EMAIL>',
          phone: '+*********** 890',
          address: 'Sétif',
          position: 'Technicien',
          department: 'Maintenance',
          hire_date: '2023-05-15',
          salary: 45000,
          status: 'active',
          company_id: 'default-company',
          social_security_number: '567890123456789',
        },
      ];

      sampleEmployees.forEach(employee => this.insert('employees', employee));
    }
  }

  // Vider toutes les données (pour les tests)
  clearAllData(): void {
    if (typeof window === 'undefined') return;

    const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
    keys.forEach(key => localStorage.removeItem(key));
  }

  // Méthodes spécifiques pour les employés
  async getEmployees(): Promise<any[]> {
    return this.getTable('employees');
  }

  async addEmployee(employee: any): Promise<any> {
    const employeeWithId = {
      ...employee,
      id: employee.id || this.generateId(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    return this.insert('employees', employeeWithId);
  }

  async updateEmployee(employee: any): Promise<any> {
    const updatedEmployee = {
      ...employee,
      updated_at: new Date().toISOString(),
    };
    return this.update('employees', employee.id, updatedEmployee);
  }

  async deleteEmployee(employeeId: string): Promise<void> {
    this.delete('employees', employeeId);
  }

  async getEmployee(employeeId: string): Promise<any | null> {
    return this.findById('employees', employeeId);
  }
}

// Instance globale de la base de données
export const localDatabase = new LocalDatabase();

// Fonctions de compatibilité
export async function initDatabase(): Promise<LocalDatabase> {
  localDatabase.initializeDefaultData();
  return localDatabase;
}

export async function getDatabase(): Promise<LocalDatabase> {
  return localDatabase;
}

export function closeDatabase(): void {
  // Rien à faire pour localStorage
}

// Initialiser automatiquement
if (typeof window !== 'undefined') {
  localDatabase.initializeDefaultData();
}
