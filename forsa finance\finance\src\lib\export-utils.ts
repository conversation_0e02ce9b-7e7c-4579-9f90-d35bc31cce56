import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import html2canvas from 'html2canvas';
import type { Invoice, Employee, Client, Supplier, Product } from '@/types';
import { COMPANY_CONFIG } from './config';

// PDF Export utilities
export class PDFExporter {
  private pdf: jsPDF;

  constructor(orientation: 'portrait' | 'landscape' = 'portrait') {
    this.pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format: 'a4',
    });
  }

  // Add company header
  addCompanyHeader(companyName?: string, companyAddress?: string) {
    const name = companyName || COMPANY_CONFIG.name;
    const address = companyAddress || COMPANY_CONFIG.address;

    this.pdf.setFontSize(16);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(name, 20, 20);
    
    this.pdf.setFontSize(10);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.text(address, 20, 30);
    
    // Add line separator
    this.pdf.line(20, 35, 190, 35);
  }

  // Export invoice to PDF
  exportInvoice(invoice: Invoice, client: Client) {
    this.addCompanyHeader();
    
    // Invoice header
    this.pdf.setFontSize(20);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('FACTURE', 20, 50);
    
    // Invoice details
    this.pdf.setFontSize(12);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.text(`Numéro: ${invoice.invoice_number}`, 20, 65);
    this.pdf.text(`Date: ${new Date(invoice.issue_date).toLocaleDateString('fr-FR')}`, 20, 75);
    this.pdf.text(`Échéance: ${new Date(invoice.due_date).toLocaleDateString('fr-FR')}`, 20, 85);
    
    // Client details
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('Facturé à:', 120, 65);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.text(client.name, 120, 75);
    this.pdf.text(client.address, 120, 85);
    if (client.nif) {
      this.pdf.text(`NIF: ${client.nif}`, 120, 95);
    }
    
    // Items table
    let yPosition = 110;
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('Description', 20, yPosition);
    this.pdf.text('Qté', 120, yPosition);
    this.pdf.text('Prix Unit.', 140, yPosition);
    this.pdf.text('Total', 170, yPosition);
    
    this.pdf.line(20, yPosition + 2, 190, yPosition + 2);
    yPosition += 10;
    
    this.pdf.setFont('helvetica', 'normal');
    invoice.items.forEach((item) => {
      this.pdf.text(item.description, 20, yPosition);
      this.pdf.text(item.quantity.toString(), 120, yPosition);
      this.pdf.text(`${item.unit_price.toFixed(2)} DZD`, 140, yPosition);
      this.pdf.text(`${item.line_total.toFixed(2)} DZD`, 170, yPosition);
      yPosition += 8;
    });
    
    // Totals
    yPosition += 10;
    this.pdf.line(120, yPosition, 190, yPosition);
    yPosition += 10;
    
    this.pdf.text(`Sous-total: ${invoice.subtotal.toFixed(2)} DZD`, 120, yPosition);
    yPosition += 8;
    this.pdf.text(`TVA: ${invoice.tax_amount.toFixed(2)} DZD`, 120, yPosition);
    yPosition += 8;
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(`Total: ${invoice.total_amount.toFixed(2)} DZD`, 120, yPosition);
    
    return this.pdf;
  }

  // Export payslip to PDF
  exportPayslip(payslip: any, employee: Employee) {
    this.addCompanyHeader();
    
    // Payslip header
    this.pdf.setFontSize(18);
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('BULLETIN DE PAIE', 20, 50);
    
    // Period and employee info
    this.pdf.setFontSize(12);
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.text(`Période: ${payslip.period}`, 20, 65);
    this.pdf.text(`Employé: ${employee.first_name} ${employee.last_name}`, 20, 75);
    this.pdf.text(`Poste: ${employee.position}`, 20, 85);
    this.pdf.text(`N° SS: ${employee.nss}`, 120, 75);
    
    // Earnings and deductions table
    let yPosition = 100;
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text('GAINS', 20, yPosition);
    this.pdf.text('RETENUES', 120, yPosition);
    
    yPosition += 10;
    this.pdf.setFont('helvetica', 'normal');
    
    // Add earnings and deductions
    const maxItems = Math.max(payslip.earnings?.length || 0, payslip.deductions?.length || 0);
    
    for (let i = 0; i < maxItems; i++) {
      if (payslip.earnings && payslip.earnings[i]) {
        const earning = payslip.earnings[i];
        this.pdf.text(`${earning.description}: ${earning.amount.toFixed(2)} DZD`, 20, yPosition);
      }
      
      if (payslip.deductions && payslip.deductions[i]) {
        const deduction = payslip.deductions[i];
        this.pdf.text(`${deduction.description}: ${deduction.amount.toFixed(2)} DZD`, 120, yPosition);
      }
      
      yPosition += 8;
    }
    
    // Summary
    yPosition += 10;
    this.pdf.line(20, yPosition, 190, yPosition);
    yPosition += 10;
    
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.text(`Salaire Brut: ${payslip.summary.grossSalary.toFixed(2)} DZD`, 20, yPosition);
    yPosition += 8;
    this.pdf.text(`Total Retenues: ${payslip.summary.totalDeductions.toFixed(2)} DZD`, 20, yPosition);
    yPosition += 8;
    this.pdf.text(`Net à Payer: ${payslip.summary.netSalary.toFixed(2)} DZD`, 20, yPosition);
    
    return this.pdf;
  }

  // Save PDF
  save(filename: string) {
    this.pdf.save(filename);
  }

  // Get PDF as blob
  getBlob(): Blob {
    return this.pdf.output('blob');
  }
}

// Excel Export utilities
export class ExcelExporter {
  private workbook: XLSX.WorkBook;

  constructor() {
    this.workbook = XLSX.utils.book_new();
  }

  // Export employees to Excel
  exportEmployees(employees: Employee[]) {
    const data = employees.map(emp => ({
      'Numéro Employé': emp.employee_number,
      'Prénom': emp.first_name,
      'Nom': emp.last_name,
      'Email': emp.email,
      'Téléphone': emp.phone,
      'Poste': emp.position,
      'Département': emp.department,
      'Salaire': emp.salary,
      'Date d\'embauche': emp.hire_date,
      'Statut': emp.status,
      'N° SS': emp.nss,
    }));

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(this.workbook, worksheet, 'Employés');
    return this;
  }

  // Export clients to Excel
  exportClients(clients: Client[]) {
    const data = clients.map(client => ({
      'Nom': client.name,
      'Email': client.email,
      'Téléphone': client.phone,
      'Adresse': client.address,
      'NIF': client.nif,
      'RC': client.rc,
      'Contact': client.contact_person,
      'Délai de paiement': client.payment_terms,
      'Limite de crédit': client.credit_limit,
      'Statut': client.status,
    }));

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(this.workbook, worksheet, 'Clients');
    return this;
  }

  // Export suppliers to Excel
  exportSuppliers(suppliers: Supplier[]) {
    const data = suppliers.map(supplier => ({
      'Nom': supplier.name,
      'Email': supplier.email,
      'Téléphone': supplier.phone,
      'Adresse': supplier.address,
      'NIF': supplier.nif,
      'RC': supplier.rc,
      'Contact': supplier.contact_person,
      'Délai de paiement': supplier.payment_terms,
      'Statut': supplier.status,
    }));

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(this.workbook, worksheet, 'Fournisseurs');
    return this;
  }

  // Export products to Excel
  exportProducts(products: Product[]) {
    const data = products.map(product => ({
      'SKU': product.sku,
      'Nom': product.name,
      'Description': product.description,
      'Catégorie': product.category,
      'Unité': product.unit,
      'Prix d\'achat': product.purchase_price,
      'Prix de vente': product.sale_price,
      'Taux TVA': product.tva_rate,
      'Stock': product.stock_quantity,
      'Stock min': product.min_stock_level,
      'Stock max': product.max_stock_level,
      'Statut': product.status,
    }));

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(this.workbook, worksheet, 'Produits');
    return this;
  }

  // Export invoices to Excel
  exportInvoices(invoices: Invoice[]) {
    const data = invoices.map(invoice => ({
      'Numéro': invoice.invoice_number,
      'Date': invoice.issue_date,
      'Échéance': invoice.due_date,
      'Statut': invoice.status,
      'Sous-total': invoice.subtotal,
      'TVA': invoice.tax_amount,
      'Total': invoice.total_amount,
      'Payé': invoice.paid_amount,
      'Devise': invoice.currency,
    }));

    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(this.workbook, worksheet, 'Factures');
    return this;
  }

  // Save Excel file
  save(filename: string) {
    const buffer = XLSX.write(this.workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, filename);
  }
}

// Utility functions
export const exportToPDF = {
  invoice: (invoice: Invoice, client: Client, filename?: string) => {
    const exporter = new PDFExporter();
    exporter.exportInvoice(invoice, client);
    exporter.save(filename || `facture-${invoice.invoice_number}.pdf`);
  },

  payslip: (payslip: any, employee: Employee, filename?: string) => {
    const exporter = new PDFExporter();
    exporter.exportPayslip(payslip, employee);
    exporter.save(filename || `bulletin-paie-${employee.employee_number}-${payslip.period}.pdf`);
  },

  fromElement: async (elementId: string, filename: string) => {
    const element = document.getElementById(elementId);
    if (!element) throw new Error('Element not found');

    const canvas = await html2canvas(element);
    const imgData = canvas.toDataURL('image/png');
    
    const pdf = new jsPDF();
    const imgWidth = 210;
    const pageHeight = 295;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    pdf.save(filename);
  },
};

export const exportToExcel = {
  employees: (employees: Employee[], filename?: string) => {
    const exporter = new ExcelExporter();
    exporter.exportEmployees(employees);
    exporter.save(filename || 'employes.xlsx');
  },

  clients: (clients: Client[], filename?: string) => {
    const exporter = new ExcelExporter();
    exporter.exportClients(clients);
    exporter.save(filename || 'clients.xlsx');
  },

  suppliers: (suppliers: Supplier[], filename?: string) => {
    const exporter = new ExcelExporter();
    exporter.exportSuppliers(suppliers);
    exporter.save(filename || 'fournisseurs.xlsx');
  },

  products: (products: Product[], filename?: string) => {
    const exporter = new ExcelExporter();
    exporter.exportProducts(products);
    exporter.save(filename || 'produits.xlsx');
  },

  invoices: (invoices: Invoice[], filename?: string) => {
    const exporter = new ExcelExporter();
    exporter.exportInvoices(invoices);
    exporter.save(filename || 'factures.xlsx');
  },
};
