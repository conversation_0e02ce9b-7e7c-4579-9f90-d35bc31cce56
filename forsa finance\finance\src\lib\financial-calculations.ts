// Financial calculations utilities for Algerian market
import { TAX_CONFIG } from './config';

// TVA (VAT) calculations
export class TVACalculator {
  static calculateTVA(amountHT: number, rate: number = TAX_CONFIG.tva.standard): number {
    return amountHT * rate;
  }

  static calculateAmountTTC(amountHT: number, rate: number = TAX_CONFIG.tva.standard): number {
    return amountHT + this.calculateTVA(amountHT, rate);
  }

  static calculateAmountHT(amountTTC: number, rate: number = TAX_CONFIG.tva.standard): number {
    return amountTTC / (1 + rate);
  }

  static getTVAFromTTC(amountTTC: number, rate: number = TAX_CONFIG.tva.standard): number {
    const amountHT = this.calculateAmountHT(amountTTC, rate);
    return amountTTC - amountHT;
  }

  // TVA declaration calculations
  static calculateTVADeclaration(sales: number[], purchases: number[], salesRates: number[], purchaseRates: number[]) {
    const tvaCollected = sales.reduce((total, sale, index) => {
      return total + this.calculateTVA(sale, salesRates[index] || TAX_CONFIG.tva.standard);
    }, 0);

    const tvaDeductible = purchases.reduce((total, purchase, index) => {
      return total + this.calculateTVA(purchase, purchaseRates[index] || TAX_CONFIG.tva.standard);
    }, 0);

    const tvaDue = Math.max(0, tvaCollected - tvaDeductible);
    const tvaCredit = Math.max(0, tvaDeductible - tvaCollected);

    return {
      tvaCollected,
      tvaDeductible,
      tvaDue,
      tvaCredit,
    };
  }
}

// IRG (Income Tax) calculations
export class IRGCalculator {
  static calculateIRG(grossSalary: number, children: number = 0): number {
    // Abattement forfaitaire de 30%
    const abattement = grossSalary * 0.30;
    const taxableIncome = grossSalary - abattement;

    // Abattement pour enfants à charge (300 DZD par enfant par mois)
    const childrenAbattement = children * 300 * 12; // Annual
    const finalTaxableIncome = Math.max(0, taxableIncome - childrenAbattement);

    // Apply progressive tax brackets
    let irg = 0;
    for (const bracket of TAX_CONFIG.irg.brackets) {
      if (finalTaxableIncome > bracket.min) {
        const taxableAtThisBracket = Math.min(
          finalTaxableIncome - bracket.min,
          bracket.max - bracket.min
        );
        irg += taxableAtThisBracket * bracket.rate;
      }
    }

    return Math.round(irg);
  }

  static calculateMonthlyIRG(monthlySalary: number, children: number = 0): number {
    const annualSalary = monthlySalary * 12;
    const annualIRG = this.calculateIRG(annualSalary, children);
    return Math.round(annualIRG / 12);
  }

  // IRG declaration for companies
  static calculateCompanyIRG(employees: Array<{ salary: number; children: number }>) {
    return employees.reduce((total, employee) => {
      return total + this.calculateMonthlyIRG(employee.salary, employee.children);
    }, 0);
  }
}

// CNAS (Social Security) calculations
export class CNASCalculator {
  static calculateEmployeeContribution(grossSalary: number): number {
    return grossSalary * TAX_CONFIG.cnas.employee;
  }

  static calculateEmployerContribution(grossSalary: number): number {
    return grossSalary * TAX_CONFIG.cnas.employer;
  }

  static calculateTotalContribution(grossSalary: number) {
    return {
      employee: this.calculateEmployeeContribution(grossSalary),
      employer: this.calculateEmployerContribution(grossSalary),
      total: this.calculateEmployeeContribution(grossSalary) + this.calculateEmployerContribution(grossSalary),
    };
  }
}

// CACOBATPH calculations (for construction companies)
export class CACOBATPHCalculator {
  static calculateContribution(grossSalary: number): number {
    return grossSalary * TAX_CONFIG.cacobatph;
  }
}

// Payroll calculations
export class PayrollCalculator {
  static calculateNetSalary(
    grossSalary: number,
    children: number = 0,
    allowances: number = 0,
    otherDeductions: number = 0
  ) {
    // Social security deduction
    const cnasDeduction = CNASCalculator.calculateEmployeeContribution(grossSalary);
    
    // Taxable salary for IRG (gross - CNAS)
    const taxableSalary = grossSalary - cnasDeduction;
    
    // IRG calculation
    const irgDeduction = IRGCalculator.calculateMonthlyIRG(taxableSalary, children);
    
    // Net salary calculation
    const netSalary = grossSalary + allowances - cnasDeduction - irgDeduction - otherDeductions;
    
    return {
      grossSalary,
      allowances,
      cnasDeduction,
      irgDeduction,
      otherDeductions,
      totalDeductions: cnasDeduction + irgDeduction + otherDeductions,
      netSalary: Math.max(0, netSalary),
    };
  }

  // Reverse calculation: calculate gross from desired net
  static calculateGrossFromNet(
    targetNetSalary: number,
    children: number = 0,
    allowances: number = 0,
    otherDeductions: number = 0,
    maxIterations: number = 100
  ) {
    let grossSalary = targetNetSalary * 1.4; // Initial estimate
    let iteration = 0;
    
    while (iteration < maxIterations) {
      const calculation = this.calculateNetSalary(grossSalary, children, allowances, otherDeductions);
      const difference = calculation.netSalary - targetNetSalary;
      
      if (Math.abs(difference) < 1) {
        return {
          ...calculation,
          grossSalary,
          iterations: iteration,
        };
      }
      
      // Adjust gross salary based on difference
      grossSalary -= difference * 0.8;
      iteration++;
    }
    
    throw new Error('Could not converge to target net salary');
  }
}

// Financial ratios and analysis
export class FinancialAnalyzer {
  static calculateProfitMargin(revenue: number, costs: number): number {
    if (revenue === 0) return 0;
    return ((revenue - costs) / revenue) * 100;
  }

  static calculateROI(gain: number, investment: number): number {
    if (investment === 0) return 0;
    return ((gain - investment) / investment) * 100;
  }

  static calculateCurrentRatio(currentAssets: number, currentLiabilities: number): number {
    if (currentLiabilities === 0) return 0;
    return currentAssets / currentLiabilities;
  }

  static calculateDebtToEquityRatio(totalDebt: number, totalEquity: number): number {
    if (totalEquity === 0) return 0;
    return totalDebt / totalEquity;
  }

  static calculateWorkingCapital(currentAssets: number, currentLiabilities: number): number {
    return currentAssets - currentLiabilities;
  }

  static calculateBreakEvenPoint(fixedCosts: number, pricePerUnit: number, variableCostPerUnit: number): number {
    const contributionMargin = pricePerUnit - variableCostPerUnit;
    if (contributionMargin <= 0) return 0;
    return fixedCosts / contributionMargin;
  }
}

// Currency and number formatting utilities
export class CurrencyFormatter {
  static formatDZD(amount: number, showCurrency: boolean = true): string {
    const formatted = new Intl.NumberFormat('fr-DZ', {
      style: 'decimal',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
    
    return showCurrency ? `${formatted} DZD` : formatted;
  }

  static formatPercentage(value: number, decimals: number = 2): string {
    return `${value.toFixed(decimals)}%`;
  }

  static parseAmount(value: string): number {
    // Remove currency symbols and spaces, replace comma with dot
    const cleaned = value.replace(/[^\d,.-]/g, '').replace(',', '.');
    return parseFloat(cleaned) || 0;
  }
}

// Date utilities for financial periods
export class FinancialPeriods {
  static getCurrentFiscalYear(): { start: Date; end: Date } {
    const now = new Date();
    const year = now.getFullYear();
    
    return {
      start: new Date(year, 0, 1), // January 1st
      end: new Date(year, 11, 31), // December 31st
    };
  }

  static getQuarter(date: Date): number {
    return Math.floor(date.getMonth() / 3) + 1;
  }

  static getQuarterDates(year: number, quarter: number): { start: Date; end: Date } {
    const startMonth = (quarter - 1) * 3;
    const endMonth = startMonth + 2;
    
    return {
      start: new Date(year, startMonth, 1),
      end: new Date(year, endMonth + 1, 0), // Last day of the month
    };
  }

  static getMonthDates(year: number, month: number): { start: Date; end: Date } {
    return {
      start: new Date(year, month - 1, 1),
      end: new Date(year, month, 0), // Last day of the month
    };
  }
}

// All calculators are already exported above
