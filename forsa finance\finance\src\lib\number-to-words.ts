
import { ToWords } from 'to-words';

const toWordsInstance = new ToWords({
  localeCode: 'fr-FR',
  converterOptions: {
    currency: true,
    ignoreDecimal: false,
    ignoreZeroCurrency: false,
    doNotAddOnly: false,
    currencyOptions: {
      name: 'Dinar',
      plural: 'Dinars',
      symbol: 'DZD',
      fractionalUnit: {
        name: 'Centime',
        plural: 'Centimes',
        symbol: '',
      },
    },
  },
});

export function toWords(num: number): string {
    if (num === 0) return "Zéro Dinars";
    try {
        return toWordsInstance.convert(num);
    } catch (e) {
        console.error("Failed to convert number to words", e);
        return "Erreur de conversion";
    }
}
