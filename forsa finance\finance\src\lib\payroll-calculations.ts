// Calculs de paie conformes à la réglementation algérienne
import { Employee } from '@/types';

export interface PayrollInput {
  employeeId: string;
  basicSalary: number;
  workingDays: number;
  standardWorkingDays: number;
  overtimeHours?: number;
  allowances?: {
    transport?: number;
    meal?: number;
    housing?: number;
    family?: number;
    other?: number;
  };
  absences?: {
    unpaidDays?: number;
    sickDays?: number;
  };
  period: {
    month: number;
    year: number;
  };
}

export interface PayrollOutput {
  employee: {
    id: string;
    name: string;
    position: string;
    socialSecurityNumber?: string;
  };
  period: {
    month: number;
    year: number;
    workingDays: number;
    workedDays: number;
  };
  earnings: {
    basicSalary: number;
    overtime: number;
    allowances: {
      transport: number;
      meal: number;
      housing: number;
      family: number;
      other: number;
      total: number;
    };
    grossSalary: number;
  };
  deductions: {
    cnas: {
      employee: number;
      employer: number;
      rate: number;
    };
    irg: {
      amount: number;
      rate: number;
      taxableIncome: number;
    };
    other: number;
    totalDeductions: number;
  };
  netSalary: number;
  employerCharges: {
    cnas: number;
    cacobatph: number;
    total: number;
  };
  totalCost: number;
}

// Taux de cotisations sociales en Algérie (2024)
const SOCIAL_RATES = {
  CNAS_EMPLOYEE: 0.09, // 9%
  CNAS_EMPLOYER: 0.25, // 25%
  CACOBATPH: 0.015, // 1.5%
};

// Barème IRG 2024 (en DZD) - Conforme à la réglementation algérienne
const IRG_BRACKETS = [
  { min: 0, max: 15000, rate: 0.00 },        // Exonéré jusqu'à 15 000 DZD
  { min: 15001, max: 30000, rate: 0.20 },    // 20% de 15 001 à 30 000 DZD
  { min: 30001, max: 120000, rate: 0.30 },   // 30% de 30 001 à 120 000 DZD
  { min: 120001, max: Infinity, rate: 0.35 }, // 35% au-delà de 120 000 DZD
];

// Abattement forfaitaire pour frais professionnels (10% du salaire, plafonné à 1 000 DZD/mois)
const PROFESSIONAL_EXPENSES_RATE = 0.10;
const PROFESSIONAL_EXPENSES_MAX = 1000;

// Salaire minimum national garanti (SNMG) 2024
const MINIMUM_WAGE = 20000; // DZD

export function calculatePayroll(input: PayrollInput, employee?: Employee): PayrollOutput {
  // Validation des données d'entrée
  if (input.basicSalary < MINIMUM_WAGE) {
    throw new Error(`Le salaire de base ne peut pas être inférieur au SNMG (${MINIMUM_WAGE} DZD)`);
  }

  // Calcul du salaire de base proportionnel
  const dailySalary = input.basicSalary / input.standardWorkingDays;
  const proportionalBasicSalary = dailySalary * input.workingDays;

  // Calcul des heures supplémentaires (majoration de 25% pour les premières 4h, 50% au-delà)
  let overtimeAmount = 0;
  if (input.overtimeHours) {
    const hourlyRate = input.basicSalary / (input.standardWorkingDays * 8); // 8h par jour
    const firstOvertimeHours = Math.min(input.overtimeHours, 4);
    const additionalOvertimeHours = Math.max(input.overtimeHours - 4, 0);
    
    overtimeAmount = (firstOvertimeHours * hourlyRate * 1.25) + 
                    (additionalOvertimeHours * hourlyRate * 1.50);
  }

  // Calcul des indemnités
  const allowances = {
    transport: input.allowances?.transport || 0,
    meal: input.allowances?.meal || 0,
    housing: input.allowances?.housing || 0,
    family: input.allowances?.family || 0,
    other: input.allowances?.other || 0,
    total: 0
  };
  allowances.total = Object.values(allowances).reduce((sum, val) => sum + val, 0) - allowances.total;

  // Calcul du salaire brut
  const grossSalary = proportionalBasicSalary + overtimeAmount + allowances.total;

  // Calcul des cotisations CNAS (sur salaire de base + heures sup, pas sur indemnités)
  const cnasBase = proportionalBasicSalary + overtimeAmount;
  const cnasEmployee = cnasBase * SOCIAL_RATES.CNAS_EMPLOYEE;
  const cnasEmployer = cnasBase * SOCIAL_RATES.CNAS_EMPLOYER;

  // Calcul de l'IRG (la fonction calculateIRG gère elle-même le calcul du revenu imposable)
  const irgAmount = calculateIRG(grossSalary);

  // Calcul des déductions totales
  const totalDeductions = cnasEmployee + irgAmount;

  // Calcul du salaire net
  const netSalary = grossSalary - totalDeductions;

  // Calcul des charges patronales
  const cacobatph = cnasBase * SOCIAL_RATES.CACOBATPH;
  const employerCharges = {
    cnas: cnasEmployer,
    cacobatph: cacobatph,
    total: cnasEmployer + cacobatph
  };

  // Coût total pour l'employeur
  const totalCost = grossSalary + employerCharges.total;

  return {
    employee: {
      id: input.employeeId,
      name: employee ? `${employee.first_name} ${employee.last_name}` : 'Employé',
      position: employee?.position || 'Non spécifié',
      socialSecurityNumber: employee?.employee_number
    },
    period: {
      month: input.period.month,
      year: input.period.year,
      workingDays: input.standardWorkingDays,
      workedDays: input.workingDays
    },
    earnings: {
      basicSalary: proportionalBasicSalary,
      overtime: overtimeAmount,
      allowances,
      grossSalary
    },
    deductions: {
      cnas: {
        employee: cnasEmployee,
        employer: cnasEmployer,
        rate: SOCIAL_RATES.CNAS_EMPLOYEE
      },
      irg: {
        amount: irgAmount,
        rate: getIRGEffectiveRate(grossSalary),
        taxableIncome: calculateTaxableIncome(grossSalary)
      },
      other: 0,
      totalDeductions
    },
    netSalary,
    employerCharges,
    totalCost
  };
}

function calculateIRG(grossSalary: number): number {
  // 1. Calcul de l'abattement forfaitaire pour frais professionnels
  const professionalExpenses = Math.min(grossSalary * PROFESSIONAL_EXPENSES_RATE, PROFESSIONAL_EXPENSES_MAX);

  // 2. Revenu imposable = Salaire brut - Cotisations CNAS - Abattement frais professionnels
  const cnasEmployee = grossSalary * SOCIAL_RATES.CNAS_EMPLOYEE;
  const taxableIncome = grossSalary - cnasEmployee - professionalExpenses;

  // 3. Si le revenu imposable est négatif ou nul, pas d'IRG
  if (taxableIncome <= 0) {
    return 0;
  }

  // 4. Calcul progressif de l'IRG
  let irg = 0;

  if (taxableIncome > 15000) {
    // Tranche 1: 20% sur la partie de 15 001 à 30 000 DZD
    const tranche1 = Math.min(taxableIncome - 15000, 15000); // Max 15 000 DZD dans cette tranche
    irg += tranche1 * 0.20;

    if (taxableIncome > 30000) {
      // Tranche 2: 30% sur la partie de 30 001 à 120 000 DZD
      const tranche2 = Math.min(taxableIncome - 30000, 90000); // Max 90 000 DZD dans cette tranche
      irg += tranche2 * 0.30;

      if (taxableIncome > 120000) {
        // Tranche 3: 35% sur la partie au-delà de 120 000 DZD
        const tranche3 = taxableIncome - 120000;
        irg += tranche3 * 0.35;
      }
    }
  }

  return Math.round(irg);
}

// Fonction pour calculer le revenu imposable
function calculateTaxableIncome(grossSalary: number): number {
  const professionalExpenses = Math.min(grossSalary * PROFESSIONAL_EXPENSES_RATE, PROFESSIONAL_EXPENSES_MAX);
  const cnasEmployee = grossSalary * SOCIAL_RATES.CNAS_EMPLOYEE;
  return Math.max(0, grossSalary - cnasEmployee - professionalExpenses);
}

// Fonction pour obtenir le taux effectif d'IRG
function getIRGEffectiveRate(grossSalary: number): number {
  const taxableIncome = calculateTaxableIncome(grossSalary);
  const irgAmount = calculateIRG(grossSalary);

  if (taxableIncome <= 0 || irgAmount <= 0) {
    return 0;
  }

  return Math.round((irgAmount / taxableIncome) * 10000) / 100; // Pourcentage avec 2 décimales
}

// Fonction pour obtenir le taux marginal d'IRG
function getIRGMarginalRate(taxableIncome: number): number {
  for (const bracket of IRG_BRACKETS) {
    if (taxableIncome >= bracket.min && (bracket.max === Infinity || taxableIncome <= bracket.max)) {
      return bracket.rate * 100; // Retourner en pourcentage
    }
  }
  return 0;
}

// Fonction pour calculer les congés payés
export function calculatePaidLeave(
  basicSalary: number,
  leaveDays: number,
  workingDaysPerMonth: number = 22
): number {
  const dailySalary = basicSalary / workingDaysPerMonth;
  return dailySalary * leaveDays;
}

// Fonction pour calculer l'indemnité de licenciement
export function calculateSeverancePay(
  basicSalary: number,
  yearsOfService: number,
  monthsOfService: number = 0
): number {
  const totalMonths = (yearsOfService * 12) + monthsOfService;
  
  // En Algérie: 1 mois de salaire par année de service
  const severancePay = (totalMonths / 12) * basicSalary;
  
  return Math.round(severancePay);
}

// Fonction pour valider les données de paie
export function validatePayrollInput(input: PayrollInput): string[] {
  const errors: string[] = [];

  if (input.basicSalary < MINIMUM_WAGE) {
    errors.push(`Le salaire de base doit être au moins égal au SNMG (${MINIMUM_WAGE} DZD)`);
  }

  if (input.workingDays > input.standardWorkingDays) {
    errors.push('Le nombre de jours travaillés ne peut pas dépasser le nombre de jours ouvrables');
  }

  if (input.workingDays < 0 || input.standardWorkingDays < 0) {
    errors.push('Le nombre de jours ne peut pas être négatif');
  }

  if (input.overtimeHours && input.overtimeHours < 0) {
    errors.push('Le nombre d\'heures supplémentaires ne peut pas être négatif');
  }

  if (input.period.month < 1 || input.period.month > 12) {
    errors.push('Le mois doit être entre 1 et 12');
  }

  if (input.period.year < 2020 || input.period.year > new Date().getFullYear() + 1) {
    errors.push('L\'année doit être valide');
  }

  return errors;
}

// Fonction pour obtenir le détail du calcul IRG (pour affichage)
export function getIRGCalculationDetails(grossSalary: number): {
  grossSalary: number;
  cnasEmployee: number;
  professionalExpenses: number;
  taxableIncome: number;
  irgBreakdown: Array<{
    tranche: string;
    base: number;
    rate: number;
    amount: number;
  }>;
  totalIRG: number;
} {
  const cnasEmployee = grossSalary * SOCIAL_RATES.CNAS_EMPLOYEE;
  const professionalExpenses = Math.min(grossSalary * PROFESSIONAL_EXPENSES_RATE, PROFESSIONAL_EXPENSES_MAX);
  const taxableIncome = Math.max(0, grossSalary - cnasEmployee - professionalExpenses);

  const breakdown = [];
  let totalIRG = 0;

  if (taxableIncome > 15000) {
    // Tranche 1: 20% sur la partie de 15 001 à 30 000 DZD
    const tranche1 = Math.min(taxableIncome - 15000, 15000);
    const amount1 = tranche1 * 0.20;
    breakdown.push({
      tranche: '15 001 - 30 000 DZD',
      base: tranche1,
      rate: 20,
      amount: amount1
    });
    totalIRG += amount1;

    if (taxableIncome > 30000) {
      // Tranche 2: 30% sur la partie de 30 001 à 120 000 DZD
      const tranche2 = Math.min(taxableIncome - 30000, 90000);
      const amount2 = tranche2 * 0.30;
      breakdown.push({
        tranche: '30 001 - 120 000 DZD',
        base: tranche2,
        rate: 30,
        amount: amount2
      });
      totalIRG += amount2;

      if (taxableIncome > 120000) {
        // Tranche 3: 35% sur la partie au-delà de 120 000 DZD
        const tranche3 = taxableIncome - 120000;
        const amount3 = tranche3 * 0.35;
        breakdown.push({
          tranche: 'Au-delà de 120 000 DZD',
          base: tranche3,
          rate: 35,
          amount: amount3
        });
        totalIRG += amount3;
      }
    }
  }

  return {
    grossSalary,
    cnasEmployee: Math.round(cnasEmployee),
    professionalExpenses: Math.round(professionalExpenses),
    taxableIncome: Math.round(taxableIncome),
    irgBreakdown: breakdown.map(item => ({
      ...item,
      base: Math.round(item.base),
      amount: Math.round(item.amount)
    })),
    totalIRG: Math.round(totalIRG)
  };
}
