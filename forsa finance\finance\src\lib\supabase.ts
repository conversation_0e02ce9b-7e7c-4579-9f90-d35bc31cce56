// Fichier de compatibilité pour remplacer Supabase par SQLite
import { databaseService } from './database-service';

// Export pour compatibilité avec l'ancien code Supabase
export const supabase = databaseService;
export const supabaseAdmin = databaseService;

// Types de compatibilité
export interface SupabaseResponse<T> {
  data: T | null;
  error: Error | null;
}

// Wrapper pour maintenir la compatibilité avec l'API Supabase
export class SupabaseCompatibilityLayer {
  from(table: string) {
    return {
      select: async (columns?: string) => {
        try {
          const data = await databaseService.findAll(table);
          return { data, error: null };
        } catch (error) {
          return { data: null, error: error as Error };
        }
      },
      insert: async (values: any) => {
        try {
          const data = await databaseService.create(table, values);
          return { data, error: null };
        } catch (error) {
          return { data: null, error: error as Error };
        }
      },
      update: async (values: any) => {
        return {
          eq: async (column: string, value: any) => {
            try {
              // Pour la compatibilité, on assume que la première entrée avec cette valeur sera mise à jour
              const existing = await databaseService.findAll(table, { [column]: value });
              if (existing.length > 0) {
                const data = await databaseService.update(table, existing[0].id, values);
                return { data, error: null };
              }
              return { data: null, error: new Error('Record not found') };
            } catch (error) {
              return { data: null, error: error as Error };
            }
          }
        };
      },
      delete: () => {
        return {
          eq: async (column: string, value: any) => {
            try {
              const existing = await databaseService.findAll(table, { [column]: value });
              if (existing.length > 0) {
                const success = await databaseService.delete(table, existing[0].id);
                return { data: success ? existing[0] : null, error: null };
              }
              return { data: null, error: new Error('Record not found') };
            } catch (error) {
              return { data: null, error: error as Error };
            }
          }
        };
      }
    };
  }

  // Méthodes d'authentification simulées (pour la compatibilité)
  auth = {
    getUser: async () => {
      // Retourner un utilisateur par défaut pour le mode hors ligne
      return {
        data: {
          user: {
            id: 'offline-user',
            email: '<EMAIL>',
            user_metadata: {
              first_name: 'Admin',
              last_name: 'Local'
            }
          }
        },
        error: null
      };
    },
    signInWithPassword: async (credentials: { email: string; password: string }) => {
      // Authentification locale simple
      if (credentials.email === '<EMAIL>' && credentials.password === 'admin') {
        return {
          data: {
            user: {
              id: 'offline-user',
              email: '<EMAIL>',
              user_metadata: {
                first_name: 'Admin',
                last_name: 'Local'
              }
            },
            session: {
              access_token: 'offline-token',
              refresh_token: 'offline-refresh'
            }
          },
          error: null
        };
      }
      return {
        data: { user: null, session: null },
        error: new Error('Invalid credentials')
      };
    },
    signOut: async () => {
      return { error: null };
    },
    onAuthStateChange: (callback: (event: string, session: any) => void) => {
      // Simuler un état d'authentification
      setTimeout(() => {
        callback('SIGNED_IN', {
          user: {
            id: 'offline-user',
            email: '<EMAIL>'
          }
        });
      }, 100);
      
      return {
        data: { subscription: { unsubscribe: () => {} } }
      };
    }
  };
}

// Instance de compatibilité
const supabaseCompat = new SupabaseCompatibilityLayer();

// Exports par défaut
export default supabaseCompat;
export { supabaseCompat as createClient };

// Export des types pour la compatibilité
export type Database = any;
export type Tables = any;
