// Core types for Forsa Finance application

import { UserRole, Permission } from '@/lib/config';

// Base types
export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

// User and Authentication types
export interface User extends BaseEntity {
  email: string;
  first_name: string;
  last_name: string;
  avatar_url?: string;
  role: UserRole;
  permissions: Permission[];
  company_id: string;
  is_active: boolean;
  last_login?: string;
  phone?: string;
  address?: string;
}

export interface Company extends BaseEntity {
  name: string;
  address: string;
  phone: string;
  email: string;
  nif: string;
  rc: string;
  logo_url?: string;
  website?: string;
  industry?: string;
  employee_count?: number;
  settings: CompanySettings;
}

export interface CompanySettings {
  currency: string;
  timezone: string;
  date_format: string;
  language: 'fr' | 'ar' | 'en';
  fiscal_year_start: string;
  tax_settings: TaxSettings;
  payroll_settings: PayrollSettings;
}

export interface TaxSettings {
  tva_number?: string;
  tva_rates: {
    standard: number;
    reduced: number;
  };
  irg_settings: {
    threshold: number;
    brackets: Array<{
      min: number;
      max: number;
      rate: number;
    }>;
  };
}

export interface PayrollSettings {
  cnas_rates: {
    employee: number;
    employer: number;
  };
  cacobatph_rate: number;
  minimum_wage: number;
  working_hours_per_month: number;
}

// Employee types
export interface Employee extends BaseEntity {
  employee_number: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  birth_date?: string;
  hire_date: string;
  termination_date?: string;
  position: string;
  department: string;
  salary: number;
  salary_type: 'monthly' | 'hourly' | 'daily';
  nss: string;
  marital_status: 'single' | 'married' | 'divorced' | 'widowed';
  children_count: number;
  bank_account?: string;
  status: 'active' | 'inactive' | 'terminated';
  avatar_url?: string;
  company_id: string;
}

// Client and Supplier types
export interface Client extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  address: string;
  nif?: string;
  rc?: string;
  contact_person?: string;
  payment_terms: number; // days
  credit_limit?: number;
  status: 'active' | 'inactive';
  company_id: string;
}

export interface Supplier extends BaseEntity {
  name: string;
  email: string;
  phone: string;
  address: string;
  nif?: string;
  rc?: string;
  contact_person?: string;
  payment_terms: number; // days
  status: 'active' | 'inactive';
  company_id: string;
}

// Product and Stock types
export interface Product extends BaseEntity {
  sku: string;
  name: string;
  description?: string;
  category: string;
  unit: string;
  purchase_price: number;
  sale_price: number;
  tva_rate: number;
  stock_quantity: number;
  min_stock_level: number;
  max_stock_level: number;
  status: 'active' | 'inactive';
  image_url?: string;
  company_id: string;
}

export interface StockMovement extends BaseEntity {
  product_id: string;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  unit_price?: number;
  reference?: string;
  notes?: string;
  user_id: string;
  company_id: string;
}

// Invoice types
export interface Invoice extends BaseEntity {
  invoice_number: string;
  client_id: string;
  issue_date: string;
  due_date: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  paid_amount: number;
  currency: string;
  notes?: string;
  terms?: string;
  company_id: string;
  items: InvoiceItem[];
}

export interface InvoiceItem extends BaseEntity {
  invoice_id: string;
  product_id?: string;
  description: string;
  quantity: number;
  unit_price: number;
  tax_rate: number;
  line_total: number;
}

// Accounting types
export interface Account extends BaseEntity {
  code: string;
  name: string;
  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense';
  parent_id?: string;
  is_active: boolean;
  company_id: string;
}

export interface JournalEntry extends BaseEntity {
  entry_number: string;
  date: string;
  reference?: string;
  description: string;
  total_debit: number;
  total_credit: number;
  status: 'draft' | 'posted' | 'reversed';
  user_id: string;
  company_id: string;
  lines: JournalEntryLine[];
}

export interface JournalEntryLine extends BaseEntity {
  journal_entry_id: string;
  account_id: string;
  description: string;
  debit: number;
  credit: number;
  line_number: number;
}

// Payroll types
export interface Payslip extends BaseEntity {
  employee_id: string;
  period_start: string;
  period_end: string;
  gross_salary: number;
  net_salary: number;
  total_deductions: number;
  total_allowances: number;
  status: 'draft' | 'approved' | 'paid';
  payment_date?: string;
  company_id: string;
  earnings: PayslipItem[];
  deductions: PayslipItem[];
}

export interface PayslipItem {
  id: string;
  payslip_id: string;
  type: 'earning' | 'deduction';
  code: string;
  description: string;
  amount: number;
  is_taxable: boolean;
}

// Tax Declaration types
export interface TaxDeclaration extends BaseEntity {
  type: 'tva' | 'irg' | 'g50' | 'das';
  period_start: string;
  period_end: string;
  status: 'draft' | 'submitted' | 'approved';
  total_amount: number;
  submission_date?: string;
  company_id: string;
  data: Record<string, any>;
}

// Bank and Treasury types
export interface BankAccount extends BaseEntity {
  name: string;
  bank_name: string;
  account_number: string;
  rib: string;
  currency: string;
  balance: number;
  is_active: boolean;
  company_id: string;
}

export interface BankTransaction extends BaseEntity {
  bank_account_id: string;
  date: string;
  description: string;
  amount: number;
  type: 'debit' | 'credit';
  reference?: string;
  category?: string;
  is_reconciled: boolean;
  company_id: string;
}

// Settings and Configuration types
export interface Setting extends BaseEntity {
  key: string;
  value: string;
  type: 'string' | 'number' | 'boolean' | 'json';
  description?: string;
  company_id: string;
}

// Audit and Logging types
export interface AuditLog extends BaseEntity {
  user_id: string;
  action: string;
  table_name: string;
  record_id: string;
  old_values?: Record<string, any>;
  new_values?: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  company_id: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form types
export interface FormState {
  isLoading: boolean;
  error?: string;
  success?: boolean;
}

// Dashboard types
export interface DashboardStats {
  revenue: {
    current: number;
    previous: number;
    change: number;
  };
  expenses: {
    current: number;
    previous: number;
    change: number;
  };
  profit: {
    current: number;
    previous: number;
    change: number;
  };
  cash: {
    current: number;
    previous: number;
    change: number;
  };
}

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string;
    borderColor?: string;
  }>;
}

// AI Analysis types
export interface AnomalyDetectionResult {
  summary: string;
  anomalies: Array<{
    description: string;
    severity: 'low' | 'medium' | 'high';
    potentialCause: string;
    recommendation?: string;
  }>;
  confidence: number;
}

export interface OptimizationSuggestion {
  title: string;
  description: string;
  category: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  estimatedSavings?: number;
  implementation: string[];
}

// Export all types
export type * from './index';
