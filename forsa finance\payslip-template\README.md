# 📄 Modèle de Fiche de Paie Algérienne - Version Hors Ligne

## 🎯 Description

Modèle complet de fiche de paie conforme à la **réglementation algérienne 2024-2025**, conçu pour fonctionner **100% hors ligne** dans un logiciel de gestion de paie pour entreprises algériennes.

## ✅ Conformité Réglementaire

### 📋 Barème IRG 2024-2025
- **0 à 15 000 DZD** : Exonéré (0%)
- **15 001 à 30 000 DZD** : 20%
- **30 001 à 120 000 DZD** : 30%
- **120 001 DZD et plus** : 35%

### 🏛️ Cotisations Sociales
- **CNAS Employé** : 9% du salaire brut
- **CNAS Employeur** : 25% du salaire brut
- **CACOBATPH** : 1.5% du salaire brut
- **CASNOS** : 15% (pour non-salariés)

### 💰 Abattements et Primes
- **Abattement frais professionnels** : 10% du salaire (plafonné à 1 000 DZD/mois)
- **Prime d'ancienneté** : Selon convention collective (0% à 25%)
- **Heures supplémentaires** : +25% (4 premières heures), +50% (au-delà)

## 🚀 Fonctionnalités

### 📊 Calculs Automatiques
- ✅ Calcul IRG progressif conforme
- ✅ Cotisations sociales CNAS/CACOBATPH
- ✅ Primes d'ancienneté automatiques
- ✅ Heures supplémentaires avec majorations
- ✅ Abattements fiscaux réglementaires

### 📄 Fiche de Paie Professionnelle
- ✅ En-tête entreprise complet (NIF, RC)
- ✅ Informations employé détaillées
- ✅ Tableau gains/retenues structuré
- ✅ Détail calcul IRG transparent
- ✅ Charges patronales affichées
- ✅ Espaces signature et cachet
- ✅ Code QR de vérification unique

### 🖨️ Impression et Export
- ✅ Optimisé pour impression A4
- ✅ Sauvegarde PDF intégrée
- ✅ Version écran et impression différenciées
- ✅ Responsive design mobile

### 🌐 Support Multilingue
- ✅ Français (par défaut)
- ✅ Arabe (RTL support)
- ✅ Anglais
- ✅ Structure extensible

## 📁 Structure des Fichiers

```
payslip-template/
├── index.html              # Interface principale
├── styles.css              # Styles CSS complets
├── payslip-calculator.js   # Moteur de calcul algérien
├── payslip-generator.js    # Interface et manipulation DOM
├── README.md               # Documentation
└── examples/               # Exemples d'intégration
    ├── electron-app/       # Intégration Electron.js
    ├── python-tkinter/     # Intégration Python Tkinter
    └── standalone/         # Version autonome
```

## 🔧 Installation et Utilisation

### 1. Version Autonome (HTML/CSS/JS)
```bash
# Télécharger les fichiers
# Ouvrir index.html dans un navigateur
# Aucune dépendance externe requise
```

### 2. Intégration Electron.js
```javascript
// main.js
const { app, BrowserWindow } = require('electron');

function createWindow() {
    const win = new BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });
    
    win.loadFile('payslip-template/index.html');
}

app.whenReady().then(createWindow);
```

### 3. Intégration Python Tkinter
```python
import tkinter as tk
from tkinter import ttk
import webview

class PayslipApp:
    def __init__(self):
        self.window = webview.create_window(
            'Fiche de Paie Algérienne',
            'payslip-template/index.html',
            width=1200,
            height=800
        )
    
    def start(self):
        webview.start()

if __name__ == '__main__':
    app = PayslipApp()
    app.start()
```

## 🎨 Personnalisation

### Couleurs et Thème
```css
/* Modifier les variables CSS dans styles.css */
:root {
    --primary-color: #1e40af;      /* Couleur principale */
    --secondary-color: #3b82f6;    /* Couleur secondaire */
    --success-color: #059669;      /* Couleur succès */
    --danger-color: #dc2626;       /* Couleur danger */
}
```

### Logo Entreprise
```javascript
// Remplacer le logo par défaut
document.getElementById('companyLogo').src = 'votre-logo.png';
```

### Données Entreprise
```javascript
// Modifier les données par défaut dans payslip-generator.js
const defaultCompany = {
    name: 'VOTRE ENTREPRISE SARL',
    address: 'Votre adresse complète',
    nif: 'Votre NIF',
    rc: 'Votre RC',
    phone: 'Votre téléphone',
    email: 'Votre email'
};
```

## 📊 Exemple d'Utilisation

### Calcul d'une Fiche de Paie
```javascript
// Initialiser le calculateur
const calculator = new AlgerianPayrollCalculator();

// Données employé
const employee = {
    id: 'EMP-001',
    name: 'BENALI Ahmed',
    position: 'Développeur Senior',
    yearsOfService: 5
};

// Données salariales
const salary = {
    baseSalary: 80000,
    transportAllowance: 3000,
    mealAllowance: 2000,
    familyAllowance: 1500,
    overtimeHours: 8,
    period: { month: 1, year: 2025, workingDays: 22, workedDays: 22 }
};

// Calcul
const payslip = calculator.calculatePayslip(employee, salary);
console.log('Net à payer:', payslip.netSalary, 'DZD');
```

### Résultat Exemple (80 000 DZD)
```
Gains:
- Salaire de base: 80 000 DZD
- Indemnité transport: 3 000 DZD
- Indemnité panier: 2 000 DZD
- Allocation familiale: 1 500 DZD
- Prime ancienneté (5 ans): 4 000 DZD
- Heures sup (8h): 3 200 DZD
Total Gains: 93 700 DZD

Retenues:
- CNAS (9%): 8 433 DZD
- IRG (progressif): 19 540 DZD
Total Retenues: 27 973 DZD

Net à Payer: 65 727 DZD

Charges Patronales:
- CNAS Employeur (25%): 23 425 DZD
- CACOBATPH (1.5%): 1 406 DZD
Coût Total Employeur: 118 531 DZD
```

## 🔒 Sécurité et Conformité

### ✅ Conformité Légale
- Barème IRG officiel 2024-2025
- Taux CNAS/CACOBATPH réglementaires
- Abattements fiscaux conformes
- Mentions légales obligatoires

### 🛡️ Sécurité des Données
- Fonctionnement 100% hors ligne
- Aucune transmission de données
- Stockage local sécurisé
- Pas de dépendances externes

### 📋 Traçabilité
- Code QR unique par fiche
- Horodatage automatique
- Numérotation séquentielle
- Historique des calculs

## 🆘 Support et Maintenance

### Mise à Jour des Barèmes
```javascript
// Modifier ALGERIA_TAX_CONFIG dans payslip-calculator.js
const ALGERIA_TAX_CONFIG = {
    IRG_BRACKETS: [
        { min: 0, max: 15000, rate: 0.00 },
        { min: 15001, max: 30000, rate: 0.20 },
        // Ajouter/modifier selon nouvelles réglementations
    ]
};
```

### Dépannage Courant
- **Calculs incorrects** : Vérifier les taux dans la configuration
- **Impression problématique** : Utiliser Chrome/Edge pour meilleur support
- **Responsive issues** : Tester sur différentes résolutions

## 📞 Contact et Support

Pour questions techniques ou mises à jour réglementaires :
- 📧 Email : <EMAIL>
- 📱 Téléphone : +213 XX XX XX XX
- 🌐 Site web : www.forsa-finance.dz

## 📄 Licence

Ce modèle est fourni sous licence MIT pour usage commercial et personnel.

---

**© 2025 Forsa Finance - Logiciel de Gestion Algérien**
