/**
 * Exemple d'intégration Electron.js pour le modèle de fiche de paie
 * Logiciel de bureau hors ligne pour Windows/Mac/Linux
 */

const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

class PayslipElectronApp {
    constructor() {
        this.mainWindow = null;
        this.isDev = process.env.NODE_ENV === 'development';
    }

    /**
     * Crée la fenêtre principale
     */
    createMainWindow() {
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            icon: path.join(__dirname, '../assets/icon.png'),
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true
            },
            titleBarStyle: 'default',
            show: false
        });

        // Charger l'interface de fiche de paie
        this.mainWindow.loadFile(path.join(__dirname, '../index.html'));

        // Afficher la fenêtre une fois prête
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            if (this.isDev) {
                this.mainWindow.webContents.openDevTools();
            }
        });

        // Gérer la fermeture
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // Créer le menu
        this.createMenu();
    }

    /**
     * Crée le menu de l'application
     */
    createMenu() {
        const template = [
            {
                label: 'Fichier',
                submenu: [
                    {
                        label: 'Nouvelle fiche de paie',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => this.newPayslip()
                    },
                    {
                        label: 'Ouvrir',
                        accelerator: 'CmdOrCtrl+O',
                        click: () => this.openPayslip()
                    },
                    {
                        label: 'Sauvegarder',
                        accelerator: 'CmdOrCtrl+S',
                        click: () => this.savePayslip()
                    },
                    { type: 'separator' },
                    {
                        label: 'Imprimer',
                        accelerator: 'CmdOrCtrl+P',
                        click: () => this.printPayslip()
                    },
                    {
                        label: 'Exporter PDF',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => this.exportToPDF()
                    },
                    { type: 'separator' },
                    {
                        label: 'Quitter',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => app.quit()
                    }
                ]
            },
            {
                label: 'Édition',
                submenu: [
                    {
                        label: 'Modifier employé',
                        click: () => this.editEmployee()
                    },
                    {
                        label: 'Modifier entreprise',
                        click: () => this.editCompany()
                    },
                    { type: 'separator' },
                    {
                        label: 'Paramètres',
                        click: () => this.openSettings()
                    }
                ]
            },
            {
                label: 'Outils',
                submenu: [
                    {
                        label: 'Calculateur IRG',
                        click: () => this.openIRGCalculator()
                    },
                    {
                        label: 'Simulateur de paie',
                        click: () => this.openPayrollSimulator()
                    },
                    { type: 'separator' },
                    {
                        label: 'Mise à jour barèmes',
                        click: () => this.updateTaxBrackets()
                    }
                ]
            },
            {
                label: 'Aide',
                submenu: [
                    {
                        label: 'Documentation',
                        click: () => this.openDocumentation()
                    },
                    {
                        label: 'À propos',
                        click: () => this.showAbout()
                    }
                ]
            }
        ];

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    /**
     * Actions du menu
     */
    newPayslip() {
        this.mainWindow.webContents.send('action', 'new-payslip');
    }

    async openPayslip() {
        const result = await dialog.showOpenDialog(this.mainWindow, {
            title: 'Ouvrir une fiche de paie',
            filters: [
                { name: 'Fichiers JSON', extensions: ['json'] },
                { name: 'Tous les fichiers', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (!result.canceled && result.filePaths.length > 0) {
            try {
                const data = fs.readFileSync(result.filePaths[0], 'utf8');
                const payslipData = JSON.parse(data);
                this.mainWindow.webContents.send('action', 'load-payslip', payslipData);
            } catch (error) {
                dialog.showErrorBox('Erreur', 'Impossible d\'ouvrir le fichier: ' + error.message);
            }
        }
    }

    async savePayslip() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'Sauvegarder la fiche de paie',
            defaultPath: `fiche-paie-${new Date().toISOString().slice(0, 10)}.json`,
            filters: [
                { name: 'Fichiers JSON', extensions: ['json'] }
            ]
        });

        if (!result.canceled) {
            this.mainWindow.webContents.send('action', 'save-payslip', result.filePath);
        }
    }

    printPayslip() {
        this.mainWindow.webContents.print({
            silent: false,
            printBackground: true,
            margins: {
                marginType: 'minimum'
            }
        });
    }

    async exportToPDF() {
        const result = await dialog.showSaveDialog(this.mainWindow, {
            title: 'Exporter en PDF',
            defaultPath: `fiche-paie-${new Date().toISOString().slice(0, 10)}.pdf`,
            filters: [
                { name: 'Fichiers PDF', extensions: ['pdf'] }
            ]
        });

        if (!result.canceled) {
            try {
                const data = await this.mainWindow.webContents.printToPDF({
                    printBackground: true,
                    margins: {
                        marginType: 'minimum'
                    }
                });
                
                fs.writeFileSync(result.filePath, data);
                dialog.showMessageBox(this.mainWindow, {
                    type: 'info',
                    title: 'Export réussi',
                    message: 'La fiche de paie a été exportée en PDF avec succès.'
                });
            } catch (error) {
                dialog.showErrorBox('Erreur d\'export', error.message);
            }
        }
    }

    editEmployee() {
        this.mainWindow.webContents.send('action', 'edit-employee');
    }

    editCompany() {
        this.mainWindow.webContents.send('action', 'edit-company');
    }

    openSettings() {
        this.mainWindow.webContents.send('action', 'open-settings');
    }

    openIRGCalculator() {
        this.mainWindow.webContents.send('action', 'open-irg-calculator');
    }

    openPayrollSimulator() {
        this.mainWindow.webContents.send('action', 'open-payroll-simulator');
    }

    updateTaxBrackets() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'Mise à jour des barèmes',
            message: 'Fonctionnalité de mise à jour automatique des barèmes fiscaux à implémenter.',
            detail: 'Cette fonction permettra de mettre à jour automatiquement les taux IRG et cotisations sociales selon les dernières réglementations.'
        });
    }

    openDocumentation() {
        this.mainWindow.webContents.send('action', 'open-documentation');
    }

    showAbout() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'À propos',
            message: 'Logiciel de Fiche de Paie Algérienne',
            detail: `Version: 1.0.0
Conforme à la réglementation algérienne 2024-2025
Développé par Forsa Finance

© 2025 Tous droits réservés`
        });
    }

    /**
     * Initialise l'application
     */
    initialize() {
        // Événement de démarrage
        app.whenReady().then(() => {
            this.createMainWindow();

            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // Fermeture de l'application
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // Gestion des événements IPC
        this.setupIPC();
    }

    /**
     * Configure la communication IPC
     */
    setupIPC() {
        // Recevoir des données du renderer
        ipcMain.handle('get-app-version', () => {
            return app.getVersion();
        });

        ipcMain.handle('save-payslip-data', async (event, data, filePath) => {
            try {
                fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
                return { success: true };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        ipcMain.handle('load-payslip-data', async (event, filePath) => {
            try {
                const data = fs.readFileSync(filePath, 'utf8');
                return { success: true, data: JSON.parse(data) };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });
    }
}

// Démarrage de l'application
const payslipApp = new PayslipElectronApp();
payslipApp.initialize();

module.exports = PayslipElectronApp;
