"""
Exemple d'intégration Python pour le modèle de fiche de paie algérienne
Utilise webview pour créer une application de bureau hors ligne
"""

import webview
import json
import os
import sys
from datetime import datetime
from pathlib import Path

class AlgerianPayslipApp:
    """
    Application de bureau pour la gestion des fiches de paie algériennes
    """
    
    def __init__(self):
        self.window = None
        self.current_payslip = None
        self.app_data_dir = self.get_app_data_dir()
        self.ensure_data_directory()
    
    def get_app_data_dir(self):
        """Obtient le répertoire de données de l'application"""
        if sys.platform == "win32":
            return Path(os.environ['APPDATA']) / 'AlgerianPayslip'
        elif sys.platform == "darwin":
            return Path.home() / 'Library' / 'Application Support' / 'AlgerianPayslip'
        else:
            return Path.home() / '.config' / 'AlgerianPayslip'
    
    def ensure_data_directory(self):
        """Crée le répertoire de données s'il n'existe pas"""
        self.app_data_dir.mkdir(parents=True, exist_ok=True)
        
        # Créer les sous-répertoires
        (self.app_data_dir / 'payslips').mkdir(exist_ok=True)
        (self.app_data_dir / 'employees').mkdir(exist_ok=True)
        (self.app_data_dir / 'companies').mkdir(exist_ok=True)
        (self.app_data_dir / 'exports').mkdir(exist_ok=True)
    
    def create_window(self):
        """Crée la fenêtre principale de l'application"""
        # Chemin vers le template HTML
        template_path = Path(__file__).parent.parent / 'index.html'
        
        self.window = webview.create_window(
            title='Fiche de Paie Algérienne - Conforme 2024-2025',
            url=str(template_path),
            width=1400,
            height=900,
            min_size=(1200, 800),
            resizable=True,
            fullscreen=False,
            minimized=False,
            on_top=False,
            shadow=True,
            js_api=self  # Expose les méthodes Python au JavaScript
        )
        
        return self.window
    
    # API JavaScript - Méthodes exposées au frontend
    
    def save_payslip(self, payslip_data):
        """
        Sauvegarde une fiche de paie
        """
        try:
            # Générer un nom de fichier unique
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            employee_id = payslip_data.get('employee', {}).get('id', 'unknown')
            filename = f"payslip_{employee_id}_{timestamp}.json"
            
            # Chemin de sauvegarde
            file_path = self.app_data_dir / 'payslips' / filename
            
            # Ajouter des métadonnées
            payslip_data['metadata'] = {
                'created_at': datetime.now().isoformat(),
                'version': '1.0.0',
                'app': 'AlgerianPayslip'
            }
            
            # Sauvegarder
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(payslip_data, f, indent=2, ensure_ascii=False)
            
            return {
                'success': True,
                'message': f'Fiche de paie sauvegardée: {filename}',
                'file_path': str(file_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de la sauvegarde: {str(e)}'
            }
    
    def load_payslip(self, file_path=None):
        """
        Charge une fiche de paie depuis un fichier
        """
        try:
            if not file_path:
                # Ouvrir un dialogue de sélection de fichier
                # Note: webview ne supporte pas nativement les dialogues de fichier
                # Il faudrait utiliser tkinter.filedialog ou une autre solution
                return {
                    'success': False,
                    'message': 'Sélection de fichier non implémentée dans cette version'
                }
            
            with open(file_path, 'r', encoding='utf-8') as f:
                payslip_data = json.load(f)
            
            self.current_payslip = payslip_data
            
            return {
                'success': True,
                'data': payslip_data,
                'message': 'Fiche de paie chargée avec succès'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors du chargement: {str(e)}'
            }
    
    def get_saved_payslips(self):
        """
        Retourne la liste des fiches de paie sauvegardées
        """
        try:
            payslips_dir = self.app_data_dir / 'payslips'
            payslips = []
            
            for file_path in payslips_dir.glob('*.json'):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    payslips.append({
                        'filename': file_path.name,
                        'path': str(file_path),
                        'employee_name': data.get('employee', {}).get('name', 'Inconnu'),
                        'period': data.get('period', {}),
                        'created_at': data.get('metadata', {}).get('created_at', ''),
                        'net_salary': data.get('netSalary', 0)
                    })
                except:
                    continue  # Ignorer les fichiers corrompus
            
            # Trier par date de création (plus récent en premier)
            payslips.sort(key=lambda x: x['created_at'], reverse=True)
            
            return {
                'success': True,
                'payslips': payslips
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de la récupération: {str(e)}'
            }
    
    def export_to_pdf(self, payslip_data):
        """
        Exporte une fiche de paie en PDF
        Note: Nécessite une bibliothèque comme reportlab ou weasyprint
        """
        try:
            # Générer un nom de fichier
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            employee_id = payslip_data.get('employee', {}).get('id', 'unknown')
            filename = f"payslip_{employee_id}_{timestamp}.pdf"
            
            # Chemin d'export
            export_path = self.app_data_dir / 'exports' / filename
            
            # Ici, vous pouvez implémenter la génération PDF
            # Exemple avec reportlab ou conversion HTML vers PDF
            
            return {
                'success': True,
                'message': f'PDF exporté: {filename}',
                'file_path': str(export_path)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de l\'export PDF: {str(e)}'
            }
    
    def get_app_settings(self):
        """
        Retourne les paramètres de l'application
        """
        settings_file = self.app_data_dir / 'settings.json'
        
        default_settings = {
            'language': 'fr',
            'currency_format': 'fr-DZ',
            'default_company': {
                'name': 'ENTREPRISE DEMO SARL',
                'address': '123 Rue de l\'Indépendance, Alger 16000, Algérie',
                'nif': '123456789012345',
                'rc': '16/00-1234567',
                'phone': '+213 21 XX XX XX',
                'email': '<EMAIL>'
            },
            'tax_config': {
                'minimum_wage': 20000,
                'cnas_employee_rate': 0.09,
                'cnas_employer_rate': 0.25,
                'cacobatph_rate': 0.015
            }
        }
        
        try:
            if settings_file.exists():
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                # Fusionner avec les paramètres par défaut
                default_settings.update(settings)
            
            return {
                'success': True,
                'settings': default_settings
            }
            
        except Exception as e:
            return {
                'success': True,
                'settings': default_settings,
                'message': f'Paramètres par défaut utilisés: {str(e)}'
            }
    
    def save_app_settings(self, settings):
        """
        Sauvegarde les paramètres de l'application
        """
        try:
            settings_file = self.app_data_dir / 'settings.json'
            
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)
            
            return {
                'success': True,
                'message': 'Paramètres sauvegardés avec succès'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'Erreur lors de la sauvegarde: {str(e)}'
            }
    
    def get_system_info(self):
        """
        Retourne les informations système
        """
        return {
            'platform': sys.platform,
            'python_version': sys.version,
            'app_data_dir': str(self.app_data_dir),
            'current_time': datetime.now().isoformat()
        }
    
    def start(self):
        """
        Démarre l'application
        """
        try:
            # Créer la fenêtre
            window = self.create_window()
            
            # Démarrer l'application webview
            webview.start(
                debug=False,  # Mettre à True pour le développement
                http_server=True  # Serveur HTTP local pour les ressources
            )
            
        except Exception as e:
            print(f"Erreur lors du démarrage de l'application: {e}")
            sys.exit(1)

def main():
    """
    Point d'entrée principal de l'application
    """
    print("Démarrage de l'application Fiche de Paie Algérienne...")
    
    # Créer et démarrer l'application
    app = AlgerianPayslipApp()
    app.start()

if __name__ == '__main__':
    main()
