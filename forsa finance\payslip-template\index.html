<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fiche de Paie - Logiciel de Gestion Algérien</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="payslip-container" id="payslip">
        <!-- En-tête Entreprise -->
        <header class="company-header">
            <div class="company-info">
                <div class="company-logo">
                    <img id="companyLogo" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiByeD0iOCIgZmlsbD0iIzE5NEJGQiIvPgo8dGV4dCB4PSIzMCIgeT0iMzgiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5FPC90ZXh0Pgo8L3N2Zz4K" alt="Logo">
                </div>
                <div class="company-details">
                    <h1 id="companyName">ENTREPRISE DEMO SARL</h1>
                    <p id="companyAddress">123 Rue de l'Indépendance, Alger 16000, Algérie</p>
                    <div class="company-ids">
                        <span>NIF: <span id="companyNIF">123456789012345</span></span>
                        <span>RC: <span id="companyRC">16/00-1234567</span></span>
                    </div>
                </div>
            </div>
            <div class="payslip-title">
                <h2>BULLETIN DE PAIE</h2>
                <p class="period" id="payPeriod">Janvier 2025</p>
                <p class="payslip-number">N° <span id="payslipNumber">2025-01-001</span></p>
            </div>
        </header>

        <!-- Informations Employé -->
        <section class="employee-section">
            <h3>INFORMATIONS EMPLOYÉ</h3>
            <div class="employee-grid">
                <div class="employee-info">
                    <div class="info-row">
                        <label>Nom et Prénom:</label>
                        <span id="employeeName">BENALI Ahmed</span>
                    </div>
                    <div class="info-row">
                        <label>Matricule:</label>
                        <span id="employeeId">EMP-001</span>
                    </div>
                    <div class="info-row">
                        <label>Fonction:</label>
                        <span id="employeePosition">Développeur Senior</span>
                    </div>
                </div>
                <div class="employee-info">
                    <div class="info-row">
                        <label>Date d'embauche:</label>
                        <span id="hireDate">01/01/2020</span>
                    </div>
                    <div class="info-row">
                        <label>Mode de paiement:</label>
                        <span id="paymentMethod">Virement bancaire</span>
                    </div>
                    <div class="info-row">
                        <label>Période de paie:</label>
                        <span id="workPeriod">22/22 jours</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tableau des Gains et Retenues -->
        <section class="salary-section">
            <h3>DÉTAIL DES GAINS ET RETENUES</h3>
            <table class="salary-table">
                <thead>
                    <tr>
                        <th>DÉSIGNATION</th>
                        <th>BASE/TAUX</th>
                        <th>MONTANT (DZD)</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Gains -->
                    <tr class="section-header">
                        <td colspan="3"><strong>GAINS</strong></td>
                    </tr>
                    <tr>
                        <td>Salaire de base</td>
                        <td id="baseSalaryDays">22/22 jours</td>
                        <td class="amount" id="baseSalary">80,000.00</td>
                    </tr>
                    <tr id="overtimeRow" style="display: none;">
                        <td>Heures supplémentaires</td>
                        <td id="overtimeHours">0h</td>
                        <td class="amount" id="overtimeAmount">0.00</td>
                    </tr>
                    <tr id="transportRow">
                        <td>Indemnité de transport</td>
                        <td>Forfait</td>
                        <td class="amount" id="transportAllowance">3,000.00</td>
                    </tr>
                    <tr id="mealRow">
                        <td>Indemnité de panier</td>
                        <td>Forfait</td>
                        <td class="amount" id="mealAllowance">2,000.00</td>
                    </tr>
                    <tr id="familyRow">
                        <td>Allocation familiale</td>
                        <td>Forfait</td>
                        <td class="amount" id="familyAllowance">1,500.00</td>
                    </tr>
                    <tr id="housingRow" style="display: none;">
                        <td>Indemnité de logement</td>
                        <td>Forfait</td>
                        <td class="amount" id="housingAllowance">0.00</td>
                    </tr>
                    <tr id="seniorityRow" style="display: none;">
                        <td>Prime d'ancienneté</td>
                        <td id="seniorityRate">0%</td>
                        <td class="amount" id="seniorityBonus">0.00</td>
                    </tr>
                    <tr id="performanceRow" style="display: none;">
                        <td>Prime de rendement</td>
                        <td>Variable</td>
                        <td class="amount" id="performanceBonus">0.00</td>
                    </tr>
                    <tr class="total-row gains-total">
                        <td><strong>TOTAL GAINS</strong></td>
                        <td></td>
                        <td class="amount"><strong id="totalGains">86,500.00</strong></td>
                    </tr>
                    
                    <!-- Retenues -->
                    <tr class="section-header">
                        <td colspan="3"><strong>RETENUES</strong></td>
                    </tr>
                    <tr>
                        <td>Cotisation CNAS</td>
                        <td>9%</td>
                        <td class="amount deduction" id="cnasDeduction">-7,785.00</td>
                    </tr>
                    <tr id="irgRow">
                        <td>Impôt sur le Revenu Global (IRG)</td>
                        <td id="irgRate">Progressif</td>
                        <td class="amount deduction" id="irgDeduction">-17,315.00</td>
                    </tr>
                    <tr id="casnos" style="display: none;">
                        <td>CASNOS</td>
                        <td>15%</td>
                        <td class="amount deduction" id="casnosDeduction">0.00</td>
                    </tr>
                    <tr id="advanceRow" style="display: none;">
                        <td>Avance sur salaire</td>
                        <td>-</td>
                        <td class="amount deduction" id="advanceDeduction">0.00</td>
                    </tr>
                    <tr id="loanRow" style="display: none;">
                        <td>Remboursement prêt</td>
                        <td>-</td>
                        <td class="amount deduction" id="loanDeduction">0.00</td>
                    </tr>
                    <tr class="total-row deductions-total">
                        <td><strong>TOTAL RETENUES</strong></td>
                        <td></td>
                        <td class="amount deduction"><strong id="totalDeductions">-25,100.00</strong></td>
                    </tr>
                    
                    <!-- Net à payer -->
                    <tr class="net-pay">
                        <td><strong>NET À PAYER</strong></td>
                        <td></td>
                        <td class="amount"><strong id="netPay">61,400.00</strong></td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- Détail IRG -->
        <section class="irg-detail">
            <h4>Détail du calcul IRG</h4>
            <div class="irg-breakdown">
                <p>Salaire brut: <span id="irgGrossSalary">86,500.00 DZD</span></p>
                <p>Cotisation CNAS: <span id="irgCnas">-7,785.00 DZD</span></p>
                <p>Abattement frais professionnels: <span id="irgAbatement">-1,000.00 DZD</span></p>
                <p><strong>Revenu imposable: <span id="irgTaxableIncome">77,715.00 DZD</span></strong></p>
                <div id="irgTranches" class="irg-tranches">
                    <!-- Les tranches IRG seront générées dynamiquement -->
                </div>
            </div>
        </section>

        <!-- Charges patronales -->
        <section class="employer-charges">
            <h4>Charges patronales</h4>
            <div class="charges-grid">
                <div class="charge-item">
                    <label>CNAS Employeur (25%):</label>
                    <span id="employerCnas">21,625.00 DZD</span>
                </div>
                <div class="charge-item">
                    <label>CACOBATPH (1.5%):</label>
                    <span id="cacobatph">1,298.00 DZD</span>
                </div>
                <div class="charge-item total-charge">
                    <label><strong>Coût total employeur:</strong></label>
                    <span id="totalEmployerCost"><strong>109,423.00 DZD</strong></span>
                </div>
            </div>
        </section>

        <!-- Pied de page -->
        <footer class="payslip-footer">
            <div class="signatures">
                <div class="signature-box">
                    <div class="signature-area"></div>
                    <p>Cachet de l'entreprise</p>
                </div>
                <div class="signature-box">
                    <div class="signature-area"></div>
                    <p>Signature du Directeur</p>
                </div>
                <div class="signature-box">
                    <div class="qr-code" id="qrCode">
                        <div class="qr-placeholder">QR</div>
                        <p id="qrText">EMP-001-01-2025</p>
                    </div>
                    <p>Code de vérification</p>
                </div>
            </div>
            <div class="legal-mentions">
                <p>Ce bulletin de paie est conforme à la législation algérienne en vigueur.</p>
                <p>Document généré le <span id="generationDate">15/01/2025</span> par Forsa Finance</p>
                <p><strong>Net à payer en lettres:</strong> <span id="netInWords">Soixante et un mille quatre cents dinars</span></p>
            </div>
        </footer>
    </div>

    <!-- Boutons d'action (cachés à l'impression) -->
    <div class="action-buttons no-print">
        <button onclick="printPayslip()" class="btn btn-primary">
            <span class="icon">🖨️</span> Imprimer
        </button>
        <button onclick="saveToPDF()" class="btn btn-secondary">
            <span class="icon">📄</span> Sauvegarder PDF
        </button>
        <button onclick="editPayslip()" class="btn btn-tertiary">
            <span class="icon">✏️</span> Modifier
        </button>
    </div>

    <script src="payslip-calculator.js"></script>
    <script src="payslip-generator.js"></script>
</body>
</html>
