{"name": "algerian-payslip-template", "version": "1.0.0", "description": "Modèle de fiche de paie algérienne conforme à la réglementation 2024-2025 - Fonctionnement 100% hors ligne", "main": "index.html", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "serve": "python -m http.server 8080", "test": "echo \"Tests à implémenter\" && exit 0"}, "keywords": ["algerie", "paie", "salaire", "irg", "cnas", "fiche-paie", "bulletin-paie", "hors-ligne", "desktop", "electron"], "author": {"name": "Forsa Finance", "email": "<EMAIL>", "url": "https://www.forsa-finance.dz"}, "license": "MIT", "homepage": "https://github.com/forsa-finance/algerian-payslip", "repository": {"type": "git", "url": "https://github.com/forsa-finance/algerian-payslip.git"}, "bugs": {"url": "https://github.com/forsa-finance/algerian-payslip/issues"}, "devDependencies": {"electron": "^22.0.0", "electron-builder": "^24.0.0"}, "dependencies": {}, "build": {"appId": "dz.forsa-finance.payslip", "productName": "Fiche de Paie Algérienne", "directories": {"output": "dist"}, "files": ["index.html", "styles.css", "payslip-calculator.js", "payslip-generator.js", "examples/electron-integration.js", "assets/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": "AppImage", "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Fiche de Paie Algérienne"}}, "engines": {"node": ">=14.0.0"}}