/**
 * Calculateur de Paie Algérien - Conforme à la réglementation 2024-2025
 * Fonctionnement 100% hors ligne
 */

// Configuration des taux et barèmes algériens 2024-2025
const ALGERIA_TAX_CONFIG = {
    // Salaire minimum national garanti (SNMG) 2024
    MINIMUM_WAGE: 20000,
    
    // Taux de cotisations sociales
    SOCIAL_RATES: {
        CNAS_EMPLOYEE: 0.09,        // 9% - Cotisation employé
        CNAS_EMPLOYER: 0.25,        // 25% - Cotisation employeur
        CACOBATPH: 0.015,           // 1.5% - Caisse accidents du travail
        CASNOS: 0.15                // 15% - Pour non-salariés
    },
    
    // Barème IRG progressif 2024 (en DZD/mois)
    IRG_BRACKETS: [
        { min: 0, max: 15000, rate: 0.00 },        // Exonéré
        { min: 15001, max: 30000, rate: 0.20 },    // 20%
        { min: 30001, max: 120000, rate: 0.30 },   // 30%
        { min: 120001, max: Infinity, rate: 0.35 } // 35%
    ],
    
    // Abattement forfaitaire pour frais professionnels
    PROFESSIONAL_EXPENSES: {
        RATE: 0.10,                 // 10% du salaire
        MAX_MONTHLY: 1000           // Plafonné à 1000 DZD/mois
    },
    
    // Primes d'ancienneté (selon convention collective)
    SENIORITY_RATES: [
        { minYears: 0, maxYears: 2, rate: 0.00 },   // 0-2 ans: 0%
        { minYears: 3, maxYears: 5, rate: 0.05 },   // 3-5 ans: 5%
        { minYears: 6, maxYears: 10, rate: 0.10 },  // 6-10 ans: 10%
        { minYears: 11, maxYears: 15, rate: 0.15 }, // 11-15 ans: 15%
        { minYears: 16, maxYears: 20, rate: 0.20 }, // 16-20 ans: 20%
        { minYears: 21, maxYears: Infinity, rate: 0.25 } // 21+ ans: 25%
    ]
};

/**
 * Classe principale pour les calculs de paie
 */
class AlgerianPayrollCalculator {
    constructor() {
        this.config = ALGERIA_TAX_CONFIG;
    }

    /**
     * Calcule la prime d'ancienneté
     * @param {number} baseSalary - Salaire de base
     * @param {number} yearsOfService - Années de service
     * @returns {number} Montant de la prime d'ancienneté
     */
    calculateSeniorityBonus(baseSalary, yearsOfService) {
        const bracket = this.config.SENIORITY_RATES.find(
            b => yearsOfService >= b.minYears && yearsOfService <= b.maxYears
        );
        return bracket ? Math.round(baseSalary * bracket.rate) : 0;
    }

    /**
     * Calcule les heures supplémentaires avec majorations
     * @param {number} baseSalary - Salaire de base mensuel
     * @param {number} overtimeHours - Nombre d'heures supplémentaires
     * @param {number} standardHours - Heures normales par mois (défaut: 173.33h)
     * @returns {number} Montant des heures supplémentaires
     */
    calculateOvertime(baseSalary, overtimeHours, standardHours = 173.33) {
        if (overtimeHours <= 0) return 0;
        
        const hourlyRate = baseSalary / standardHours;
        let overtimeAmount = 0;
        
        // Majoration 25% pour les 4 premières heures
        const firstTierHours = Math.min(overtimeHours, 4);
        overtimeAmount += firstTierHours * hourlyRate * 1.25;
        
        // Majoration 50% au-delà de 4 heures
        if (overtimeHours > 4) {
            const secondTierHours = overtimeHours - 4;
            overtimeAmount += secondTierHours * hourlyRate * 1.50;
        }
        
        return Math.round(overtimeAmount);
    }

    /**
     * Calcule l'abattement forfaitaire pour frais professionnels
     * @param {number} grossSalary - Salaire brut
     * @returns {number} Montant de l'abattement
     */
    calculateProfessionalExpenses(grossSalary) {
        const calculated = grossSalary * this.config.PROFESSIONAL_EXPENSES.RATE;
        return Math.min(calculated, this.config.PROFESSIONAL_EXPENSES.MAX_MONTHLY);
    }

    /**
     * Calcule l'IRG selon le barème progressif algérien
     * @param {number} grossSalary - Salaire brut
     * @returns {object} Détail du calcul IRG
     */
    calculateIRG(grossSalary) {
        // 1. Calcul des cotisations CNAS
        const cnasEmployee = grossSalary * this.config.SOCIAL_RATES.CNAS_EMPLOYEE;
        
        // 2. Calcul de l'abattement frais professionnels
        const professionalExpenses = this.calculateProfessionalExpenses(grossSalary);
        
        // 3. Revenu imposable
        const taxableIncome = Math.max(0, grossSalary - cnasEmployee - professionalExpenses);
        
        // 4. Calcul progressif de l'IRG
        let irg = 0;
        const tranches = [];
        
        for (const bracket of this.config.IRG_BRACKETS) {
            if (taxableIncome <= bracket.min) break;
            
            const taxableAtThisBracket = Math.min(
                taxableIncome - bracket.min,
                bracket.max === Infinity ? taxableIncome - bracket.min : bracket.max - bracket.min
            );
            
            if (taxableAtThisBracket > 0) {
                const irgAtThisBracket = taxableAtThisBracket * bracket.rate;
                irg += irgAtThisBracket;
                
                tranches.push({
                    tranche: `${bracket.min.toLocaleString()} - ${bracket.max === Infinity ? '∞' : bracket.max.toLocaleString()} DZD`,
                    base: taxableAtThisBracket,
                    rate: bracket.rate * 100,
                    amount: irgAtThisBracket
                });
            }
        }
        
        return {
            grossSalary: grossSalary,
            cnasEmployee: Math.round(cnasEmployee),
            professionalExpenses: Math.round(professionalExpenses),
            taxableIncome: Math.round(taxableIncome),
            tranches: tranches,
            totalIRG: Math.round(irg),
            effectiveRate: taxableIncome > 0 ? (irg / taxableIncome * 100) : 0
        };
    }

    /**
     * Calcule une fiche de paie complète
     * @param {object} employeeData - Données de l'employé
     * @param {object} salaryData - Données salariales
     * @returns {object} Fiche de paie complète
     */
    calculatePayslip(employeeData, salaryData) {
        // Validation des données d'entrée
        if (salaryData.baseSalary < this.config.MINIMUM_WAGE) {
            throw new Error(`Le salaire de base ne peut pas être inférieur au SNMG (${this.config.MINIMUM_WAGE.toLocaleString()} DZD)`);
        }

        // 1. Calcul des gains
        const baseSalary = salaryData.baseSalary || 0;
        const overtimeAmount = this.calculateOvertime(baseSalary, salaryData.overtimeHours || 0);
        const seniorityBonus = this.calculateSeniorityBonus(baseSalary, employeeData.yearsOfService || 0);
        
        const allowances = {
            transport: salaryData.transportAllowance || 0,
            meal: salaryData.mealAllowance || 0,
            housing: salaryData.housingAllowance || 0,
            family: salaryData.familyAllowance || 0,
            performance: salaryData.performanceBonus || 0,
            seniority: seniorityBonus,
            other: salaryData.otherAllowances || 0
        };

        const totalAllowances = Object.values(allowances).reduce((sum, val) => sum + val, 0);
        const grossSalary = baseSalary + overtimeAmount + totalAllowances;

        // 2. Calcul des retenues
        const cnasEmployee = Math.round(grossSalary * this.config.SOCIAL_RATES.CNAS_EMPLOYEE);
        const irgCalculation = this.calculateIRG(grossSalary);
        
        const otherDeductions = {
            advance: salaryData.advanceDeduction || 0,
            loan: salaryData.loanDeduction || 0,
            other: salaryData.otherDeductions || 0
        };

        const totalOtherDeductions = Object.values(otherDeductions).reduce((sum, val) => sum + val, 0);
        const totalDeductions = cnasEmployee + irgCalculation.totalIRG + totalOtherDeductions;

        // 3. Calcul du net à payer
        const netSalary = grossSalary - totalDeductions;

        // 4. Calcul des charges patronales
        const employerCharges = {
            cnas: Math.round(grossSalary * this.config.SOCIAL_RATES.CNAS_EMPLOYER),
            cacobatph: Math.round(grossSalary * this.config.SOCIAL_RATES.CACOBATPH)
        };
        employerCharges.total = employerCharges.cnas + employerCharges.cacobatph;

        const totalEmployerCost = grossSalary + employerCharges.total;

        // 5. Génération du résultat
        return {
            employee: employeeData,
            period: salaryData.period,
            earnings: {
                baseSalary: baseSalary,
                overtime: overtimeAmount,
                allowances: allowances,
                totalAllowances: totalAllowances,
                grossSalary: grossSalary
            },
            deductions: {
                cnas: cnasEmployee,
                irg: irgCalculation,
                other: otherDeductions,
                totalOther: totalOtherDeductions,
                totalDeductions: totalDeductions
            },
            netSalary: netSalary,
            employerCharges: employerCharges,
            totalEmployerCost: totalEmployerCost,
            calculationDate: new Date().toISOString()
        };
    }

    /**
     * Formate un montant en devise algérienne
     * @param {number} amount - Montant à formater
     * @returns {string} Montant formaté
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('fr-DZ', {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount) + ' DZD';
    }

    /**
     * Convertit un nombre en lettres (français)
     * @param {number} amount - Montant à convertir
     * @returns {string} Montant en lettres
     */
    numberToWords(amount) {
        // Implémentation simplifiée - peut être étendue
        const units = ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf'];
        const teens = ['dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'];
        const tens = ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'];
        
        if (amount === 0) return 'zéro dinars';
        if (amount < 0) return 'moins ' + this.numberToWords(-amount);
        
        // Implémentation basique pour les montants courants
        if (amount < 1000) {
            return amount.toString() + ' dinars';
        } else if (amount < 1000000) {
            const thousands = Math.floor(amount / 1000);
            const remainder = amount % 1000;
            let result = thousands + ' mille';
            if (remainder > 0) {
                result += ' ' + remainder;
            }
            return result + ' dinars';
        } else {
            return amount.toLocaleString('fr-DZ') + ' dinars';
        }
    }
}

// Export pour utilisation
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AlgerianPayrollCalculator;
} else {
    window.AlgerianPayrollCalculator = AlgerianPayrollCalculator;
}
