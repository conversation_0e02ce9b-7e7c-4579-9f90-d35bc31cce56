/**
 * Générateur de Fiche de Paie - Interface et Manipulation DOM
 * Fonctionnement 100% hors ligne avec sauvegarde PDF
 */

// Instance globale du calculateur
let payrollCalculator;
let currentPayslipData = null;

// Configuration multilingue
const TRANSLATIONS = {
    fr: {
        companyName: "Nom de l'entreprise",
        employeeName: "Nom et Prénom",
        position: "Fonction",
        baseSalary: "Salaire de base",
        netPay: "Net à payer",
        print: "Imprimer",
        save: "Sauvegarder PDF",
        edit: "Modifier"
    },
    ar: {
        companyName: "اسم الشركة",
        employeeName: "الاسم واللقب",
        position: "المنصب",
        baseSalary: "الراتب الأساسي",
        netPay: "صافي الراتب",
        print: "طباعة",
        save: "حفظ PDF",
        edit: "تعديل"
    },
    en: {
        companyName: "Company Name",
        employeeName: "Full Name",
        position: "Position",
        baseSalary: "Base Salary",
        netPay: "Net Pay",
        print: "Print",
        save: "Save PDF",
        edit: "Edit"
    }
};

/**
 * Initialise l'application
 */
function initializePayslipGenerator() {
    payrollCalculator = new AlgerianPayrollCalculator();
    
    // Charger les données par défaut
    loadDefaultData();
    
    // Générer la fiche de paie par défaut
    generateDefaultPayslip();
    
    console.log('Générateur de fiche de paie initialisé');
}

/**
 * Charge les données par défaut
 */
function loadDefaultData() {
    const defaultEmployee = {
        id: 'EMP-001',
        name: 'BENALI Ahmed',
        position: 'Développeur Senior',
        hireDate: '2020-01-01',
        yearsOfService: 5,
        paymentMethod: 'Virement bancaire'
    };

    const defaultSalary = {
        baseSalary: 80000,
        overtimeHours: 0,
        transportAllowance: 3000,
        mealAllowance: 2000,
        familyAllowance: 1500,
        housingAllowance: 0,
        performanceBonus: 0,
        otherAllowances: 0,
        advanceDeduction: 0,
        loanDeduction: 0,
        otherDeductions: 0,
        period: {
            month: new Date().getMonth() + 1,
            year: new Date().getFullYear(),
            workingDays: 22,
            workedDays: 22
        }
    };

    const defaultCompany = {
        name: 'ENTREPRISE DEMO SARL',
        address: '123 Rue de l\'Indépendance, Alger 16000, Algérie',
        nif: '123456789012345',
        rc: '16/00-1234567',
        phone: '+213 21 XX XX XX',
        email: '<EMAIL>'
    };

    // Stocker en localStorage pour persistance
    localStorage.setItem('defaultEmployee', JSON.stringify(defaultEmployee));
    localStorage.setItem('defaultSalary', JSON.stringify(defaultSalary));
    localStorage.setItem('defaultCompany', JSON.stringify(defaultCompany));
}

/**
 * Génère une fiche de paie par défaut
 */
function generateDefaultPayslip() {
    const employee = JSON.parse(localStorage.getItem('defaultEmployee'));
    const salary = JSON.parse(localStorage.getItem('defaultSalary'));
    const company = JSON.parse(localStorage.getItem('defaultCompany'));

    try {
        currentPayslipData = payrollCalculator.calculatePayslip(employee, salary);
        updatePayslipDisplay(currentPayslipData, company);
    } catch (error) {
        console.error('Erreur lors du calcul de la fiche de paie:', error);
        alert('Erreur: ' + error.message);
    }
}

/**
 * Met à jour l'affichage de la fiche de paie
 * @param {object} payslipData - Données calculées de la fiche de paie
 * @param {object} companyData - Données de l'entreprise
 */
function updatePayslipDisplay(payslipData, companyData) {
    // Mise à jour des informations entreprise
    updateElement('companyName', companyData.name);
    updateElement('companyAddress', companyData.address);
    updateElement('companyNIF', companyData.nif);
    updateElement('companyRC', companyData.rc);

    // Mise à jour des informations employé
    updateElement('employeeName', payslipData.employee.name);
    updateElement('employeeId', payslipData.employee.id);
    updateElement('employeePosition', payslipData.employee.position);
    updateElement('hireDate', formatDate(payslipData.employee.hireDate));
    updateElement('paymentMethod', payslipData.employee.paymentMethod);

    // Mise à jour de la période
    const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
                       'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const periodText = `${monthNames[payslipData.period.month - 1]} ${payslipData.period.year}`;
    updateElement('payPeriod', periodText);
    updateElement('workPeriod', `${payslipData.period.workedDays}/${payslipData.period.workingDays} jours`);

    // Numéro de fiche de paie
    const payslipNumber = `${payslipData.period.year}-${payslipData.period.month.toString().padStart(2, '0')}-${payslipData.employee.id}`;
    updateElement('payslipNumber', payslipNumber);

    // Mise à jour des montants
    updateElement('baseSalary', formatAmount(payslipData.earnings.baseSalary));
    updateElement('baseSalaryDays', `${payslipData.period.workedDays}/${payslipData.period.workingDays} jours`);

    // Heures supplémentaires
    if (payslipData.earnings.overtime > 0) {
        showElement('overtimeRow');
        updateElement('overtimeAmount', formatAmount(payslipData.earnings.overtime));
    } else {
        hideElement('overtimeRow');
    }

    // Indemnités
    updateElement('transportAllowance', formatAmount(payslipData.earnings.allowances.transport));
    updateElement('mealAllowance', formatAmount(payslipData.earnings.allowances.meal));
    updateElement('familyAllowance', formatAmount(payslipData.earnings.allowances.family));

    if (payslipData.earnings.allowances.housing > 0) {
        showElement('housingRow');
        updateElement('housingAllowance', formatAmount(payslipData.earnings.allowances.housing));
    } else {
        hideElement('housingRow');
    }

    if (payslipData.earnings.allowances.seniority > 0) {
        showElement('seniorityRow');
        updateElement('seniorityBonus', formatAmount(payslipData.earnings.allowances.seniority));
        const seniorityRate = ((payslipData.earnings.allowances.seniority / payslipData.earnings.baseSalary) * 100).toFixed(0);
        updateElement('seniorityRate', seniorityRate + '%');
    } else {
        hideElement('seniorityRow');
    }

    if (payslipData.earnings.allowances.performance > 0) {
        showElement('performanceRow');
        updateElement('performanceBonus', formatAmount(payslipData.earnings.allowances.performance));
    } else {
        hideElement('performanceRow');
    }

    // Total gains
    updateElement('totalGains', formatAmount(payslipData.earnings.grossSalary));

    // Retenues
    updateElement('cnasDeduction', '-' + formatAmount(payslipData.deductions.cnas));
    updateElement('irgDeduction', '-' + formatAmount(payslipData.deductions.irg.totalIRG));

    // Autres retenues
    if (payslipData.deductions.other.advance > 0) {
        showElement('advanceRow');
        updateElement('advanceDeduction', '-' + formatAmount(payslipData.deductions.other.advance));
    } else {
        hideElement('advanceRow');
    }

    if (payslipData.deductions.other.loan > 0) {
        showElement('loanRow');
        updateElement('loanDeduction', '-' + formatAmount(payslipData.deductions.other.loan));
    } else {
        hideElement('loanRow');
    }

    // Total retenues
    updateElement('totalDeductions', '-' + formatAmount(payslipData.deductions.totalDeductions));

    // Net à payer
    updateElement('netPay', formatAmount(payslipData.netSalary));

    // Détail IRG
    updateIRGDetail(payslipData.deductions.irg);

    // Charges patronales
    updateElement('employerCnas', formatAmount(payslipData.employerCharges.cnas) + ' DZD');
    updateElement('cacobatph', formatAmount(payslipData.employerCharges.cacobatph) + ' DZD');
    updateElement('totalEmployerCost', formatAmount(payslipData.totalEmployerCost) + ' DZD');

    // Code QR et date
    updateElement('qrText', payslipNumber);
    updateElement('generationDate', formatDate(new Date()));

    // Net en lettres
    updateElement('netInWords', payrollCalculator.numberToWords(payslipData.netSalary));
}

/**
 * Met à jour le détail du calcul IRG
 * @param {object} irgData - Données du calcul IRG
 */
function updateIRGDetail(irgData) {
    updateElement('irgGrossSalary', formatAmount(irgData.grossSalary) + ' DZD');
    updateElement('irgCnas', '-' + formatAmount(irgData.cnasEmployee) + ' DZD');
    updateElement('irgAbatement', '-' + formatAmount(irgData.professionalExpenses) + ' DZD');
    updateElement('irgTaxableIncome', formatAmount(irgData.taxableIncome) + ' DZD');

    // Affichage des tranches IRG
    const tranchesContainer = document.getElementById('irgTranches');
    if (tranchesContainer && irgData.tranches.length > 0) {
        tranchesContainer.innerHTML = '<h5>Tranches IRG appliquées:</h5>';
        irgData.tranches.forEach(tranche => {
            const trancheDiv = document.createElement('div');
            trancheDiv.className = 'irg-tranche';
            trancheDiv.innerHTML = `
                <span>${tranche.tranche} (${tranche.rate}%)</span>
                <span>${formatAmount(tranche.amount)} DZD</span>
            `;
            tranchesContainer.appendChild(trancheDiv);
        });
    }
}

/**
 * Fonctions utilitaires
 */
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function showElement(id) {
    const element = document.getElementById(id);
    if (element) {
        element.style.display = '';
    }
}

function hideElement(id) {
    const element = document.getElementById(id);
    if (element) {
        element.style.display = 'none';
    }
}

function formatAmount(amount) {
    return new Intl.NumberFormat('fr-DZ', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (typeof date === 'string') {
        date = new Date(date);
    }
    return date.toLocaleDateString('fr-DZ');
}

/**
 * Fonctions d'action
 */
function printPayslip() {
    // Masquer les boutons d'action
    const actionButtons = document.querySelector('.action-buttons');
    if (actionButtons) {
        actionButtons.style.display = 'none';
    }

    // Imprimer
    window.print();

    // Restaurer les boutons après impression
    setTimeout(() => {
        if (actionButtons) {
            actionButtons.style.display = 'flex';
        }
    }, 1000);
}

function saveToPDF() {
    // Utilisation de l'API Print to PDF du navigateur
    if (window.chrome && window.chrome.webstore) {
        // Chrome - utiliser l'impression vers PDF
        printPayslip();
    } else {
        // Autres navigateurs - fallback
        alert('Pour sauvegarder en PDF, utilisez Ctrl+P puis sélectionnez "Enregistrer au format PDF"');
        printPayslip();
    }
}

function editPayslip() {
    // Ouvrir un modal d'édition (à implémenter selon les besoins)
    alert('Fonction d\'édition à implémenter selon vos besoins spécifiques');
}

/**
 * Gestion multilingue
 */
function changeLanguage(lang) {
    document.documentElement.setAttribute('data-lang', lang);
    
    // Mettre à jour les textes selon la langue
    const translations = TRANSLATIONS[lang] || TRANSLATIONS.fr;
    
    // Ici vous pouvez ajouter la logique de traduction
    // des éléments de l'interface selon vos besoins
}

/**
 * Initialisation au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    initializePayslipGenerator();
});

// Export des fonctions pour utilisation globale
window.printPayslip = printPayslip;
window.saveToPDF = saveToPDF;
window.editPayslip = editPayslip;
window.changeLanguage = changeLanguage;
