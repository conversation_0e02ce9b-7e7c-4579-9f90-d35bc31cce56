/* Styles CSS pour Fiche de Paie Algérienne - Version Hors Ligne */

/* Variables CSS pour personnalisation facile */
:root {
    --primary-color: #1e40af;
    --secondary-color: #3b82f6;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --font-family: 'Arial', 'Helvetica', sans-serif;
    --border-radius: 8px;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Reset et base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-100);
    padding: 20px;
}

/* Container principal */
.payslip-container {
    max-width: 210mm; /* Format A4 */
    margin: 0 auto;
    background: white;
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* En-tête entreprise */
.company-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.company-info {
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.company-logo img {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
}

.company-details h1 {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
}

.company-details p {
    font-size: 14px;
    margin-bottom: 8px;
    opacity: 0.9;
}

.company-ids {
    display: flex;
    gap: 20px;
    font-size: 12px;
    opacity: 0.8;
}

.payslip-title {
    text-align: right;
}

.payslip-title h2 {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 8px;
}

.payslip-title .period {
    font-size: 16px;
    margin-bottom: 4px;
}

.payslip-title .payslip-number {
    font-size: 12px;
    opacity: 0.8;
}

/* Sections */
section {
    padding: 25px 30px;
    border-bottom: 1px solid var(--gray-200);
}

section h3 {
    color: var(--primary-color);
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--primary-color);
}

section h4 {
    color: var(--gray-800);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
}

/* Informations employé */
.employee-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px dotted var(--gray-300);
}

.info-row label {
    font-weight: 600;
    color: var(--gray-600);
    min-width: 140px;
}

.info-row span {
    font-weight: 500;
    color: var(--gray-900);
}

/* Tableau des salaires */
.salary-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 13px;
}

.salary-table th {
    background: var(--gray-100);
    color: var(--gray-800);
    font-weight: bold;
    padding: 12px 8px;
    text-align: left;
    border: 1px solid var(--gray-300);
}

.salary-table td {
    padding: 10px 8px;
    border: 1px solid var(--gray-300);
    vertical-align: middle;
}

.salary-table .section-header td {
    background: var(--gray-100);
    font-weight: bold;
    color: var(--primary-color);
    padding: 8px;
}

.salary-table .amount {
    text-align: right;
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.salary-table .deduction {
    color: var(--danger-color);
}

.salary-table .total-row {
    background: var(--gray-50);
    font-weight: bold;
}

.salary-table .gains-total {
    background: #dbeafe;
    color: var(--primary-color);
}

.salary-table .deductions-total {
    background: #fee2e2;
    color: var(--danger-color);
}

.salary-table .net-pay {
    background: #d1fae5;
    color: var(--success-color);
    font-size: 16px;
    font-weight: bold;
}

/* Détail IRG */
.irg-detail {
    background: #eff6ff;
    border-left: 4px solid var(--primary-color);
}

.irg-breakdown p {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    font-size: 13px;
}

.irg-tranches {
    margin-top: 15px;
    padding: 15px;
    background: white;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.irg-tranche {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 12px;
    padding: 4px 0;
    border-bottom: 1px dotted var(--gray-300);
}

/* Charges patronales */
.charges-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.charge-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.charge-item.total-charge {
    grid-column: 1 / -1;
    background: #dbeafe;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Pied de page */
.payslip-footer {
    padding: 30px;
    background: var(--gray-50);
}

.signatures {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.signature-box {
    text-align: center;
}

.signature-area {
    height: 80px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: 12px;
}

.qr-code {
    height: 80px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
}

.qr-placeholder {
    width: 40px;
    height: 40px;
    background: var(--gray-200);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    margin-bottom: 5px;
}

.signature-box p {
    font-size: 11px;
    color: var(--gray-600);
    font-weight: 500;
}

.legal-mentions {
    text-align: center;
    font-size: 11px;
    color: var(--gray-600);
    line-height: 1.5;
}

.legal-mentions p {
    margin-bottom: 5px;
}

/* Boutons d'action */
.action-buttons {
    max-width: 210mm;
    margin: 20px auto;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1e3a8a;
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--success-color);
    color: white;
}

.btn-secondary:hover {
    background: #047857;
    transform: translateY(-1px);
}

.btn-tertiary {
    background: var(--gray-200);
    color: var(--gray-800);
}

.btn-tertiary:hover {
    background: var(--gray-300);
    transform: translateY(-1px);
}

/* Styles d'impression */
@media print {
    body {
        background: white;
        padding: 0;
        margin: 0;
    }
    
    .payslip-container {
        box-shadow: none;
        border-radius: 0;
        max-width: none;
        margin: 0;
    }
    
    .no-print {
        display: none !important;
    }
    
    .company-header {
        background: var(--primary-color) !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    .salary-table .gains-total,
    .salary-table .deductions-total,
    .salary-table .net-pay {
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
    
    section {
        page-break-inside: avoid;
    }
    
    .payslip-footer {
        page-break-inside: avoid;
    }
}

/* Responsive */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .company-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .employee-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .charges-grid {
        grid-template-columns: 1fr;
    }
    
    .signatures {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .salary-table {
        font-size: 11px;
    }
    
    .salary-table th,
    .salary-table td {
        padding: 6px 4px;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.payslip-container {
    animation: fadeIn 0.5s ease-out;
}

/* Utilitaires */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.font-bold {
    font-weight: bold;
}

.text-primary {
    color: var(--primary-color);
}

.text-success {
    color: var(--success-color);
}

.text-danger {
    color: var(--danger-color);
}

/* Styles pour multilingue */
[data-lang="ar"] {
    direction: rtl;
    text-align: right;
}

[data-lang="ar"] .company-header {
    flex-direction: row-reverse;
}

[data-lang="ar"] .payslip-title {
    text-align: left;
}

[data-lang="ar"] .employee-grid {
    direction: rtl;
}

[data-lang="ar"] .info-row {
    flex-direction: row-reverse;
}

[data-lang="ar"] .salary-table th,
[data-lang="ar"] .salary-table td {
    text-align: right;
}

[data-lang="ar"] .amount {
    text-align: left !important;
}
