<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Calculs - Fiche de Paie Algérienne</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .test-success {
            background: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .test-info {
            background: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .amount {
            text-align: right;
            font-family: 'Courier New', monospace;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .scenario {
            border: 1px solid #ddd;
            margin: 20px 0;
            border-radius: 5px;
        }
        .scenario-header {
            background: #f8f9fa;
            padding: 15px;
            font-weight: bold;
            border-bottom: 1px solid #ddd;
        }
        .scenario-content {
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧮 Test des Calculs - Fiche de Paie Algérienne</h1>
        <p>Validation des calculs conformes à la réglementation algérienne 2024-2025</p>
        
        <div class="test-result test-info">
            <strong>Configuration des tests:</strong><br>
            - Barème IRG 2024-2025<br>
            - CNAS Employé: 9% | CNAS Employeur: 25% | CACOBATPH: 1.5%<br>
            - Abattement frais professionnels: 10% (max 1000 DZD/mois)<br>
            - SNMG: 20 000 DZD
        </div>

        <button class="btn" onclick="runAllTests()">🚀 Lancer tous les tests</button>
        <button class="btn" onclick="clearResults()">🗑️ Effacer les résultats</button>

        <div id="testResults"></div>
    </div>

    <div class="test-container">
        <h2>📊 Scénarios de Test</h2>
        
        <div class="scenario">
            <div class="scenario-header">Scénario 1: Salaire SNMG (20 000 DZD)</div>
            <div class="scenario-content">
                <button class="btn" onclick="testSNMG()">Tester</button>
                <div id="snmg-result"></div>
            </div>
        </div>

        <div class="scenario">
            <div class="scenario-header">Scénario 2: Salaire Moyen (80 000 DZD + Indemnités)</div>
            <div class="scenario-content">
                <button class="btn" onclick="testMediumSalary()">Tester</button>
                <div id="medium-result"></div>
            </div>
        </div>

        <div class="scenario">
            <div class="scenario-header">Scénario 3: Salaire Élevé (150 000 DZD + Primes)</div>
            <div class="scenario-content">
                <button class="btn" onclick="testHighSalary()">Tester</button>
                <div id="high-result"></div>
            </div>
        </div>

        <div class="scenario">
            <div class="scenario-header">Scénario 4: Avec Heures Supplémentaires</div>
            <div class="scenario-content">
                <button class="btn" onclick="testOvertime()">Tester</button>
                <div id="overtime-result"></div>
            </div>
        </div>
    </div>

    <script src="payslip-calculator.js"></script>
    <script>
        let calculator;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            calculator = new AlgerianPayrollCalculator();
            console.log('Calculateur initialisé');
        });

        function runAllTests() {
            clearResults();
            testSNMG();
            testMediumSalary();
            testHighSalary();
            testOvertime();
            testIRGCalculations();
            testValidations();
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('snmg-result').innerHTML = '';
            document.getElementById('medium-result').innerHTML = '';
            document.getElementById('high-result').innerHTML = '';
            document.getElementById('overtime-result').innerHTML = '';
        }

        function testSNMG() {
            const employee = {
                id: 'TEST-001',
                name: 'EMPLOYÉ TEST SNMG',
                position: 'Employé Junior',
                yearsOfService: 1
            };

            const salary = {
                baseSalary: 20000,
                transportAllowance: 1000,
                mealAllowance: 500,
                familyAllowance: 0,
                period: { month: 1, year: 2025, workingDays: 22, workedDays: 22 }
            };

            try {
                const result = calculator.calculatePayslip(employee, salary);
                displayResult('snmg-result', result, 'Calcul SNMG réussi');
                
                // Vérifications spécifiques
                const expectedGross = 21500; // 20000 + 1000 + 500
                const expectedCNAS = Math.round(expectedGross * 0.09);
                const expectedNet = expectedGross - expectedCNAS; // Pas d'IRG sur SNMG

                if (Math.abs(result.earnings.grossSalary - expectedGross) < 1) {
                    addTestResult('✅ Salaire brut SNMG correct: ' + formatAmount(result.earnings.grossSalary) + ' DZD');
                } else {
                    addTestResult('❌ Erreur salaire brut SNMG: attendu ' + expectedGross + ', obtenu ' + result.earnings.grossSalary);
                }

            } catch (error) {
                displayError('snmg-result', error.message);
            }
        }

        function testMediumSalary() {
            const employee = {
                id: 'TEST-002',
                name: 'EMPLOYÉ TEST MOYEN',
                position: 'Technicien',
                yearsOfService: 5
            };

            const salary = {
                baseSalary: 80000,
                transportAllowance: 3000,
                mealAllowance: 2000,
                familyAllowance: 1500,
                housingAllowance: 0,
                period: { month: 1, year: 2025, workingDays: 22, workedDays: 22 }
            };

            try {
                const result = calculator.calculatePayslip(employee, salary);
                displayResult('medium-result', result, 'Calcul salaire moyen réussi');

                // Vérifications IRG
                const irgDetail = result.deductions.irg;
                if (irgDetail.totalIRG > 0) {
                    addTestResult('✅ IRG calculé: ' + formatAmount(irgDetail.totalIRG) + ' DZD (taux effectif: ' + irgDetail.effectiveRate.toFixed(2) + '%)');
                } else {
                    addTestResult('⚠️ Aucun IRG calculé pour ce salaire');
                }

            } catch (error) {
                displayError('medium-result', error.message);
            }
        }

        function testHighSalary() {
            const employee = {
                id: 'TEST-003',
                name: 'EMPLOYÉ TEST ÉLEVÉ',
                position: 'Directeur',
                yearsOfService: 15
            };

            const salary = {
                baseSalary: 150000,
                transportAllowance: 5000,
                mealAllowance: 3000,
                familyAllowance: 2000,
                performanceBonus: 20000,
                period: { month: 1, year: 2025, workingDays: 22, workedDays: 22 }
            };

            try {
                const result = calculator.calculatePayslip(employee, salary);
                displayResult('high-result', result, 'Calcul salaire élevé réussi');

                // Vérifier la prime d'ancienneté
                if (result.earnings.allowances.seniority > 0) {
                    addTestResult('✅ Prime d\'ancienneté (15 ans): ' + formatAmount(result.earnings.allowances.seniority) + ' DZD');
                }

                // Vérifier que l'IRG est dans la tranche maximale
                const irgDetail = result.deductions.irg;
                if (irgDetail.effectiveRate > 25) {
                    addTestResult('✅ IRG tranche élevée appliquée: ' + irgDetail.effectiveRate.toFixed(2) + '%');
                }

            } catch (error) {
                displayError('high-result', error.message);
            }
        }

        function testOvertime() {
            const employee = {
                id: 'TEST-004',
                name: 'EMPLOYÉ TEST HEURES SUP',
                position: 'Ouvrier',
                yearsOfService: 3
            };

            const salary = {
                baseSalary: 50000,
                overtimeHours: 10, // 10 heures supplémentaires
                transportAllowance: 2000,
                mealAllowance: 1500,
                period: { month: 1, year: 2025, workingDays: 22, workedDays: 22 }
            };

            try {
                const result = calculator.calculatePayslip(employee, salary);
                displayResult('overtime-result', result, 'Calcul avec heures supplémentaires réussi');

                // Vérifier le calcul des heures supplémentaires
                if (result.earnings.overtime > 0) {
                    const hourlyRate = salary.baseSalary / 173.33; // Heures standard par mois
                    const expectedOvertime = (4 * hourlyRate * 1.25) + (6 * hourlyRate * 1.50); // 4h à 125%, 6h à 150%
                    
                    if (Math.abs(result.earnings.overtime - expectedOvertime) < 10) {
                        addTestResult('✅ Heures supplémentaires correctes: ' + formatAmount(result.earnings.overtime) + ' DZD');
                    } else {
                        addTestResult('⚠️ Heures supplémentaires: attendu ~' + Math.round(expectedOvertime) + ', obtenu ' + result.earnings.overtime);
                    }
                }

            } catch (error) {
                displayError('overtime-result', error.message);
            }
        }

        function testIRGCalculations() {
            // Test spécifique des tranches IRG
            const testCases = [
                { gross: 15000, expectedIRG: 0 },
                { gross: 25000, expectedIRG: 0 }, // Après CNAS et abattement
                { gross: 50000, expectedIRG: 0 }, // Vérifier le calcul
                { gross: 100000, expectedIRG: 0 } // À calculer selon le barème
            ];

            testCases.forEach(testCase => {
                const irgResult = calculator.calculateIRG(testCase.gross);
                addTestResult(`IRG pour ${formatAmount(testCase.gross)} DZD: ${formatAmount(irgResult.totalIRG)} DZD (revenu imposable: ${formatAmount(irgResult.taxableIncome)} DZD)`);
            });
        }

        function testValidations() {
            // Test des validations
            try {
                const invalidEmployee = { id: 'TEST', name: 'Test', yearsOfService: 0 };
                const invalidSalary = { baseSalary: 15000, period: { month: 1, year: 2025 } }; // Sous le SNMG
                
                calculator.calculatePayslip(invalidEmployee, invalidSalary);
                addTestResult('❌ Validation SNMG échouée - devrait rejeter les salaires < 20000 DZD');
            } catch (error) {
                addTestResult('✅ Validation SNMG réussie: ' + error.message);
            }
        }

        function displayResult(containerId, result, title) {
            const container = document.getElementById(containerId);
            container.innerHTML = `
                <div class="test-result test-success">
                    <strong>${title}</strong>
                    <table>
                        <tr><th>Élément</th><th>Montant</th></tr>
                        <tr><td>Salaire brut</td><td class="amount">${formatAmount(result.earnings.grossSalary)} DZD</td></tr>
                        <tr><td>CNAS (9%)</td><td class="amount">${formatAmount(result.deductions.cnas)} DZD</td></tr>
                        <tr><td>IRG</td><td class="amount">${formatAmount(result.deductions.irg.totalIRG)} DZD</td></tr>
                        <tr><td>Total retenues</td><td class="amount">${formatAmount(result.deductions.totalDeductions)} DZD</td></tr>
                        <tr><td><strong>Net à payer</strong></td><td class="amount"><strong>${formatAmount(result.netSalary)} DZD</strong></td></tr>
                        <tr><td>Coût employeur</td><td class="amount">${formatAmount(result.totalEmployerCost)} DZD</td></tr>
                    </table>
                </div>
            `;
        }

        function displayError(containerId, message) {
            const container = document.getElementById(containerId);
            container.innerHTML = `<div class="test-result test-error"><strong>Erreur:</strong> ${message}</div>`;
        }

        function addTestResult(message) {
            const resultsContainer = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-result ' + (message.includes('✅') ? 'test-success' : message.includes('❌') ? 'test-error' : 'test-info');
            resultDiv.innerHTML = message;
            resultsContainer.appendChild(resultDiv);
        }

        function formatAmount(amount) {
            return new Intl.NumberFormat('fr-DZ', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(amount);
        }
    </script>
</body>
</html>
